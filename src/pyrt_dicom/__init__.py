"""
pyrt-dicom: Python library for creating radiotherapy DICOM files.

A focused library for creating CT, RTSTRUCT, RTDOSE, and RTPLAN files from
common data sources, filling the critical gap in the Python RT ecosystem.
"""

__author__ = """<PERSON>"""
__email__ = '<EMAIL>'
__version__ = '0.1.0'

# Import core utilities
from .utils import (
    PyrtDicomError,
    DicomCreationError,
    ValidationError,
    CoordinateSystemError,
    UIDGenerationError,
    TemplateError,
    get_clinical_logger,
    log_dicom_creation,
    log_validation_result,
)

# UID generation system
from .uid_generation import (
    UIDGenerator,
    HashBasedUIDGenerator,
    RandomUIDGenerator,
    DefaultUIDGenerator,
    UIDRegistry,
)

# Core classes will be imported as they are implemented
from .core import BaseDicomCreator, CTSeries
# from .core import RTStructureSet, RTDose, RTPlan

__all__ = [
    # Metadata
    '__version__',
    '__author__', 
    '__email__',
    
    # Utilities
    'PyrtDicomError',
    'DicomCreationError', 
    'ValidationError',
    'CoordinateSystemError',
    'UIDGenerationError',
    'TemplateError',
    'get_clinical_logger',
    'log_dicom_creation',
    'log_validation_result',
    
    # UID Generation
    'UIDGenerator',
    'HashBasedUIDGenerator',
    'RandomUIDGenerator',
    'DefaultUIDGenerator',
    'UIDRegistry',
    
    # Core classes (to be added as implemented)
    'BaseDicomCreator',
    'CTSeries',
    # 'RTStructureSet', 
    # 'RTDose',
    # 'RTPlan',
]
