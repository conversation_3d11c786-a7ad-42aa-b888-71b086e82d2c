"""
Entry point for running pyrt-dicom as a module.

This module allows pyrt-dicom to be executed as a module using:
    python -m pyrt_dicom

It provides access to the command-line interface for DICOM RT file
creation and manipulation tasks.

## Usage

```bash
# Run pyrt-dicom CLI
python -m pyrt_dicom

# Show version information  
python -m pyrt_dicom info --version

# Get help for available commands
python -m pyrt_dicom --help
```

## Clinical Applications

The module entry point is designed for clinical physics workflows where
pyrt-dicom needs to be run from environments where direct script execution
may not be available or preferred.

Examples:
- Hospital computing environments with restricted execution policies
- Containerized deployments in clinical systems
- Integration with workflow management systems
- Batch processing scripts that invoke Python modules
"""

from .cli import app

if __name__ == "__main__":
    app()
