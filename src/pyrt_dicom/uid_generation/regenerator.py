# src/pyrt_dicom/uid_generation/regenerator.py

from enum import Enum, auto, Flag
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set, Type, TypeVar, Union
import copy
import re
import pydicom
from pydicom.dataset import Dataset

from .generators import UIDGenerator, DefaultUIDGenerator
from .registry import UIDRegistry
from ..utils.exceptions import UIDGenerationError

class UIDCategory(Flag):
    """Categories of DICOM UIDs that can be regenerated."""
    STUDY = auto()               # StudyInstanceUID
    SERIES = auto()             # SeriesInstanceUID
    INSTANCE = auto()           # SOPInstanceUID
    FRAME_OF_REF = auto()       # FrameOfReferenceUID
    MEDIA_STORAGE = auto()      # MediaStorageSOPInstanceUID
    REFERENCED = auto()         # Any referenced UIDs in sequences
    ALL = (STUDY | SERIES | INSTANCE | 
           FRAME_OF_REF | MEDIA_STORAGE | REFERENCED)

    @classmethod
    def from_string(cls, uid_type: str) -> 'UIDCategory':
        """Convert string representation to UIDCategory."""
        uid_type = uid_type.upper()
        try:
            return cls[uid_type]
        except KeyError:
            raise ValueError(f"Unknown UID type: {uid_type}")

def _validate_uid(uid: str) -> bool:
    """
    Validate that a UID follows DICOM format.
    
    DICOM UIDs must:
    - Start with a digit
    - Contain only digits and periods
    - Have max length of 64 characters
    - Not end with a period
    - Not have consecutive periods
    """
    if not uid:
        return False
        
    # Check length
    if len(uid) > 64:
        return False
        
    # Check pattern: must start with digit, contain only digits and periods
    if not re.match(r'^\d+(\.\d+)*$', uid):
        return False
        
    # Check for consecutive periods (already covered by regex, but explicit)
    if '..' in uid:
        return False
        
    return True

def _validate_uid_mapping(uid_mapping: Dict[str, str]) -> None:
    """Validate all UIDs in a mapping dictionary."""
    for old_uid, new_uid in uid_mapping.items():
        if not _validate_uid(old_uid):
            raise ValueError(f"Invalid source UID format: {old_uid}")
        if not _validate_uid(new_uid):
            raise ValueError(f"Invalid target UID format: {new_uid}")

@dataclass
class UIDRegenerationConfig:
    """Configuration for UID regeneration."""
    # Which categories of UIDs to regenerate
    regenerate: UIDCategory = UIDCategory.ALL
    
    # Whether to maintain relationships between UIDs
    maintain_relationships: bool = True
    
    # Optional mapping of old UIDs to new UIDs for consistency
    uid_mapping: Dict[str, str] = field(default_factory=dict)
    
    # Optional generator for creating new UIDs
    generator: Optional[UIDGenerator] = None
    
    def __post_init__(self):
        if self.generator is None:
            self.generator = DefaultUIDGenerator.create_default_generator()
        
        # Validate UID mappings
        if self.uid_mapping:
            _validate_uid_mapping(self.uid_mapping)

class DICOMUIDRegenerator:
    """
    Regenerates DICOM UIDs while maintaining relationships across multiple files.
    
    This class provides a comprehensive solution for regenerating UIDs in DICOM files
    while preserving the relationships between different DICOM objects.
    
    Example:
        >>> regenerator = DICOMUIDRegenerator()
        >>> config = UIDRegenerationConfig(regenerate=UIDCategory.STUDY | UIDCategory.SERIES)
        >>> datasets = [pydicom.dcmread(f) for f in dicom_files]
        >>> regenerated = regenerator.regenerate_datasets(datasets, config)
        >>> for ds, new_ds in zip(datasets, regenerated):
        ...     new_ds.save_as(f"new_{ds.SOPInstanceUID}.dcm")
    """
    
    def __init__(self, config: Optional[UIDRegenerationConfig] = None):
        """
        Initialize the DICOM UID regenerator.
        
        Args:
            config: Optional configuration for UID regeneration. If None, uses defaults.
        """
        self.config = config or UIDRegenerationConfig()
        self._uid_registry = UIDRegistry()
        
    def regenerate_datasets(
        self,
        datasets: List[Dataset],
        config: Optional[UIDRegenerationConfig] = None
    ) -> List[Dataset]:
        """
        Regenerate UIDs in multiple DICOM datasets.
        
        Args:
            datasets: List of DICOM datasets to process
            config: Optional configuration to override the instance config
            
        Returns:
            List of new DICOM datasets with regenerated UIDs
        """
        config = config or self.config
        results = []
        
        if config.maintain_relationships:
            # When maintaining relationships, use shared mapping across all datasets
            uid_mapping = config.uid_mapping.copy()
            
            # First pass: collect all UIDs and their relationships
            for ds in datasets:
                self._collect_uids(ds, config, uid_mapping)
                
            # Second pass: regenerate UIDs
            for ds in datasets:
                new_ds = self._regenerate_dataset(ds, config, uid_mapping)
                results.append(new_ds)
        else:
            # When not maintaining relationships, process each dataset independently
            # to ensure unique UIDs even for identical source UIDs
            for i, ds in enumerate(datasets):
                # Create independent mapping for each dataset
                dataset_uid_mapping = config.uid_mapping.copy()
                
                # Add dataset index as prefix to ensure uniqueness
                dataset_prefix = f"_ds{i}_"
                
                # Collect UIDs for this specific dataset
                self._collect_uids(ds, config, dataset_uid_mapping, dataset_prefix)
                
                # Regenerate UIDs for this dataset
                new_ds = self._regenerate_dataset(ds, config, dataset_uid_mapping)
                results.append(new_ds)
            
        return results
    
    def _collect_uids(
        self,
        dataset: Dataset,
        config: UIDRegenerationConfig,
        uid_mapping: Dict[str, str],
        dataset_prefix: str = ""
    ) -> None:
        """Collect UIDs from a dataset and update the registry."""
        # Process main UID types using helper method
        self._collect_uid_by_type(
            dataset, config, uid_mapping, dataset_prefix,
            UIDCategory.STUDY, 'StudyInstanceUID', 
            config.generator.generate_study_instance_uid,
            'get_study_uid'
        )
        
        self._collect_uid_by_type(
            dataset, config, uid_mapping, dataset_prefix,
            UIDCategory.SERIES, 'SeriesInstanceUID',
            config.generator.generate_series_instance_uid,
            'get_series_uid', 'StudyInstanceUID'
        )
        
        self._collect_uid_by_type(
            dataset, config, uid_mapping, dataset_prefix,
            UIDCategory.INSTANCE, 'SOPInstanceUID',
            config.generator.generate_sop_instance_uid,
            'get_instance_uid', 'SeriesInstanceUID'
        )
        
        self._collect_uid_by_type(
            dataset, config, uid_mapping, dataset_prefix,
            UIDCategory.FRAME_OF_REF, 'FrameOfReferenceUID',
            config.generator.generate_frame_of_reference_uid
        )
        
        # Process referenced UIDs in sequences if needed
        if config.regenerate & UIDCategory.REFERENCED:
            self._collect_referenced_uids(dataset, config, uid_mapping, dataset_prefix)
    
    def _collect_uid_by_type(
        self,
        dataset: Dataset,
        config: UIDRegenerationConfig,
        uid_mapping: Dict[str, str],
        dataset_prefix: str,
        uid_category: UIDCategory,
        uid_attribute: str,
        generator_method,
        registry_method: Optional[str] = None,
        parent_uid_attribute: Optional[str] = None
    ) -> None:
        """Helper method to collect a specific type of UID."""
        # Check if we should process this UID type
        if not (config.regenerate & uid_category):
            return
            
        if not (hasattr(dataset, uid_attribute) and getattr(dataset, uid_attribute)):
            return
            
        current_uid = getattr(dataset, uid_attribute)
        # Use prefixed key for unique mapping when not maintaining relationships
        mapping_key = dataset_prefix + current_uid if dataset_prefix else current_uid
        
        if mapping_key not in uid_mapping:
            # Try registry lookup if maintaining relationships
            if config.maintain_relationships and registry_method and hasattr(self._uid_registry, registry_method):
                # Check if we need parent UID context
                if parent_uid_attribute:
                    parent_uid = None
                    if hasattr(dataset, parent_uid_attribute) and getattr(dataset, parent_uid_attribute) in uid_mapping:
                        parent_uid = uid_mapping[getattr(dataset, parent_uid_attribute)]
                    
                    if parent_uid:
                        new_uid = getattr(self._uid_registry, registry_method)(current_uid)
                        if new_uid:
                            uid_mapping[mapping_key] = new_uid
                            if dataset_prefix:
                                uid_mapping[current_uid] = new_uid
                            return
                else:
                    # Simple registry lookup without parent context
                    new_uid = getattr(self._uid_registry, registry_method)(current_uid)
                    if new_uid:
                        uid_mapping[mapping_key] = new_uid
                        if dataset_prefix:
                            uid_mapping[current_uid] = new_uid
                        return
            
            # Generate new UID if not found in registry or not maintaining relationships
            uid_mapping[mapping_key] = generator_method()
            # Also store without prefix for lookup during regeneration
            if dataset_prefix:
                uid_mapping[current_uid] = uid_mapping[mapping_key]
    
    def _collect_referenced_uids(
        self,
        dataset: Dataset,
        config: UIDRegenerationConfig,
        uid_mapping: Dict[str, str],
        dataset_prefix: str = ""
    ) -> None:
        """Collect referenced UIDs from DICOM sequences and generate new ones if needed."""
        # Process all possible reference sequences that might contain UIDs
        reference_sequences = [
            'ReferencedStudySequence',
            'ReferencedSeriesSequence',
            'ReferencedInstanceSequence',
            'ReferencedSOPSequence',
            'ReferencedFrameOfReferenceSequence',
            'SourceImageSequence',
            'ReferencedImageSequence',
            'ReferencedRTPlanSequence',
            'ReferencedStructureSetSequence',
            'ReferencedPatientSetupSequence',
            'ReferencedDoseSequence',
            'ReferencedFractionGroupSequence',
            'ReferencedBeamSequence',
            'ReferencedBrachyApplicationSetupSequence',
            'ReferencedVerificationImageSequence'
        ]
        
        for seq_name in reference_sequences:
            if hasattr(dataset, seq_name) and getattr(dataset, seq_name):
                for ref in getattr(dataset, seq_name):
                    # Handle standard reference attributes
                    for attr in ['ReferencedSOPInstanceUID', 'ReferencedSOPClassUID', 'ReferencedFrameOfReferenceUID']:
                        if hasattr(ref, attr) and getattr(ref, attr):
                            current_uid = getattr(ref, attr)
                            # Use prefixed key for unique mapping when not maintaining relationships
                            mapping_key = dataset_prefix + current_uid if dataset_prefix else current_uid
                            
                            if mapping_key not in uid_mapping:
                                # Generate new UID for referenced UIDs
                                if 'SOPInstance' in attr:
                                    uid_mapping[mapping_key] = config.generator.generate_sop_instance_uid()
                                elif 'FrameOfReference' in attr:
                                    uid_mapping[mapping_key] = config.generator.generate_frame_of_reference_uid()
                                else:
                                    # For other types, generate a generic UID
                                    uid_mapping[mapping_key] = config.generator.generate_sop_instance_uid()
                                
                                # Also store without prefix for lookup during regeneration
                                if dataset_prefix:
                                    uid_mapping[current_uid] = uid_mapping[mapping_key]
                    
                    # Handle nested sequences
                    if hasattr(ref, 'ReferencedInstanceSequence'):
                        for instance_ref in ref.ReferencedInstanceSequence:
                            if hasattr(instance_ref, 'ReferencedSOPInstanceUID'):
                                current_uid = instance_ref.ReferencedSOPInstanceUID
                                mapping_key = dataset_prefix + current_uid if dataset_prefix else current_uid
                                if mapping_key not in uid_mapping:
                                    uid_mapping[mapping_key] = config.generator.generate_sop_instance_uid()
                                    if dataset_prefix:
                                        uid_mapping[current_uid] = uid_mapping[mapping_key]
                    
                    # Handle RT Referenced Series Sequence specifically
                    if hasattr(ref, 'RTReferencedSeriesSequence'):
                        for rt_series_ref in ref.RTReferencedSeriesSequence:
                            if hasattr(rt_series_ref, 'SeriesInstanceUID'):
                                current_uid = rt_series_ref.SeriesInstanceUID
                                mapping_key = dataset_prefix + current_uid if dataset_prefix else current_uid
                                if mapping_key not in uid_mapping:
                                    uid_mapping[mapping_key] = config.generator.generate_series_instance_uid()
                                    if dataset_prefix:
                                        uid_mapping[current_uid] = uid_mapping[mapping_key]
                            
                            if hasattr(rt_series_ref, 'ContourImageSequence'):
                                for contour_ref in rt_series_ref.ContourImageSequence:
                                    if hasattr(contour_ref, 'ReferencedSOPInstanceUID'):
                                        current_uid = contour_ref.ReferencedSOPInstanceUID
                                        mapping_key = dataset_prefix + current_uid if dataset_prefix else current_uid
                                        if mapping_key not in uid_mapping:
                                            uid_mapping[mapping_key] = config.generator.generate_sop_instance_uid()
                                            if dataset_prefix:
                                                uid_mapping[current_uid] = uid_mapping[mapping_key]
    
    def _regenerate_dataset(
        self,
        dataset: Dataset,
        config: UIDRegenerationConfig,
        uid_mapping: Dict[str, str]
    ) -> Dataset:
        """Create a new dataset with regenerated UIDs."""
        # Create a deep copy to avoid modifying the original
        new_ds = copy.deepcopy(dataset)
        
        # Update UIDs based on the mapping
        if config.regenerate & UIDCategory.STUDY and hasattr(new_ds, 'StudyInstanceUID') and new_ds.StudyInstanceUID:
            new_ds.StudyInstanceUID = uid_mapping.get(
                new_ds.StudyInstanceUID, 
                new_ds.StudyInstanceUID
            )
            
        if config.regenerate & UIDCategory.SERIES and hasattr(new_ds, 'SeriesInstanceUID') and new_ds.SeriesInstanceUID:
            new_ds.SeriesInstanceUID = uid_mapping.get(
                new_ds.SeriesInstanceUID,
                new_ds.SeriesInstanceUID
            )
            
        if config.regenerate & UIDCategory.INSTANCE and hasattr(new_ds, 'SOPInstanceUID') and new_ds.SOPInstanceUID:
            new_ds.SOPInstanceUID = uid_mapping.get(
                new_ds.SOPInstanceUID,
                new_ds.SOPInstanceUID
            )
            
        if (config.regenerate & UIDCategory.FRAME_OF_REF and 
            hasattr(new_ds, 'FrameOfReferenceUID') and 
            new_ds.FrameOfReferenceUID):
            new_ds.FrameOfReferenceUID = uid_mapping.get(
                new_ds.FrameOfReferenceUID,
                new_ds.FrameOfReferenceUID
            )
            
        # Always ensure MediaStorageSOPInstanceUID matches SOPInstanceUID if it exists
        if (hasattr(new_ds, 'file_meta') and 
            hasattr(new_ds.file_meta, 'MediaStorageSOPInstanceUID') and
            hasattr(new_ds, 'SOPInstanceUID') and 
            new_ds.SOPInstanceUID):
            new_ds.file_meta.MediaStorageSOPInstanceUID = new_ds.SOPInstanceUID
            
        # Process referenced UIDs in sequences if needed
        if config.regenerate & UIDCategory.REFERENCED:
            self._process_referenced_uids(new_ds, uid_mapping)
        
        # Update the UID registry if maintaining relationships
        if config.maintain_relationships and hasattr(self, '_uid_registry'):
            self._update_uid_registry(new_ds)
            
        return new_ds
        
    def _update_uid_registry(self, dataset: Dataset) -> None:
        """Update the UID registry with the new UIDs."""
        # Get or create study UID
        study_uid = None
        if hasattr(dataset, 'StudyInstanceUID') and dataset.StudyInstanceUID:
            study_uid = dataset.StudyInstanceUID
            try:
                self._uid_registry.register_study_uid(study_uid)
            except UIDGenerationError:
                # Study already exists, this is fine
                pass
        
        # Get or create series UID
        series_uid = None
        if hasattr(dataset, 'SeriesInstanceUID') and dataset.SeriesInstanceUID:
            series_uid = dataset.SeriesInstanceUID
            if study_uid:
                try:
                    self._uid_registry.register_series_uid(series_uid, study_uid)
                except UIDGenerationError:
                    # Series already exists, this is fine
                    pass
        
        # Register instance UID if it exists
        if hasattr(dataset, 'SOPInstanceUID') and dataset.SOPInstanceUID and series_uid:
            try:
                self._uid_registry.register_instance_uid(dataset.SOPInstanceUID, series_uid)
            except UIDGenerationError:
                # Instance already exists, this is fine
                pass
        
        # Register frame of reference UID if it exists
        if (hasattr(dataset, 'FrameOfReferenceUID') and 
            dataset.FrameOfReferenceUID and 
            study_uid):
            try:
                self._uid_registry.register_frame_reference_uid(
                    dataset.FrameOfReferenceUID, 
                    study_uid
                )
            except UIDGenerationError:
                # Frame of reference already exists, this is fine
                pass
    
    def _process_referenced_uids(self, dataset: Dataset, uid_mapping: Dict[str, str]) -> None:
        """Process UIDs in referenced sequences."""
        # Process all possible reference sequences that might contain UIDs
        reference_sequences = [
            'ReferencedStudySequence',
            'ReferencedSeriesSequence',
            'ReferencedInstanceSequence',
            'ReferencedSOPSequence',
            'ReferencedFrameOfReferenceSequence',
            'SourceImageSequence',
            'ReferencedImageSequence',
            'ReferencedRTPlanSequence',
            'ReferencedStructureSetSequence',
            'ReferencedPatientSetupSequence',
            'ReferencedDoseSequence',
            'ReferencedFractionGroupSequence',
            'ReferencedBeamSequence',
            'ReferencedBrachyApplicationSetupSequence',
            'ReferencedVerificationImageSequence'
        ]
        
        for seq_name in reference_sequences:
            if hasattr(dataset, seq_name) and getattr(dataset, seq_name):
                for ref in getattr(dataset, seq_name):
                    # Handle standard reference attributes
                    for attr in ['ReferencedSOPInstanceUID', 'ReferencedSOPClassUID', 'ReferencedFrameOfReferenceUID']:
                        if hasattr(ref, attr) and getattr(ref, attr):
                            current_uid = getattr(ref, attr)
                            setattr(ref, attr, uid_mapping.get(current_uid, current_uid))
                    
                    # Handle nested sequences (e.g., ReferencedInstanceSequence within ReferencedSeriesSequence)
                    if hasattr(ref, 'ReferencedInstanceSequence'):
                        for instance_ref in ref.ReferencedInstanceSequence:
                            if hasattr(instance_ref, 'ReferencedSOPInstanceUID'):
                                current_uid = instance_ref.ReferencedSOPInstanceUID
                                instance_ref.ReferencedSOPInstanceUID = uid_mapping.get(current_uid, current_uid)
                    
                    # Handle RT Referenced Series Sequence specifically
                    if hasattr(ref, 'RTReferencedSeriesSequence'):
                        for rt_series_ref in ref.RTReferencedSeriesSequence:
                            if hasattr(rt_series_ref, 'SeriesInstanceUID'):
                                current_uid = rt_series_ref.SeriesInstanceUID
                                rt_series_ref.SeriesInstanceUID = uid_mapping.get(current_uid, current_uid)
                            
                            if hasattr(rt_series_ref, 'ContourImageSequence'):
                                for contour_ref in rt_series_ref.ContourImageSequence:
                                    if hasattr(contour_ref, 'ReferencedSOPInstanceUID'):
                                        current_uid = contour_ref.ReferencedSOPInstanceUID
                                        contour_ref.ReferencedSOPInstanceUID = uid_mapping.get(current_uid, current_uid)

# Convenience function for common use cases
def regenerate_dicom_files(
    file_paths: List[str],
    output_dir: str = None,
    config: Optional[UIDRegenerationConfig] = None
) -> List[Dataset]:
    """
    Convenience function to regenerate UIDs in DICOM files.
    
    Args:
        file_paths: List of paths to DICOM files
        output_dir: Optional directory to save regenerated files. If None, files are not saved.
        config: Optional configuration for UID regeneration
        
    Returns:
        List of datasets with regenerated UIDs
    """
    regenerator = DICOMUIDRegenerator(config)
    datasets = [pydicom.dcmread(f) for f in file_paths]
    results = regenerator.regenerate_datasets(datasets)
    
    if output_dir:
        import os
        os.makedirs(output_dir, exist_ok=True)
        for ds in results:
            output_path = os.path.join(output_dir, f"{ds.SOPInstanceUID}.dcm")
            ds.save_as(output_path)
            
    return results