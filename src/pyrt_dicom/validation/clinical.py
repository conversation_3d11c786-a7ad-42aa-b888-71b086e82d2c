# Copyright (C) 2024 Pirate DICOM Contributors

"""
Clinical Validation Framework for RT DICOM Objects.

This module provides comprehensive clinical validation for RT DICOM objects,
ensuring clinical safety, reasonableness checks, and TPS compatibility.
Validates structure volumes, naming conventions, dose parameters, and geometric
relationships following AAPM TG-263 guidelines and clinical practice standards.

## Clinical Validation Categories

### Structure Validation
- **Volume Limits**: Validates structure volumes against clinical ranges
- **Naming Conventions**: Ensures standard clinical structure naming
- **Geometric Reasonableness**: Validates aspect ratios and spatial relationships
- **TPS Compatibility**: Ensures structure parameters meet TPS requirements

### Dose Validation  
- **Dose Ranges**: Validates dose values against clinical safety limits
- **Fraction Schemes**: Ensures reasonable fractionation patterns
- **Unit Consistency**: Validates dose units and scaling factors
- **Clinical Feasibility**: Checks dose rates and delivery parameters

### Geometric Validation
- **Coordinate Consistency**: Ensures spatial alignment between objects
- **Resolution Limits**: Validates pixel spacing and slice thickness
- **Field of View**: Ensures adequate coverage for treatment delivery
- **Geometric Integrity**: Validates contour closure and topology

## Clinical Context

This validation framework implements safety checks based on:
- **AAPM TG-263**: Standardized nomenclature for RT structures
- **IEC 60601-2-1**: Safety requirements for RT equipment
- **Clinical Practice Guidelines**: Common clinical parameter ranges
- **TPS Compatibility**: Multi-vendor treatment planning system requirements

The framework provides both **warning-level** validation (common clinical ranges)
and **error-level** validation (safety-critical limits) to support different
clinical workflows while maintaining safety standards.
"""

from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np
from numpy.typing import NDArray
import logging
from dataclasses import dataclass
from enum import Enum

from ..utils.exceptions import ValidationError, PyrtDicomError
from ..utils.logging import get_clinical_logger


# Configure module logger
logger = get_clinical_logger(__name__)


class ValidationLevel(Enum):
    """Validation severity levels."""
    INFO = "info"
    WARNING = "warning"  
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of clinical validation check."""
    level: ValidationLevel
    message: str
    parameter: str
    value: Any
    expected_range: Optional[Tuple[float, float]] = None
    suggestion: Optional[str] = None


class ClinicalValidator:
    """
    Comprehensive clinical validation for RT DICOM objects.
    
    Provides multi-level validation including structure volume limits,
    clinical naming conventions, dose parameter safety checks, and
    geometric reasonableness validation following clinical practice
    guidelines and safety standards.
    
    Clinical Notes
    --------------
    This validator implements clinical safety checks based on AAPM TG-263
    guidelines, IEC 60601-2-1 safety requirements, and common clinical
    practice standards. Validation levels allow flexibility for different
    clinical workflows while maintaining essential safety requirements.
    
    **Structure Validation**: Validates structure volumes, names, and
    geometric properties against clinical ranges and TPS requirements.
    
    **Dose Validation**: Ensures dose parameters are within clinical
    safety limits and follow reasonable fractionation schemes.
    
    **Geometric Validation**: Validates spatial relationships and
    coordinate system consistency across RT objects.
    
    Parameters
    ----------
    strict_mode : bool, default True
        If True, enforce strict clinical guidelines
        If False, allow more flexibility for research workflows
    custom_limits : Dict[str, Any], optional
        Custom clinical limits to override defaults
    tps_compatibility : bool, default True
        Enable TPS-specific compatibility checks
        
    Examples
    --------
    Structure volume validation:
    
    >>> validator = ClinicalValidator()
    >>> # Validate PTV volume (typical range 10-2000 cc)
    >>> results = validator.validate_structure_volume(
    ...     volume_cc=150.0,
    ...     structure_name="PTV_7000", 
    ...     structure_type="PTV"
    ... )
    >>> len([r for r in results if r.level == ValidationLevel.ERROR])
    0
    
    Clinical naming validation:
    
    >>> # Check structure name against TG-263 guidelines
    >>> results = validator.validate_structure_naming(
    ...     structure_name="Heart",
    ...     structure_type="ORGAN"
    ... )
    >>> all(r.level != ValidationLevel.ERROR for r in results)
    True
    """
    
    # Clinical parameter limits based on AAPM TG-263 and clinical practice
    DOSE_LIMITS = {
        'max_clinical_dose': 100.0,         # Gy, typical high-dose limit (up to 80 Gy for head/neck)
        'max_fraction_dose': 8.0,           # Gy, SBRT maximum
        'min_meaningful_dose': 0.01,        # Gy, minimum clinical relevance
        'max_dose_rate': 2400.0,            # cGy/min, maximum clinical dose rate
        'min_dose_rate': 50.0,              # cGy/min, minimum practical dose rate
        'max_total_fractions': 50,          # Maximum reasonable fraction count
        'min_total_fractions': 1,           # SBRT single fraction minimum
    }
    
    STRUCTURE_VOLUME_LIMITS = {
        # Structure type volume limits in cc (clinical reasonableness)
        'PTV': {'min': 0.5, 'max': 3000.0, 'typical_range': (10.0, 500.0)},
        'CTV': {'min': 0.1, 'max': 2500.0, 'typical_range': (5.0, 400.0)},
        'GTV': {'min': 0.01, 'max': 2000.0, 'typical_range': (0.5, 300.0)},
        'ORGAN': {'min': 0.001, 'max': 8000.0, 'typical_range': (1.0, 2000.0)},
        'BODY': {'min': 10.0, 'max': 200000.0, 'typical_range': (50000.0, 120000.0)},
        'EXTERNAL': {'min': 10.0, 'max': 200000.0, 'typical_range': (50000.0, 120000.0)},
        'SUPPORT': {'min': 0.1, 'max': 50000.0, 'typical_range': (100.0, 10000.0)},
        'MARKER': {'min': 0.001, 'max': 10.0, 'typical_range': (0.01, 1.0)},
    }
    
    GEOMETRIC_LIMITS = {
        'max_structure_volume': 200000.0,    # cc, absolute maximum (whole body)
        'min_structure_volume': 0.001,       # cc, minimum meaningful volume
        'max_pixel_spacing': 5.0,            # mm, coarse resolution limit
        'min_pixel_spacing': 0.1,            # mm, fine resolution limit
        'max_slice_thickness': 10.0,         # mm, thick slice limit
        'min_slice_thickness': 0.5,          # mm, thin slice limit
        'max_aspect_ratio': 50.0,            # Maximum structure aspect ratio
        'min_contour_points': 3,             # Minimum points for valid contour
        'max_contour_points': 5000,          # Maximum points (performance limit)
    }
    
    # Clinical structure naming conventions (TG-263 based)
    CLINICAL_STRUCTURE_NAMES = {
        # Target volumes
        'PTV': ['PTV', 'PTV1', 'PTV2', 'PTV_HIGH', 'PTV_LOW', 'PTV_BOOST'],
        'CTV': ['CTV', 'CTV1', 'CTV2', 'CTV_HIGH', 'CTV_LOW', 'CTV_BOOST'],
        'GTV': ['GTV', 'GTV1', 'GTV2', 'GTV_PRIMARY', 'GTV_NODES'],
        
        # Critical organs (partial TG-263 list)
        'ORGAN': [
            'Brain', 'Brainstem', 'SpinalCord', 'Esophagus', 'Heart', 'Lung_L', 'Lung_R',
            'Liver', 'Kidney_L', 'Kidney_R', 'Bladder', 'Rectum', 'Bowel', 'Parotid_L', 
            'Parotid_R', 'Cochlea_L', 'Cochlea_R', 'Lens_L', 'Lens_R', 'OpticNrv_L', 
            'OpticNrv_R', 'OpticChiasm', 'Pituitary', 'Thyroid', 'Larynx', 'Mandible',
            'Femur_L', 'Femur_R', 'BrachialPlex_L', 'BrachialPlex_R'
        ],
        
        # External and support structures  
        'EXTERNAL': ['BODY', 'EXTERNAL', 'SKIN'],
        'SUPPORT': ['Couch', 'CouchTop', 'Headrest', 'Immobilization'],
        'MARKER': ['Marker', 'BB', 'Fiducial', 'Clip']
    }
    
    def __init__(
        self,
        strict_mode: bool = True,
        custom_limits: Optional[Dict[str, Any]] = None,
        tps_compatibility: bool = True
    ):
        """Initialize clinical validator with configuration options."""
        self.strict_mode = strict_mode
        self.tps_compatibility = tps_compatibility
        
        # Apply custom limits if provided
        self.dose_limits = self.DOSE_LIMITS.copy()
        self.volume_limits = self.STRUCTURE_VOLUME_LIMITS.copy()
        self.geometric_limits = self.GEOMETRIC_LIMITS.copy()
        
        if custom_limits:
            if 'dose_limits' in custom_limits:
                self.dose_limits.update(custom_limits['dose_limits'])
            if 'volume_limits' in custom_limits:
                self.volume_limits.update(custom_limits['volume_limits'])
            if 'geometric_limits' in custom_limits:
                self.geometric_limits.update(custom_limits['geometric_limits'])
    
    def validate_structure_volume(
        self,
        volume_cc: float,
        structure_name: str,
        structure_type: str = "ORGAN"
    ) -> List[ValidationResult]:
        """
        Validate structure volume against clinical limits.
        
        Parameters
        ----------
        volume_cc : float
            Structure volume in cubic centimeters
        structure_name : str
            Clinical structure name
        structure_type : str, default "ORGAN"
            Structure type (PTV, CTV, GTV, ORGAN, BODY, etc.)
            
        Returns
        -------
        List[ValidationResult]
            Validation results with recommendations
            
        Examples
        --------
        >>> validator = ClinicalValidator()
        >>> results = validator.validate_structure_volume(
        ...     volume_cc=150.0,
        ...     structure_name="PTV_7000",
        ...     structure_type="PTV"
        ... )
        >>> any(r.level == ValidationLevel.ERROR for r in results)
        False
        """
        results = []
        
        # Get limits for structure type
        if structure_type not in self.volume_limits:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message=f"Unknown structure type '{structure_type}', using ORGAN limits",
                parameter="structure_type",
                value=structure_type,
                suggestion="Use standard structure types: PTV, CTV, GTV, ORGAN, BODY, EXTERNAL, SUPPORT, MARKER"
            ))
            structure_type = "ORGAN"
        
        limits = self.volume_limits[structure_type]
        
        # Critical volume checks (errors)
        if volume_cc < 0:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message="Structure volume cannot be negative",
                parameter="volume_cc",
                value=volume_cc,
                suggestion="Check mask calculation or coordinate system"
            ))
        
        if volume_cc < limits['min']:
            level = ValidationLevel.ERROR if self.strict_mode else ValidationLevel.WARNING
            results.append(ValidationResult(
                level=level,
                message=f"Structure volume {volume_cc:.3f} cc below minimum for {structure_type}",
                parameter="volume_cc",
                value=volume_cc,
                expected_range=(limits['min'], limits['max']),
                suggestion=f"Consider if structure is clinically meaningful (min: {limits['min']} cc)"
            ))
        
        if volume_cc > limits['max']:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message=f"Structure volume {volume_cc:.1f} cc exceeds maximum for {structure_type}",
                parameter="volume_cc",
                value=volume_cc,
                expected_range=(limits['min'], limits['max']),
                suggestion=f"Verify structure definition (max: {limits['max']} cc)"
            ))
        
        # Typical range warnings
        typical_min, typical_max = limits['typical_range']
        if volume_cc < typical_min or volume_cc > typical_max:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message=f"Structure volume {volume_cc:.1f} cc outside typical range for {structure_type}",
                parameter="volume_cc",
                value=volume_cc,
                expected_range=(typical_min, typical_max),
                suggestion=f"Review structure definition (typical: {typical_min}-{typical_max} cc)"
            ))
        
        # Structure-specific validation
        results.extend(self._validate_structure_specific_volume(
            volume_cc, structure_name, structure_type
        ))
        
        logger.debug(f"Volume validation for {structure_name}: {len(results)} issues found")
        return results
    
    def validate_structure_naming(
        self,
        structure_name: str,
        structure_type: str = "ORGAN"
    ) -> List[ValidationResult]:
        """
        Validate structure naming against clinical conventions.
        
        Parameters
        ----------
        structure_name : str
            Clinical structure name to validate
        structure_type : str, default "ORGAN"
            Expected structure type
            
        Returns
        -------
        List[ValidationResult]
            Validation results with naming recommendations
            
        Examples
        --------
        >>> validator = ClinicalValidator()
        >>> results = validator.validate_structure_naming(
        ...     structure_name="Heart",
        ...     structure_type="ORGAN"
        ... )
        >>> any(r.level == ValidationLevel.ERROR for r in results)
        False
        """
        results = []
        
        # Basic name validation
        if not structure_name or not structure_name.strip():
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message="Structure name cannot be empty",
                parameter="structure_name",
                value=structure_name,
                suggestion="Provide descriptive clinical structure name"
            ))
            return results
        
        # Length validation
        if len(structure_name) > 64:  # DICOM VR limit for CS
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message=f"Structure name length {len(structure_name)} exceeds DICOM limit (64 characters)",
                parameter="structure_name",
                value=structure_name,
                suggestion="Abbreviate structure name while maintaining clinical clarity"
            ))
        
        # Character validation for TPS compatibility
        if not structure_name.replace('_', '').replace('-', '').isalnum():
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message="Structure name contains special characters that may cause TPS compatibility issues",
                parameter="structure_name",
                value=structure_name,
                suggestion="Use alphanumeric characters, underscores, and hyphens only"
            ))
        
        # TG-263 nomenclature validation
        if structure_type in self.CLINICAL_STRUCTURE_NAMES:
            standard_names = self.CLINICAL_STRUCTURE_NAMES[structure_type]
            
            # Check for exact matches
            if structure_name not in standard_names:
                # Check for partial matches or common variations
                close_matches = [name for name in standard_names 
                               if name.lower() in structure_name.lower() or 
                               structure_name.lower() in name.lower()]
                
                if close_matches:
                    results.append(ValidationResult(
                        level=ValidationLevel.INFO,
                        message=f"Structure name '{structure_name}' does not match TG-263 standard",
                        parameter="structure_name",
                        value=structure_name,
                        suggestion=f"Consider standard names: {', '.join(close_matches[:3])}"
                    ))
                else:
                    level = ValidationLevel.WARNING if self.strict_mode else ValidationLevel.INFO
                    results.append(ValidationResult(
                        level=level,
                        message=f"Non-standard structure name for {structure_type}",
                        parameter="structure_name",
                        value=structure_name,
                        suggestion=f"Consider TG-263 standard names: {', '.join(standard_names[:5])}"
                    ))
        
        # Consistency checks between name and type
        results.extend(self._validate_name_type_consistency(structure_name, structure_type))
        
        logger.debug(f"Naming validation for {structure_name}: {len(results)} issues found")
        return results
    
    def validate_dose_parameters(
        self,
        dose_array: Optional[NDArray] = None,
        dose_units: str = "Gy",
        prescription_dose: Optional[float] = None,
        fractions: Optional[int] = None
    ) -> List[ValidationResult]:
        """
        Validate dose parameters for clinical safety.
        
        Parameters
        ----------
        dose_array : NDArray, optional
            3D dose array in specified units
        dose_units : str, default "Gy"
            Dose units (Gy, cGy)
        prescription_dose : float, optional
            Prescription dose in specified units
        fractions : int, optional
            Number of fractions
            
        Returns
        -------
        List[ValidationResult]
            Validation results with dose safety recommendations
        """
        results = []
        
        # Units validation
        if dose_units not in ['Gy', 'cGy', 'RELATIVE']:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message=f"Non-standard dose units '{dose_units}'",
                parameter="dose_units",
                value=dose_units,
                suggestion="Use standard units: Gy, cGy, or RELATIVE"
            ))
        
        # Convert to Gy for validation
        dose_scale = 1.0 if dose_units == "Gy" else 0.01 if dose_units == "cGy" else 1.0
        
        # Dose array validation
        if dose_array is not None:
            dose_gy = dose_array * dose_scale
            max_dose = np.max(dose_gy)
            min_dose = np.min(dose_gy)
            
            # Critical safety checks
            if max_dose > self.dose_limits['max_clinical_dose']:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Maximum dose {max_dose:.1f} Gy exceeds clinical safety limit",
                    parameter="max_dose",
                    value=max_dose,
                    expected_range=(0, self.dose_limits['max_clinical_dose']),
                    suggestion="Verify dose calculation and units"
                ))
            
            if min_dose < 0:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Negative dose values found in dose array",
                    parameter="min_dose",
                    value=min_dose,
                    suggestion="Check dose calculation algorithm"
                ))
        
        # Prescription dose validation
        if prescription_dose is not None:
            prescription_gy = prescription_dose * dose_scale
            
            if prescription_gy > self.dose_limits['max_clinical_dose']:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Prescription dose {prescription_gy:.1f} Gy exceeds clinical limit",
                    parameter="prescription_dose",
                    value=prescription_gy,
                    expected_range=(0, self.dose_limits['max_clinical_dose']),
                    suggestion="Verify prescription dose and units"
                ))
            
            if prescription_gy < self.dose_limits['min_meaningful_dose']:
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Prescription dose {prescription_gy:.3f} Gy below clinical threshold",
                    parameter="prescription_dose",
                    value=prescription_gy,
                    suggestion="Verify if dose is clinically meaningful"
                ))
        
        # Fractionation validation
        if fractions is not None:
            if fractions < self.dose_limits['min_total_fractions']:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Number of fractions must be at least 1",
                    parameter="fractions",
                    value=fractions,
                    suggestion="Use valid fraction count for treatment"
                ))
            
            if fractions > self.dose_limits['max_total_fractions']:
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Fraction count {fractions} unusually high",
                    parameter="fractions",
                    value=fractions,
                    expected_range=(1, self.dose_limits['max_total_fractions']),
                    suggestion="Verify fractionation scheme"
                ))
            
            # Dose per fraction check
            if prescription_dose is not None and fractions > 0:
                dose_per_fraction = (prescription_dose * dose_scale) / fractions
                if dose_per_fraction > self.dose_limits['max_fraction_dose']:
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Dose per fraction {dose_per_fraction:.1f} Gy exceeds safety limit",
                        parameter="dose_per_fraction",
                        value=dose_per_fraction,
                        expected_range=(0, self.dose_limits['max_fraction_dose']),
                        suggestion="Review fractionation scheme for safety"
                    ))
        
        logger.debug(f"Dose parameter validation: {len(results)} issues found")
        return results
    
    def validate_geometric_consistency(
        self,
        objects: List[Dict[str, Any]]
    ) -> List[ValidationResult]:
        """
        Validate geometric consistency between RT objects.
        
        Parameters
        ----------
        objects : List[Dict[str, Any]]
            List of RT objects with geometric parameters
            Each dict should contain 'name', 'type', and geometric data
            
        Returns
        -------
        List[ValidationResult]
            Validation results for geometric consistency
        """
        results = []
        
        if len(objects) < 2:
            return results  # Need at least 2 objects for consistency checks
        
        # Extract Frame of Reference UIDs
        frame_of_reference_uids = []
        for obj in objects:
            if 'frame_of_reference_uid' in obj:
                frame_of_reference_uids.append(obj['frame_of_reference_uid'])
        
        # Check Frame of Reference UID consistency
        if len(set(frame_of_reference_uids)) > 1:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message="Inconsistent Frame of Reference UIDs between objects",
                parameter="frame_of_reference_uid",
                value=frame_of_reference_uids,
                suggestion="Ensure all RT objects share the same coordinate system"
            ))
        
        # Check pixel spacing consistency
        pixel_spacings = []
        for obj in objects:
            if 'pixel_spacing' in obj:
                pixel_spacings.append(tuple(obj['pixel_spacing']))
        
        if len(set(pixel_spacings)) > 1:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message="Different pixel spacings detected between objects",
                parameter="pixel_spacing",
                value=pixel_spacings,
                suggestion="Verify spatial resolution consistency for accurate dose calculations"
            ))
        
        logger.debug(f"Geometric consistency validation: {len(results)} issues found")
        return results
    
    def _validate_structure_specific_volume(
        self,
        volume_cc: float,
        structure_name: str,
        structure_type: str
    ) -> List[ValidationResult]:
        """Validate volume for specific structure types and names."""
        results = []
        
        # Organ-specific volume checks
        name_lower = structure_name.lower()
        
        if 'lung' in name_lower:
            if volume_cc > 8000:  # Total lung capacity ~6000 cc
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Lung volume {volume_cc:.0f} cc unusually large",
                    parameter="volume_cc",
                    value=volume_cc,
                    suggestion="Verify lung structure definition (typical: 1000-4000 cc per lung)"
                ))
        
        elif 'heart' in name_lower:
            if volume_cc > 1500:  # Heart ~500-800 cc
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Heart volume {volume_cc:.0f} cc unusually large",
                    parameter="volume_cc",
                    value=volume_cc,
                    suggestion="Verify heart structure definition (typical: 400-900 cc)"
                ))
        
        elif 'brain' in name_lower:
            if volume_cc > 2000:  # Brain ~1400 cc
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Brain volume {volume_cc:.0f} cc unusually large",
                    parameter="volume_cc",
                    value=volume_cc,
                    suggestion="Verify brain structure definition (typical: 1200-1600 cc)"
                ))
        
        # PTV-specific checks
        if 'ptv' in name_lower:
            if volume_cc > 1000:
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"PTV volume {volume_cc:.0f} cc very large",
                    parameter="volume_cc", 
                    value=volume_cc,
                    suggestion="Large PTVs may require special treatment techniques"
                ))
        
        return results
    
    def _validate_name_type_consistency(
        self,
        structure_name: str,
        structure_type: str
    ) -> List[ValidationResult]:
        """Validate consistency between structure name and type."""
        results = []
        
        name_lower = structure_name.lower()
        
        # Check if name suggests different type
        if structure_type == "ORGAN":
            if any(target in name_lower for target in ['ptv', 'ctv', 'gtv']):
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Structure name '{structure_name}' suggests target volume but type is ORGAN",
                    parameter="structure_type",
                    value=structure_type,
                    suggestion="Consider using appropriate target volume type (PTV, CTV, GTV)"
                ))
        
        elif structure_type in ["PTV", "CTV", "GTV"]:
            type_lower = structure_type.lower()
            if type_lower not in name_lower:
                results.append(ValidationResult(
                    level=ValidationLevel.INFO,
                    message=f"Structure type {structure_type} not reflected in name '{structure_name}'",
                    parameter="structure_name",
                    value=structure_name,
                    suggestion=f"Consider including {structure_type} in structure name for clarity"
                ))
        
        return results


def validate_clinical_parameters(
    structures: Optional[List[Dict[str, Any]]] = None,
    dose_data: Optional[Dict[str, Any]] = None,
    geometric_data: Optional[List[Dict[str, Any]]] = None,
    strict_mode: bool = True
) -> List[ValidationResult]:
    """
    Comprehensive clinical validation for RT DICOM parameters.
    
    Parameters
    ----------
    structures : List[Dict[str, Any]], optional
        Structure data for validation
    dose_data : Dict[str, Any], optional
        Dose parameters for validation
    geometric_data : List[Dict[str, Any]], optional
        Geometric data for consistency validation
    strict_mode : bool, default True
        Enable strict clinical validation
        
    Returns
    -------
    List[ValidationResult]
        Combined validation results
        
    Examples
    --------
    >>> structure_data = [{
    ...     'name': 'PTV_7000',
    ...     'type': 'PTV',
    ...     'volume_cc': 150.0
    ... }]
    >>> results = validate_clinical_parameters(structures=structure_data)
    >>> error_count = len([r for r in results if r.level == ValidationLevel.ERROR])
    >>> error_count
    0
    """
    validator = ClinicalValidator(strict_mode=strict_mode)
    all_results = []
    
    # Structure validation
    if structures:
        for structure in structures:
            name = structure.get('name', 'Unknown')
            struct_type = structure.get('type', 'ORGAN')
            volume = structure.get('volume_cc')
            
            if volume is not None:
                all_results.extend(validator.validate_structure_volume(
                    volume, name, struct_type
                ))
            
            all_results.extend(validator.validate_structure_naming(
                name, struct_type
            ))
    
    # Dose validation
    if dose_data:
        all_results.extend(validator.validate_dose_parameters(**dose_data))
    
    # Geometric consistency validation
    if geometric_data:
        all_results.extend(validator.validate_geometric_consistency(geometric_data))
    
    return all_results


def format_validation_report(results: List[ValidationResult]) -> str:
    """
    Format validation results into readable report.
    
    Parameters
    ----------
    results : List[ValidationResult]
        Validation results to format
        
    Returns
    -------
    str
        Formatted validation report
    """
    if not results:
        return "✅ All clinical validation checks passed"
    
    # Group by severity level
    by_level = {}
    for result in results:
        level = result.level.value
        if level not in by_level:
            by_level[level] = []
        by_level[level].append(result)
    
    report_lines = ["Clinical Validation Report", "=" * 30]
    
    # Summary
    total_issues = len(results)
    error_count = len(by_level.get('error', []))
    warning_count = len(by_level.get('warning', []))
    
    report_lines.append(f"Total Issues: {total_issues}")
    report_lines.append(f"Errors: {error_count}, Warnings: {warning_count}")
    report_lines.append("")
    
    # Details by level
    for level in ['critical', 'error', 'warning', 'info']:
        if level in by_level:
            level_results = by_level[level]
            report_lines.append(f"{level.upper()} ({len(level_results)} issues):")
            
            for result in level_results:
                report_lines.append(f"  • {result.message}")
                if result.suggestion:
                    report_lines.append(f"    → {result.suggestion}")
            report_lines.append("")
    
    return "\n".join(report_lines)