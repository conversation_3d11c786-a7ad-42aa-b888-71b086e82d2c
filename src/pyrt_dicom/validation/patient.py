"""
Patient information validation for DICOM RT objects.

This module provides comprehensive validation for patient information fields,
ensuring DICOM VR (Value Representation) compliance and clinical safety.

## Clinical Context

Patient information validation is critical for:
- DICOM standard compliance (PS 3.5 VR definitions)
- Clinical traceability and audit requirements
- Multi-vendor TPS compatibility
- Patient safety and identification accuracy

## Usage

```python
from pyrt_dicom.validation.patient import PatientInfoValidator, validate_patient_info

# Validate complete patient information
validator = PatientInfoValidator()
errors = validator.validate_patient_info({
    'PatientID': 'RT001',
    'PatientName': 'Doe^John',
    'PatientBirthDate': '19800101',
    'PatientSex': 'M'
})

# Validate individual fields
from pyrt_dicom.validation.patient import validate_patient_id, validate_dicom_date

patient_id_errors = validate_patient_id('RT001')
date_errors = validate_dicom_date('19800101', 'PatientBirthDate')
```
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Union, Any

from ..utils.exceptions import ValidationError


# DICOM VR constraints for patient information fields
PATIENT_INFO_CONSTRAINTS = {
    "PatientID": {
        "vr": "LO",  # Long String
        "max_length": 64,
        "character_set": r"^[A-Za-z0-9\s\.\-_]*$",  # Alphanumeric, space, period, hyphen, underscore
        "required": True,
        "description": "Primary identifier for the patient",
    },
    "PatientName": {
        "vr": "PN",  # Person Name
        "max_length": 64,
        "character_set": r"^[A-Za-z0-9\s\.\-_\^\=\']*$",  # PN allows ^ as component separator and apostrophes
        "required": False,
        "description": "Patient name in DICOM PN format (Family^Given^Middle^Prefix^Suffix)",
    },
    "PatientBirthDate": {
        "vr": "DA",  # Date
        "max_length": 8,
        "format": r"^\d{8}$",  # YYYYMMDD
        "required": False,
        "description": "Patient birth date in YYYYMMDD format",
    },
    "PatientSex": {
        "vr": "CS",  # Code String
        "max_length": 16,
        "valid_values": ["M", "F", "O", ""],  # Male, Female, Other, Unknown
        "required": False,
        "description": "Patient sex (M/F/O or empty for unknown)",
    },
    "PatientAge": {
        "vr": "AS",  # Age String
        "max_length": 4,
        "format": r"^\d{3}[DWMY]$",  # nnnD, nnnW, nnnM, nnnY
        "required": False,
        "description": "Patient age in format nnnD/W/M/Y (days/weeks/months/years)",
    },
    "StudyDate": {
        "vr": "DA",  # Date
        "max_length": 8,
        "format": r"^\d{8}$",  # YYYYMMDD
        "required": False,
        "description": "Study date in YYYYMMDD format",
    },
    "StudyTime": {
        "vr": "TM",  # Time
        "max_length": 16,
        "format": r"^\d{2}(\d{2}(\d{2}(\.\d{1,6})?)?)?$",  # HHMMSS.FFFFFF
        "required": False,
        "description": "Study time in HHMMSS.FFFFFF format",
    },
    "SeriesDate": {
        "vr": "DA",  # Date
        "max_length": 8,
        "format": r"^\d{8}$",  # YYYYMMDD
        "required": False,
        "description": "Series date in YYYYMMDD format",
    },
    "SeriesTime": {
        "vr": "TM",  # Time
        "max_length": 16,
        "format": r"^\d{2}(\d{2}(\d{2}(\.\d{1,6})?)?)?$",  # HHMMSS.FFFFFF
        "required": False,
        "description": "Series time in HHMMSS.FFFFFF format",
    },
}


class PatientInfoValidator:
    """Comprehensive patient information validator for DICOM RT objects.

    Validates patient information fields against DICOM VR (Value Representation)
    constraints, clinical reasonableness checks, and format requirements to ensure
    both DICOM standard compliance and clinical safety.

    Clinical Notes:
        Patient information validation serves multiple critical purposes in RT
        workflows:

        **DICOM Compliance**: Ensures all patient fields conform to DICOM PS 3.5
        VR definitions, preventing compatibility issues with clinical systems.

        **Clinical Traceability**: PatientID validation ensures proper patient
        identification throughout the treatment planning and delivery process.

        **Audit Requirements**: Proper patient information formatting supports
        regulatory compliance and clinical audit trails.

        **Multi-vendor Compatibility**: Standardized validation prevents issues
        when transferring data between different TPS vendors.

        Key validation rules:
        - PatientID: Required, max 64 chars, LO VR (alphanumeric + limited symbols)
        - PatientName: PN VR format Family^Given^Middle^Prefix^Suffix
        - Dates: YYYYMMDD format with clinical reasonableness checks
        - Age: DICOM AS VR format nnnD/W/M/Y with range validation

    Attributes:
        strict_mode: Whether to enforce strict DICOM VR compliance
        constraints: Dictionary of DICOM field constraints and validation rules
    """

    def __init__(self, strict_mode: bool = True) -> None:
        """Initialize patient information validator.

        Args:
            strict_mode: Whether to enforce strict DICOM VR compliance. When True,
                all validation follows exact DICOM standard requirements. When False,
                allows some flexibility for clinical workflows where slight deviations
                may be acceptable (e.g., extended character sets for international names).

        Clinical Notes:
            Strict mode is recommended for production clinical systems to ensure
            maximum compatibility with DICOM viewers and TPS systems. Non-strict
            mode may be useful for research environments or when processing legacy
            data that doesn't fully conform to modern DICOM standards.
        """
        self.strict_mode = strict_mode
        self.constraints = PATIENT_INFO_CONSTRAINTS.copy()

    def validate_patient_info(self, patient_info: Dict[str, Any]) -> List[str]:
        """Validate complete patient information dictionary.

        Args:
            patient_info: Dictionary containing patient information fields.
                Common keys include PatientID, PatientName, PatientBirthDate,
                PatientSex, StudyDate, etc. Values will be validated against
                their respective DICOM VR constraints.

        Returns:
            List of validation error messages. Empty list indicates all fields
            passed validation. Error messages include specific details about
            violations and suggested corrections.

        Clinical Notes:
            This method performs comprehensive validation of all patient fields
            according to DICOM standard requirements and clinical safety checks.
            It validates both presence of required fields and format compliance
            for all provided fields.

            Validation includes:
            - Required field presence (PatientID mandatory)
            - DICOM VR format compliance for all fields
            - Clinical reasonableness (e.g., birth dates not in future)
            - Character set restrictions per DICOM standard
            - Length limits per DICOM VR definitions

        Examples:
            Validate complete patient information:

            >>> validator = PatientInfoValidator()
            >>> patient_data = {
            ...     'PatientID': 'RT001',
            ...     'PatientName': 'Doe^John^M',
            ...     'PatientBirthDate': '19800101',
            ...     'PatientSex': 'M'
            ... }
            >>> errors = validator.validate_patient_info(patient_data)
            >>> if not errors:
            ...     print("All patient information valid")

            Handle validation errors:

            >>> invalid_data = {'PatientID': 'Very_Long_Patient_ID_That_Exceeds_Limits'}
            >>> errors = validator.validate_patient_info(invalid_data)
            >>> for error in errors:
            ...     print(f"Validation error: {error}")
        """
        errors = []

        # Check required fields
        for field_name, constraints in self.constraints.items():
            if constraints.get("required", False):
                if field_name not in patient_info or not patient_info[field_name]:
                    errors.append(
                        f"{field_name} is required for DICOM compliance. "
                        f"{constraints['description']}"
                    )

        # Validate each provided field
        for field_name, value in patient_info.items():
            if (
                field_name in self.constraints and value
            ):  # Only validate non-empty values
                field_errors = self._validate_field(field_name, value)
                errors.extend(field_errors)

        return errors

    def _validate_field(self, field_name: str, value: Any) -> List[str]:
        """Validate individual patient information field."""
        errors = []
        constraints = self.constraints.get(field_name)

        if not constraints:
            return errors  # Unknown field, skip validation

        # Convert to string for validation
        str_value = str(value).strip()

        # Length validation
        if len(str_value) > constraints["max_length"]:
            errors.append(
                f"{field_name} exceeds maximum length of {constraints['max_length']} "
                f"characters (got {len(str_value)}). DICOM VR: {constraints['vr']}"
            )

        # Format validation
        if "format" in constraints:
            if not re.match(constraints["format"], str_value):
                errors.append(
                    f"{field_name} format invalid. Expected: {constraints['description']}. "
                    f"Got: '{str_value}'"
                )

        # Character set validation
        if "character_set" in constraints:
            if not re.match(constraints["character_set"], str_value):
                errors.append(
                    f"{field_name} contains invalid characters. "
                    f"DICOM VR {constraints['vr']} allows: {constraints['character_set']}. "
                    f"Got: '{str_value}'"
                )

        # Valid values validation
        if "valid_values" in constraints:
            if str_value not in constraints["valid_values"]:
                errors.append(
                    f"{field_name} has invalid value. "
                    f"Valid values: {constraints['valid_values']}. "
                    f"Got: '{str_value}'"
                )

        # Field-specific validation
        if field_name in ["PatientBirthDate", "StudyDate", "SeriesDate"]:
            date_errors = self._validate_date_field(field_name, str_value)
            errors.extend(date_errors)
        elif field_name in ["StudyTime", "SeriesTime"]:
            time_errors = self._validate_time_field(field_name, str_value)
            errors.extend(time_errors)
        elif field_name == "PatientAge":
            age_errors = self._validate_age_field(str_value)
            errors.extend(age_errors)

        return errors

    def _validate_date_field(self, field_name: str, date_str: str) -> List[str]:
        """Validate DICOM date field (DA VR)."""
        errors = []

        if len(date_str) != 8:
            return [f"{field_name} must be exactly 8 digits (YYYYMMDD format)"]

        try:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])

            # Clinical reasonableness for birth dates (check first to avoid duplicate messages)
            if field_name == "PatientBirthDate":
                current_year = datetime.now().year
                if year > current_year:
                    errors.append(
                        f"PatientBirthDate cannot be in the future (year {year})"
                    )
                    return errors  # Return early to avoid other range checks
                elif year < current_year - 150:
                    errors.append(f"PatientBirthDate year {year} is unreasonably old")
                    return errors  # Return early to avoid other range checks

            # Basic range checks for non-birth dates or reasonable birth dates
            if year < 1900 or year > 2100:
                errors.append(
                    f"{field_name} year {year} outside reasonable range (1900-2100)"
                )

            if month < 1 or month > 12:
                errors.append(f"{field_name} month {month} invalid (must be 01-12)")

            if day < 1 or day > 31:
                errors.append(f"{field_name} day {day} invalid (must be 01-31)")

            # Validate actual date
            datetime(year, month, day)

        except ValueError as e:
            errors.append(f"{field_name} is not a valid date: {e}")

        return errors

    def _validate_time_field(self, field_name: str, time_str: str) -> List[str]:
        """Validate DICOM time field (TM VR)."""
        errors = []

        # Basic format already checked by regex, validate ranges
        if len(time_str) >= 2:
            try:
                hour = int(time_str[:2])
                if hour > 23:
                    errors.append(f"{field_name} hour {hour} invalid (must be 00-23)")
            except ValueError:
                errors.append(f"{field_name} hour component invalid")

        if len(time_str) >= 4:
            try:
                minute = int(time_str[2:4])
                if minute > 59:
                    errors.append(
                        f"{field_name} minute {minute} invalid (must be 00-59)"
                    )
            except ValueError:
                errors.append(f"{field_name} minute component invalid")

        if len(time_str) >= 6:
            try:
                second = int(time_str[4:6])
                if second > 59:
                    errors.append(
                        f"{field_name} second {second} invalid (must be 00-59)"
                    )
            except ValueError:
                errors.append(f"{field_name} second component invalid")

        return errors

    def _validate_age_field(self, age_str: str) -> List[str]:
        """Validate DICOM age field (AS VR)."""
        errors = []

        if len(age_str) != 4:
            return ["PatientAge must be exactly 4 characters (nnnD/W/M/Y format)"]

        try:
            age_value = int(age_str[:3])
            age_unit = age_str[3]

            # Validate age ranges based on unit
            if age_unit == "D":  # Days
                if age_value > 365:
                    errors.append(
                        "PatientAge in days should not exceed 365 (use weeks/months/years)"
                    )
            elif age_unit == "W":  # Weeks
                if age_value > 520:  # ~10 years
                    errors.append(
                        "PatientAge in weeks should not exceed 520 (use months/years)"
                    )
            elif age_unit == "M":  # Months
                if age_value > 1200:  # 100 years
                    errors.append(
                        "PatientAge in months should not exceed 1200 (100 years)"
                    )
            elif age_unit == "Y":  # Years
                if age_value > 150:
                    errors.append("PatientAge in years should not exceed 150")

        except ValueError:
            errors.append("PatientAge numeric component must be valid integer")

        return errors


# Convenience functions for common validation tasks


def validate_patient_info(
    patient_info: Dict[str, Any], strict_mode: bool = True
) -> List[str]:
    """
    Validate patient information dictionary.

    Parameters
    ----------
    patient_info : Dict[str, Any]
        Patient information to validate
    strict_mode : bool, default True
        Enable strict DICOM VR compliance

    Returns
    -------
    List[str]
        List of validation errors
    """
    validator = PatientInfoValidator(strict_mode=strict_mode)
    return validator.validate_patient_info(patient_info)


def validate_patient_id(patient_id: str) -> List[str]:
    """
    Validate PatientID field specifically.

    Parameters
    ----------
    patient_id : str
        Patient ID to validate

    Returns
    -------
    List[str]
        List of validation errors

    Examples
    --------
    Valid patient IDs:

    >>> errors = validate_patient_id("RT001")
    >>> len(errors)
    0

    >>> errors = validate_patient_id("Patient_123")
    >>> len(errors)
    0

    Common Issues and Solutions
    ---------------------------

    **Issue 1: Patient ID too long**

    >>> # DICOM LO VR has 64 character limit
    >>> long_id = "A" * 65  # 65 characters
    >>> errors = validate_patient_id(long_id)
    >>> print(errors[0])  # "PatientID exceeds maximum length of 64 characters"
    >>> # Solution: Truncate or use shorter identifier
    >>> short_id = long_id[:64]
    >>> validate_patient_id(short_id)  # Should pass

    **Issue 2: Invalid characters**

    >>> # Some characters not allowed in DICOM LO VR
    >>> errors = validate_patient_id("Patient@123")  # @ not allowed
    >>> # Solution: Use only alphanumeric, space, and basic punctuation
    >>> fixed_id = "Patient_123"
    >>> validate_patient_id(fixed_id)  # Should pass

    **Issue 3: Empty or whitespace-only ID**

    >>> errors = validate_patient_id("")
    >>> errors = validate_patient_id("   ")  # Whitespace only
    >>> # Solution: Provide meaningful identifier
    >>> validate_patient_id("ANON_001")  # For anonymous patients

    Clinical Notes
    --------------
    - **Required field**: PatientID is mandatory for DICOM compliance
    - **Uniqueness**: Should be unique within your institution's system
    - **Privacy**: Consider using study-specific IDs for research
    - **Format**: Alphanumeric with underscores/hyphens recommended
    - **Length**: Keep under 16 characters for maximum compatibility
    """
    validator = PatientInfoValidator()
    return validator._validate_field("PatientID", patient_id)


def validate_dicom_date(date_str: str, field_name: str = "Date") -> List[str]:
    """
    Validate DICOM date string (DA VR).

    Parameters
    ----------
    date_str : str
        Date string in YYYYMMDD format
    field_name : str, default 'Date'
        Name of the date field for error messages

    Returns
    -------
    List[str]
        List of validation errors
    """
    validator = PatientInfoValidator()
    return validator._validate_date_field(field_name, date_str)
