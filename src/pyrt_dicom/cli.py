"""
Command-line interface for pyrt-dicom.

This module provides CLI commands for DICOM RT file creation, validation,
and manipulation tasks commonly used by medical physicists.

## Available Commands

### Main Commands
- `pyrt-dicom create`: Create DICOM RT objects from various input formats
- `pyrt-dicom validate`: Validate existing DICOM files for clinical compliance
- `pyrt-dicom convert`: Convert between different RT data formats
- `pyrt-dicom info`: Display information about DICOM files

### Usage Examples

```bash
# Create RT Structure Set from mask files
pyrt-dicom create rtstruct --ct-reference planning_ct.dcm --masks masks/ --output structures.dcm

# Validate DICOM file compliance
pyrt-dicom validate --file rtdose.dcm --clinical-checks

# Display DICOM file information
pyrt-dicom info rtplan.dcm

# Convert dose array to DICOM RT Dose
pyrt-dicom create rtdose --array dose.npy --reference-ct planning_ct.dcm --output dose.dcm
```

## Clinical Workflow Integration

The CLI is designed to integrate seamlessly with clinical physics workflows:

- **Batch Processing**: Process multiple files with glob patterns
- **Clinical Validation**: Built-in safety checks for RT parameters
- **Format Flexibility**: Support for common physics data formats (NumPy, MATLAB, etc.)
- **Audit Logging**: Comprehensive logging for regulatory compliance

For detailed command documentation, run:
```bash
pyrt-dicom --help
pyrt-dicom <command> --help
```
"""

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from pyrt_dicom import utils, __version__

app = typer.Typer(
    name="pyrt-dicom",
    help="CLI for creating and manipulating DICOM RT files for medical physics workflows.",
    no_args_is_help=True,
    rich_markup_mode="rich"
)
console = Console()


@app.command()
def info(
    version: bool = typer.Option(False, "--version", "-v", help="Show version information")
):
    """
    Display pyrt-dicom information and status.
    
    Shows version, supported modalities, and basic usage information
    for medical physicists new to the tool.
    """
    if version:
        console.print(f"pyrt-dicom version {__version__}")
        return
    
    # Create version and status info panel
    info_table = Table(show_header=False, box=None, padding=(0, 2))
    info_table.add_row("[bold blue]Version:[/bold blue]", __version__)
    info_table.add_row("[bold blue]Author:[/bold blue]", "Scott Robertson")
    info_table.add_row("[bold blue]Purpose:[/bold blue]", "DICOM RT file creation for medical physics")
    
    # Supported modalities
    modalities_table = Table(show_header=True, box=None, padding=(0, 1))
    modalities_table.add_column("Modality", style="cyan")
    modalities_table.add_column("Status", style="green")
    modalities_table.add_column("Description")
    
    modalities_table.add_row("CT", "✓ Planned", "CT image series creation")
    modalities_table.add_row("RTSTRUCT", "✓ Planned", "RT Structure Set creation")
    modalities_table.add_row("RTDOSE", "✓ Planned", "RT Dose distribution creation")
    modalities_table.add_row("RTPLAN", "✓ Planned", "RT Plan creation")
    
    console.print(Panel(info_table, title="[bold]pyrt-dicom Information[/bold]", border_style="blue"))
    console.print(Panel(modalities_table, title="[bold]Supported DICOM Modalities[/bold]", border_style="green"))
    
    console.print("\n[bold yellow]Note:[/bold yellow] This is an early-stage project. Most functionality is currently in development.")
    console.print("For the latest documentation, visit: https://github.com/your-repo/pyrt-dicom")


@app.command()  
def main():
    """
    Main entry point for pyrt-dicom CLI.
    
    This is a placeholder command that will be replaced with actual
    DICOM creation functionality as the project develops.
    
    Clinical Usage:
        The main command will eventually provide quick access to common
        RT DICOM creation tasks without requiring knowledge of the
        detailed Python API.
    """
    console.print(Panel(
        "[bold red]Development Notice[/bold red]\n\n"
        "pyrt-dicom is currently in early development. "
        "The CLI interface is being designed but not yet implemented.\n\n"
        "[bold blue]Coming Soon:[/bold blue]\n"
        "• DICOM RT Structure Set creation from masks\n"
        "• DICOM RT Dose creation from dose arrays\n" 
        "• DICOM RT Plan creation from beam configurations\n"
        "• Clinical validation and compliance checking\n\n"
        "[bold green]Current Status:[/bold green]\n"
        "• Core framework and validation systems implemented\n"
        "• UID generation and coordinate systems ready\n"
        "• Clinical logging and audit trail framework complete\n\n"
        "Use [bold cyan]pyrt-dicom info[/bold cyan] for project status.",
        title="pyrt-dicom CLI", 
        border_style="yellow"
    ))
    
    # This will be replaced with actual functionality
    utils.do_something_useful()


if __name__ == "__main__":
    app()
