"""
RT Structure Set IOD Template Implementation.

Provides a complete DICOM Information Object Definition (IOD) template for RT
Structure Set objects, implementing all required and optional modules according
to DICOM Part 3 Section C.8.8.5 (RT Structure Set IOD).

## Clinical Usage

The RT Structure Set template ensures that all created RTSTRUCT DICOM files
contain the proper DICOM elements required for clinical viewing systems and
treatment planning systems. This includes mandatory elements for structure
definitions, contour data, and geometric relationships.

```python
import pyrt_dicom as prt
import numpy as np

# Create RT Structure Set from mask data
masks = {
    'PTV_7000': ptv_mask,  # 3D binary array
    'Bladder': bladder_mask,
    'Rectum': rectum_mask
}
colors = ['red', 'yellow', 'blue']

rt_struct = prt.RTStructureSet.from_masks(
    ct_reference=ct_reference,
    masks=masks,
    colors=colors,
    patient_info={'PatientID': 'RT001'}
)
rt_struct.save('rt_structures.dcm')
```

## DICOM Compliance

This template implements the complete RT Structure Set IOD as defined in DICOM
PS 3.3 Part 3, ensuring compatibility with all major treatment planning systems
including:

- Varian Eclipse
- Elekta Monaco  
- RaySearch RayStation
- Philips Pinnacle
- CMS XiO/Monaco
- Accuray CyberKnife
- BrainLAB iPlan

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.rt_struct.RTStructureSet` - RT Structure Set implementation
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Base DICOM creator framework
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Spatial consistency

**See Also**:
- DICOM Part 3 Section C.8.8.5: RT Structure Set IOD
- DICOM Part 6: Data Dictionary for element definitions
- :mod:`~pyrt_dicom.validation.dicom_compliance` - IOD compliance validation
"""

from typing import Dict, Any, Optional, List, Tuple
import pydicom
from pydicom.dataset import Dataset
from pydicom.sequence import Sequence
from pydicom.uid import RTStructureSetStorage
import numpy as np

from ..utils.exceptions import TemplateError
from ..uid_generation.generators import DefaultUIDGenerator


class RTStructureSetTemplate:
    """DICOM RT Structure Set IOD template implementation.

    Creates properly structured DICOM datasets conforming to the RT Structure
    Set IOD specification (DICOM PS 3.3 C.8.8.5). Handles all required modules
    and ensures clinical compatibility across TPS vendors.

    Clinical Notes:
        The RT Structure Set IOD is fundamental to radiotherapy workflows as it
        defines regions of interest (ROIs) for treatment planning. Key clinical
        requirements include:

        - Precise contour definition for target volumes and organs at risk
        - Consistent spatial relationships with planning CT images
        - Structure naming conventions for clinical workflow integration
        - Color assignments for visual identification in planning systems
        - Frame of Reference UID consistency for geometric accuracy

        Structure data must maintain sub-millimeter accuracy to ensure proper
        dose calculation and treatment delivery verification.

    Attributes:
        SOP_CLASS_UID: RT Structure Set Storage SOP Class UID (1.2.840.10008.*******.1.481.3)
        REQUIRED_MODULES: List of mandatory DICOM modules for RT Structure Set IOD
        OPTIONAL_MODULES: List of optional modules that enhance clinical functionality
    """

    # RT Structure Set Storage SOP Class UID from DICOM Part 6
    SOP_CLASS_UID = RTStructureSetStorage

    # Required modules per DICOM Part 3 C.8.8.5
    REQUIRED_MODULES = [
        "Patient",
        "General Study", 
        "RT Series",
        "Frame of Reference",
        "General Equipment",
        "RT Structure Set",
        "Structure Set",
        "ROI Contour",
        "RT ROI Observations",
        "SOP Common",
    ]

    # Optional modules that enhance clinical functionality
    OPTIONAL_MODULES = [
        "Clinical Trial Subject",
        "Clinical Trial Study", 
        "Clinical Trial Series",
        "Approval",
    ]

    # Standard clinical colors for common structure types
    CLINICAL_COLORS = {
        # Target volumes - warm colors for visibility
        'PTV': (255, 0, 0),                          # Red
        'CTV': (255, 128, 0),                        # Orange  
        'GTV': (255, 255, 0),                        # Yellow
        'ITV': (255, 192, 203),                      # Pink
        'CTV1': (255, 0, 0),                         # Red
        'PTV1': (128, 0, 0),                         # Maroon
        'CTV2': (128, 0, 128),                       # Purple
        'PTV2': (128, 0, 255),                       # Dark Purple
        'CTV3': (0, 255, 255),                       # Cyan
        'PTV3': (0, 0, 255),                         # Blue
        'CTV4': (0, 255, 128),                       # Light Blue
        'PTV4': (0, 0, 128),                         # Dark Blue
        
        # Organs at risk - cool colors for contrast
        'Bladder': (255, 255, 0),                    # Yellow
        'BoneMarrow': (34, 139, 34),                 # Forest
        'Bowel': (255, 192, 203),                    # Pink
        'Bowel_Large': (128, 128, 128),              # Grey
        'Bowel_Small': (165, 42, 42),                # Brown
        'BrachialPlexus': (34, 139, 34),             # Forest
        'Brain': (0, 255, 255),                      # Aqua
        'Brainstem': (255, 255, 0),                  # Yellow
        'Brainstem_PRV03': (240, 230, 140),          # Khaki
        'Breast_Contra': (70, 130, 180),             # Steel Blue
        'Breast_Ipsi': (128, 128, 0),                # Olive
        'Breast-PTV': (255, 255, 224),               # Light Yellow
        'BronchialTree': (173, 216, 230),            # Light Blue
        'BronchialTree_PRV20': (255, 200, 124),      # Light Orange (approx)
        'BronchialTree+Trachea': (255, 255, 0),      # Yellow
        'Bronchus': (255, 165, 0),                   # Orange
        'Carina': (154, 205, 50),                    # Yellow Green
        'Carotids': (34, 139, 34),                   # Forest
        'Carotids_PRV03': (240, 230, 140),           # Khaki
        'CaudaEquina': (144, 238, 144),              # Light Green
        'Cavity': (0, 255, 255),                     # Cyan
        'Chestwall': (0, 128, 0),                    # Green
        'Cochlea_L': (230, 230, 250),                # Lavender
        'Cochlea_R': (128, 128, 0),                  # Olive
        'Colon': (70, 130, 180),                     # Steel Blue
        'Duodenum': (34, 139, 34),                   # Forest
        'Esophagus': (0, 255, 255),                  # Aqua
        'Esophagus_Cervical': (0, 255, 255),         # Aqua
        'Eye_L': (255, 200, 124),                    # Light Orange (approx)
        'Eye_R': (165, 42, 42),                      # Brown
        'Femur_Heads': (0, 128, 128),                # Teal
        'Femur_L': (0, 0, 255),                      # Blue
        'Femur_R': (0, 255, 255),                    # Cyan
        'Genitals': (230, 230, 250),                 # Lavender
        'GreatVessels': (255, 245, 238),             # Seashell
        'Heart': (255, 99, 71),                      # Tomato
        'Heart_PRV03': (128, 0, 0),                  # Maroon
        'Hippocampus': (0, 255, 255),                # Cyan
        'Iliac_Crests': (154, 205, 50),              # Yellow Green
        'Jejunum': (240, 230, 140),                  # Khaki
        'Jejunum-Ileum': (240, 230, 140),            # Khaki
        'Kidney_L': (255, 165, 0),                   # Orange
        'Kidney_R': (128, 128, 0),                   # Olive
        'Kidneys': (230, 230, 250),                  # Lavender
        'Kidneys_Cortex': (64, 0, 64),               # Dark Purple (approx)
        'Kidneys_Hilum': (173, 216, 230),            # Light Blue
        'Lacrimal_L': (173, 216, 230),               # Light Blue
        'Lacrimal_R': (154, 205, 50),                # Yellow Green
        'Larynx': (255, 192, 203),                   # Pink
        'Lens_L': (240, 230, 140),                   # Khaki
        'Lens_R': (70, 130, 180),                    # Steel Blue
        'Lips': (0, 255, 255),                       # Aqua
        'Liver': (255, 255, 0),                      # Yellow
        'Liver-GTV': (34, 139, 34),                  # Forest
        'Lung_Contra': (0, 128, 128),                # Teal
        'Lung_Ipsi': (34, 139, 34),                  # Forest
        'Lung_L': (240, 230, 140),                   # Khaki
        'Lung_R': (255, 200, 124),                   # Light Orange (approx)
        'Lungs': (128, 0, 0),                        # Maroon
        'Lungs-CTV': (0, 255, 255),                  # Cyan
        'Mandible': (255, 165, 0),                   # Orange
        'Mandible_Uninvolved': (230, 230, 250),      # Lavender
        'Neck_Nodes': (255, 192, 203),               # Pink
        'OpticChiasm': (34, 139, 34),                # Forest
        'OpticChiasm_PRV03': (128, 0, 128),          # Purple
        'OpticNerve_L': (154, 205, 50),              # Yellow Green
        'OpticNerve_R': (0, 255, 255),               # Aqua
        'OpticNrv_PRV03_L': (255, 255, 0),           # Yellow
        'OpticNrv_PRV03_R': (0, 128, 128),           # Teal
        'Oral_Cavity': (154, 205, 50),               # Yellow Green
        'Parotid_L': (0, 128, 128),                  # Teal
        'Parotid_R': (255, 255, 0),                  # Yellow
        'Parotids': (0, 255, 255),                   # Aqua
        'PartialCord': (255, 165, 0),                # Orange
        'PenileBulb': (255, 165, 0),                 # Orange
        'Pharyngeal_Constrictors': (240, 230, 140),  # Khaki
        'Post_Neck': (144, 238, 144),                # Light Green
        'Prostate': (0, 255, 255),                   # Cyan
        'Rectum': (165, 42, 42),                     # Brown
        'Rib': (255, 192, 203),                      # Pink
        'SacralPlexus': (154, 205, 50),              # Yellow Green
        'Scar': (255, 200, 124),                     # Light Orange (approx)
        'SeminalVesicle': (0, 128, 0),               # Green
        'SeminalVesicle_NonTarget': (34, 139, 34),   # Forest
        'SpaceOAR': (230, 230, 250),                 # Lavender
        'SpinalCanal': (34, 139, 34),                # Forest
        'SpinalCord': (0, 128, 0),                   # Green
        'SpinalCord_PRV05': (0, 128, 128),           # Teal
        'Stomach': (34, 139, 34),                    # Forest
        'Submandibular_Uninvolved': (128, 128, 0),   # Olive
        'Trachea': (255, 255, 0),                    # Yellow
        'Urethra': (0, 128, 128),                    # Teal
        'Urethra_PRV02': (70, 130, 180),             # Steel Blue
        
        # Fiducials and Markers
        'bbs': (255, 255, 255),                      # White
        'Clips': (128, 0, 128),                      # Purple
        'Clips_3mm': (64, 0, 64),                    # Dark Purple
        'Fiducials': (128, 0, 128),                  # Purple
        'Outline': (0, 255, 255),                    # Cyan
        'Override': (0, 255, 255),                   # Aqua (same as Cyan here for consistency)

        # Control and Planning Structures
        'Block_Margin': (128, 128, 128),             # Grey
        'Body': (255, 218, 185),                     # Peach

        # Avoidance Structures
        'Body-PTV': (128, 128, 0),                   # Olive
        'Body-PTV06': (0, 255, 255),                 # Aqua
        'Body-PTV10': (0, 255, 255),                 # Aqua
        'Body-PTV20': (230, 230, 250),               # Lavender
        'Brain-PTV': (0, 128, 128),                  # Teal
        'Skin_3mm': (128, 128, 128),                 # Grey
        'Skin_Dose': (0, 255, 255),                  # Cyan
        'Skin_Dose_2mm': (128, 128, 128),            # Grey
    }

    @classmethod
    def create_dataset(
        cls,
        structures: List[Dict[str, Any]],
        reference_frame_uid: str,
        referenced_ct_sop_instance_uids: Optional[List[str]] = None,
        **kwargs,
    ) -> Dataset:
        """Create RT Structure Set dataset with proper IOD structure.

        Args:
            structures: List of structure dictionaries, each containing:
                - 'name': Structure name (str)
                - 'number': ROI number (int, 1-based)
                - 'color': RGB color tuple (r, g, b) with values 0-255
                - 'contours': List of contour sequences (optional)
                - 'type': RT ROI Interpreted Type ('ORGAN', 'PTV', 'CTV', etc.)
                - 'algorithm': ROI Generation Algorithm ('MANUAL', 'AUTOMATIC', etc.)
            reference_frame_uid: Frame of Reference UID linking to planning CT
            referenced_ct_sop_instance_uids: List of referenced CT SOP Instance UIDs
                for establishing geometric relationships
            **kwargs: Additional DICOM elements to include in dataset

        Returns:
            Complete DICOM dataset conforming to RT Structure Set IOD specification

        Raises:
            TemplateError: If structure definitions are invalid, geometric parameters
                are inconsistent, or required DICOM elements cannot be created

        Clinical Notes:
            The RT Structure Set template ensures proper clinical workflow integration:

            **Structure Organization**: ROI numbers must be unique and typically
            start from 1. Structure names should follow institutional conventions
            for automatic recognition in planning systems.

            **Geometric Consistency**: Frame of Reference UID must match the
            planning CT to ensure spatial alignment. Referenced SOP Instance UIDs
            establish explicit relationships with CT slices.

            **Clinical Interpretation**: RT ROI Interpreted Type helps planning
            systems categorize structures for dose constraints and optimization.

            **Visual Identification**: RGB colors enable consistent structure
            display across different planning system vendors.

        Examples:
            Create basic structure set with common ROIs:

            >>> structures = [
            ...     {
            ...         'name': 'PTV_7000',
            ...         'number': 1,
            ...         'color': (255, 0, 0),
            ...         'type': 'PTV',
            ...         'algorithm': 'MANUAL'
            ...     },
            ...     {
            ...         'name': 'Bladder',
            ...         'number': 2, 
            ...         'color': (0, 0, 255),
            ...         'type': 'ORGAN',
            ...         'algorithm': 'MANUAL'
            ...     }
            ... ]
            >>> dataset = RTStructureSetTemplate.create_dataset(
            ...     structures=structures,
            ...     reference_frame_uid='1.2.3.4.5.6.7',
            ...     referenced_ct_sop_instance_uids=['1.2.3.4.5.6.8', '1.2.3.4.5.6.9']
            ... )
            >>> print(dataset.SOPClassUID)
            1.2.840.10008.*******.1.481.3

            Create structure set with clinical color defaults:

            >>> structures = [
            ...     {
            ...         'name': 'PTV',
            ...         'number': 1,
            ...         'color': cls.CLINICAL_COLORS['PTV'],
            ...         'type': 'PTV',
            ...         'algorithm': 'MANUAL'
            ...     }
            ... ]
            >>> dataset = RTStructureSetTemplate.create_dataset(
            ...     structures=structures,
            ...     reference_frame_uid=frame_uid
            ... )
        """
        # Validate input parameters
        if not structures:
            raise TemplateError(
                "At least one structure must be provided for RT Structure Set",
                modality="RTSTRUCT",
                suggestions=[
                    "Provide a list of structure dictionaries with required fields",
                    "Each structure needs: name, number, color, type, algorithm",
                    "Use RTStructureSet.from_masks() for automatic structure creation",
                ],
                clinical_context={
                    "provided_structures": len(structures),
                    "minimum_required": 1,
                },
            )

        # Validate structure definitions
        roi_numbers = set()
        for i, structure in enumerate(structures):
            if 'name' not in structure:
                raise TemplateError(
                    f"Structure {i} missing required 'name' field",
                    modality="RTSTRUCT",
                    suggestions=[
                        "Provide structure name as string (e.g., 'PTV', 'Bladder')",
                        "Use clinical naming conventions for TPS recognition",
                        "Names should be unique within the structure set",
                    ],
                )

            if 'number' not in structure:
                raise TemplateError(
                    f"Structure '{structure.get('name', i)}' missing required 'number' field",
                    modality="RTSTRUCT",
                    suggestions=[
                        "Provide unique ROI number starting from 1",
                        "ROI numbers must be positive integers",
                        "Numbers are used for internal DICOM references",
                    ],
                )

            roi_num = structure['number']
            if roi_num in roi_numbers:
                raise TemplateError(
                    f"Duplicate ROI number: {roi_num}",
                    modality="RTSTRUCT",
                    suggestions=[
                        "ROI numbers must be unique within structure set",
                        "Use consecutive integers starting from 1",
                        "Check for duplicate assignments in structure list",
                    ],
                    clinical_context={
                        "duplicate_number": roi_num,
                        "structure_name": structure.get('name'),
                    },
                )
            roi_numbers.add(roi_num)

            if 'color' not in structure:
                raise TemplateError(
                    f"Structure '{structure['name']}' missing required 'color' field",
                    modality="RTSTRUCT",
                    suggestions=[
                        "Provide RGB color as tuple (r, g, b) with values 0-255",
                        "Use CLINICAL_COLORS dictionary for standard colors",
                        "Colors help distinguish structures in planning systems",
                    ],
                )

        # Create base dataset
        dataset = Dataset()

        # SOP Common Module (C.12.1) - Required
        dataset.SOPClassUID = cls.SOP_CLASS_UID
        uid_generator = DefaultUIDGenerator.create_default_generator()
        dataset.SOPInstanceUID = uid_generator.generate_sop_instance_uid()

        # RT Series Module (C.8.8.1) - Required for RT objects
        dataset.Modality = "RTSTRUCT"
        dataset.SeriesDescription = kwargs.get("SeriesDescription", "RT Structure Set")

        # Frame of Reference Module (C.7.4.1) - Required
        dataset.FrameOfReferenceUID = reference_frame_uid
        dataset.PositionReferenceIndicator = ""

        # RT Structure Set Module (C.8.8.5) - Required
        dataset.StructureSetLabel = kwargs.get("StructureSetLabel", "RT_STRUCT")
        dataset.StructureSetName = kwargs.get("StructureSetName", "RT Structure Set")
        dataset.StructureSetDescription = kwargs.get(
            "StructureSetDescription", 
            "RT Structure Set created by pyrt-dicom"
        )
        
        # Structure Set Date/Time
        from datetime import datetime
        now = datetime.now()
        dataset.StructureSetDate = now.strftime("%Y%m%d")
        dataset.StructureSetTime = now.strftime("%H%M%S.%f")[:10]

        # Referenced Frame of Reference Sequence (C.*******)
        ref_for_sequence = Sequence()
        ref_for_item = Dataset()
        ref_for_item.FrameOfReferenceUID = reference_frame_uid
        
        # RT Referenced Study Sequence
        rt_ref_study_seq = Sequence()
        rt_ref_study_item = Dataset()
        rt_ref_study_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1"  # Study Root Query/Retrieve
        rt_ref_study_item.ReferencedSOPInstanceUID = uid_generator.generate_study_instance_uid()
        
        # RT Referenced Series Sequence  
        rt_ref_series_seq = Sequence()
        rt_ref_series_item = Dataset()
        rt_ref_series_item.SeriesInstanceUID = uid_generator.generate_series_instance_uid()
        
        # Contour Image Sequence (if CT references provided)
        if referenced_ct_sop_instance_uids:
            contour_image_seq = Sequence()
            for ct_sop_uid in referenced_ct_sop_instance_uids:
                contour_image_item = Dataset()
                contour_image_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
                contour_image_item.ReferencedSOPInstanceUID = ct_sop_uid
                contour_image_seq.append(contour_image_item)
            rt_ref_series_item.ContourImageSequence = contour_image_seq
        
        rt_ref_study_item.RTReferencedSeriesSequence = Sequence([rt_ref_series_item])
        ref_for_item.RTReferencedStudySequence = Sequence([rt_ref_study_item])
        ref_for_sequence.append(ref_for_item)
        dataset.ReferencedFrameOfReferenceSequence = ref_for_sequence

        # Structure Set ROI Sequence - Define the ROIs
        struct_set_roi_seq = Sequence()
        roi_contour_seq = Sequence()
        rt_roi_observations_seq = Sequence()

        for structure in structures:
            # Structure Set ROI Sequence Item
            roi_item = Dataset()
            roi_item.ROINumber = structure['number']
            roi_item.ReferencedFrameOfReferenceUID = reference_frame_uid
            roi_item.ROIName = structure['name']
            roi_item.ROIDescription = structure.get('description', structure['name'])
            roi_item.ROIGenerationAlgorithm = structure.get('algorithm', 'MANUAL')
            struct_set_roi_seq.append(roi_item)

            # ROI Contour Sequence Item
            roi_contour_item = Dataset()
            roi_contour_item.ReferencedROINumber = structure['number']
            
            # Set ROI display color
            color = structure['color']
            if isinstance(color, (list, tuple)) and len(color) >= 3:
                roi_contour_item.ROIDisplayColor = [int(color[0]), int(color[1]), int(color[2])]
            else:
                # Default to red if color is invalid
                roi_contour_item.ROIDisplayColor = [255, 0, 0]

            # Contour Sequence (initially empty - will be populated during mask-to-contour conversion)
            if 'contours' in structure and structure['contours']:
                roi_contour_item.ContourSequence = Sequence(structure['contours'])
            else:
                roi_contour_item.ContourSequence = Sequence()

            roi_contour_seq.append(roi_contour_item)

            # RT ROI Observations Sequence Item
            roi_obs_item = Dataset()
            roi_obs_item.ObservationNumber = structure['number']
            roi_obs_item.ReferencedROINumber = structure['number']
            roi_obs_item.RTROIInterpretedType = structure.get('type', 'ORGAN')
            roi_obs_item.ROIInterpreter = structure.get('interpreter', '')
            rt_roi_observations_seq.append(roi_obs_item)

        # Assign sequences to dataset
        dataset.StructureSetROISequence = struct_set_roi_seq
        dataset.ROIContourSequence = roi_contour_seq
        dataset.RTROIObservationsSequence = rt_roi_observations_seq

        # Add any additional elements from kwargs
        for key, value in kwargs.items():
            if key not in [
                "SeriesDescription",
                "StructureSetLabel", 
                "StructureSetName",
                "StructureSetDescription",
            ]:
                try:
                    setattr(dataset, key, value)
                except Exception:
                    # Skip invalid DICOM elements
                    continue

        return dataset

    @classmethod
    def get_clinical_color(cls, structure_name: str) -> Tuple[int, int, int]:
        """Get clinical standard color for common structure names.

        Args:
            structure_name: Structure name to look up color for

        Returns:
            RGB color tuple (r, g, b) with values 0-255

        Clinical Notes:
            Uses standardized colors that are commonly recognized across
            different treatment planning systems for consistency.
        """
        # Normalize structure name for lookup
        name_upper = structure_name.upper()
        
        # Check for exact matches first (case-insensitive)
        for standard_name in cls.CLINICAL_COLORS:
            if standard_name.upper() == name_upper:
                return cls.CLINICAL_COLORS[standard_name]
        
        # Check for partial matches for common patterns
        for standard_name, color in cls.CLINICAL_COLORS.items():
            if standard_name in name_upper or name_upper in standard_name:
                return color
                
        # Default colors based on common prefixes
        if name_upper.startswith('PTV'):
            return cls.CLINICAL_COLORS['PTV']
        elif name_upper.startswith('CTV'):
            return cls.CLINICAL_COLORS['CTV'] 
        elif name_upper.startswith('ITV'):
            return cls.CLINICAL_COLORS['ITV']
        elif name_upper.startswith('GTV'):
            return cls.CLINICAL_COLORS['GTV']
        
        # Default to red if no match found
        return (255, 0, 0)

    @classmethod
    def validate_compliance(cls, dataset: Dataset) -> List[str]:
        """Validate dataset compliance with RT Structure Set IOD.

        Args:
            dataset: DICOM dataset to validate

        Returns:
            List of validation error messages. Empty list indicates compliance

        Clinical Notes:
            Validation ensures that created RT Structure Sets will load properly
            in treatment planning systems and maintain geometric relationships
            with planning CT images.
        """
        errors = []

        # Check SOP Class UID
        if not hasattr(dataset, "SOPClassUID"):
            errors.append("Missing required SOPClassUID")
        elif dataset.SOPClassUID != cls.SOP_CLASS_UID:
            errors.append(
                f"Invalid SOPClassUID: expected {cls.SOP_CLASS_UID}, got {dataset.SOPClassUID}"
            )

        # Check required RT Structure Set elements
        required_elements = [
            "Modality",
            "StructureSetLabel",
            "StructureSetROISequence",
            "ROIContourSequence", 
            "RTROIObservationsSequence",
            "ReferencedFrameOfReferenceSequence",
        ]

        for element in required_elements:
            if not hasattr(dataset, element):
                errors.append(f"Missing required element: {element}")

        # Validate modality
        if hasattr(dataset, "Modality") and dataset.Modality != "RTSTRUCT":
            errors.append(f"Invalid modality for RT Structure Set: {dataset.Modality}")

        # Validate sequence consistency
        if (hasattr(dataset, "StructureSetROISequence") and 
            hasattr(dataset, "ROIContourSequence") and
            hasattr(dataset, "RTROIObservationsSequence")):
            
            roi_seq_len = len(dataset.StructureSetROISequence)
            contour_seq_len = len(dataset.ROIContourSequence)
            obs_seq_len = len(dataset.RTROIObservationsSequence)
            
            if not (roi_seq_len == contour_seq_len == obs_seq_len):
                errors.append(
                    f"Sequence length mismatch: StructureSetROI={roi_seq_len}, "
                    f"ROIContour={contour_seq_len}, RTROIObservations={obs_seq_len}"
                )

        # Validate ROI numbers are unique and consistent
        if hasattr(dataset, "StructureSetROISequence"):
            roi_numbers = []
            for roi_item in dataset.StructureSetROISequence:
                if hasattr(roi_item, "ROINumber"):
                    roi_num = roi_item.ROINumber
                    if roi_num in roi_numbers:
                        errors.append(f"Duplicate ROI number: {roi_num}")
                    else:
                        roi_numbers.append(roi_num)

        return errors