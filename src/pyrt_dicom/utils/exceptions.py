"""
Custom exception hierarchy for pyrt-dicom.

Provides RT-specific exceptions with clinical context, helpful error messages,
actionable recovery suggestions, and comprehensive debugging information
for medical physics workflows.

## Error Handling Best Practices

```python
import pyrt_dicom as prt
from pyrt_dicom.utils.exceptions import *

# 1. Comprehensive error handling in RT workflows
try:
    # Create RT Structure with validation
    rt_struct = prt.RTStructureSet.from_masks(
        ct_reference=ct_data,
        masks={'PTV': ptv_mask, 'OAR': oar_mask},
        patient_info={'PatientID': 'RT_001'}
    )
    rt_struct.save('structures.dcm')

except ValidationError as e:
    print(f"Clinical validation failed: {e}")
    print(f"Validation type: {e.clinical_context.get('validation_type')}")

    # Handle specific validation failures
    if 'parameter_name' in e.clinical_context:
        param = e.clinical_context['parameter_name']
        current = e.clinical_context.get('current_value')
        valid_range = e.clinical_context.get('valid_range')
        print(f"Parameter '{param}' value {current} outside range {valid_range}")

    # Apply suggested fixes
    for i, suggestion in enumerate(e.suggestions, 1):
        print(f"{i}. {suggestion}")

except CoordinateSystemError as e:
    print(f"Coordinate system error: {e}")

    # Handle Frame of Reference issues
    if 'frame_of_reference_uid' in e.clinical_context:
        frame_uid = e.clinical_context['frame_of_reference_uid']
        print(f"Frame of Reference UID: {frame_uid}")

    # Handle patient position conflicts
    if 'patient_position' in e.clinical_context:
        position = e.clinical_context['patient_position']
        print(f"Patient position: {position}")
        print(f"Typical values: {e.clinical_context.get('typical_values')}")

except UIDGenerationError as e:
    print(f"UID generation failed: {e}")
    print(f"UID format requirements: {e.clinical_context.get('uid_format')}")
    print(f"Max length: {e.clinical_context.get('max_length')} characters")

except DicomCreationError as e:
    print(f"DICOM creation failed: {e}")

    # Handle missing elements
    if 'missing_elements' in e.clinical_context:
        missing = e.clinical_context['missing_elements']
        print(f"Missing DICOM elements: {missing}")

    # Handle file I/O issues
    if 'file_path' in e.clinical_context:
        file_path = e.clinical_context['file_path']
        print(f"File operation failed: {file_path}")

except TemplateError as e:
    print(f"DICOM template error: {e}")

    # Handle modality-specific issues
    if 'modality' in e.clinical_context:
        modality = e.clinical_context['modality']
        print(f"Modality: {modality}")

    # Handle missing attributes
    if 'missing_attributes' in e.clinical_context:
        missing_attrs = e.clinical_context['missing_attributes']
        print(f"Missing required attributes: {missing_attrs}")

except PyrtDicomError as e:
    # Generic pyrt-dicom error handling
    print(f"pyrt-dicom error: {e}")
    if e.dicom_reference:
        print(f"DICOM standard reference: {e.dicom_reference}")

finally:
    # Cleanup and logging
    print("RT DICOM operation completed (with or without errors)")
```

## Cross-References

**Related Modules**:
- :mod:`~pyrt_dicom.validation` - Parameter validation that raises these exceptions
- :mod:`~pyrt_dicom.core.base` - BaseDicomCreator error handling patterns
- :mod:`~pyrt_dicom.utils.logging` - Clinical audit logging for error tracking

**See Also**:
- :class:`~pyrt_dicom.validation.patient.PatientInfoValidator` - Patient data validation
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Coordinate error sources
- :class:`~pyrt_dicom.uid_generation.generators.UIDGenerator` - UID generation errors
"""

from typing import List, Optional, Dict, Any, Union


class PyrtDicomError(Exception):
    """
    Base exception class for all pyrt-dicom errors.

    Provides enhanced error reporting with actionable suggestions and clinical context
    suitable for medical physics workflows.

    Common scenarios:
    - Generic pyrt-dicom operation failures
    - Unexpected errors during DICOM processing
    - System-level failures not covered by specific exceptions

    Attributes
    ----------
    suggestions : List[str]
        Actionable suggestions for resolving the error
    clinical_context : Dict[str, Any]
        Clinical context information (units, ranges, standards)
    dicom_reference : str, optional
        Reference to relevant DICOM standard section
    """

    def __init__(
        self,
        message: str,
        *args,  # Allow positional arguments for backward compatibility
        suggestions: Optional[List[str]] = None,
        clinical_context: Optional[Dict[str, Any]] = None,
        dicom_reference: Optional[str] = None,
        **kwargs,
    ) -> None:
        """
        Initialize base pyrt-dicom error.

        Parameters
        ----------
        message : str
            Primary error message describing what went wrong
        suggestions : List[str], optional
            Actionable suggestions for resolving the error
        clinical_context : Dict[str, Any], optional
            Clinical context (e.g., valid ranges, units, typical values)
        dicom_reference : str, optional
            Reference to DICOM standard (e.g., "PS 3.3, C.7.6.1")
        **kwargs
            Additional context information
        """
        super().__init__(message, *args)
        self.suggestions = suggestions or []
        self.clinical_context = clinical_context or {}
        self.dicom_reference = dicom_reference
        self.additional_context = kwargs

    def add_suggestion(self, suggestion: str) -> None:
        """Add an actionable suggestion to the error."""
        self.suggestions.append(suggestion)

    def add_clinical_context(self, key: str, value: Any) -> None:
        """Add clinical context information."""
        self.clinical_context[key] = value

    def __str__(self) -> str:
        """Enhanced string representation with context and suggestions."""
        # For backward compatibility, show enhanced context only if explicitly provided
        show_enhanced = (
            len(self.suggestions) > 0
            or len(self.clinical_context) > 0
            or self.dicom_reference is not None
        )

        if not show_enhanced:
            return super().__str__()

        parts = [super().__str__()]

        # Add clinical context if available
        if self.clinical_context:
            context_parts = []
            for key, value in self.clinical_context.items():
                if (
                    key == "valid_range"
                    and isinstance(value, (tuple, list))
                    and len(value) == 2
                ):
                    context_parts.append(f"Valid range: {value[0]} - {value[1]}")
                elif key == "units":
                    context_parts.append(f"Units: {value}")
                elif key == "typical_values":
                    context_parts.append(f"Typical values: {value}")
                elif key == "current_value":
                    context_parts.append(f"Current value: {value}")
                else:
                    context_parts.append(f"{key}: {value}")

            if context_parts:
                parts.append(f"Clinical Context: {', '.join(context_parts)}")

        # Add DICOM reference if available
        if self.dicom_reference:
            parts.append(f"DICOM Reference: {self.dicom_reference}")

        # Add actionable suggestions
        if self.suggestions:
            parts.append("Suggestions for resolution:")
            for i, suggestion in enumerate(self.suggestions, 1):
                parts.append(f"  {i}. {suggestion}")

        return "\n".join(parts)


class DicomCreationError(PyrtDicomError):
    """
    Raised when DICOM file creation fails.

    This covers errors in the DICOM creation process, including invalid
    data structures, missing required elements, or pydicom integration issues.

    Common scenarios:
    - Missing required DICOM elements
    - Invalid data types for DICOM tags
    - File I/O errors during saving
    - pydicom integration issues
    """

    def __init__(
        self,
        message: str,
        *args,  # Allow positional arguments for backward compatibility
        missing_elements: Optional[List[str]] = None,
        invalid_data_types: Optional[Dict[str, str]] = None,
        file_path: Optional[str] = None,
        **kwargs,
    ) -> None:
        # Extract parameters from kwargs to avoid conflicts
        additional_context = kwargs.pop("clinical_context", {})
        user_suggestions = kwargs.pop("suggestions", [])
        user_dicom_reference = kwargs.pop("dicom_reference", None)

        # Check if any enhanced features are being used
        enhanced_features_used = (
            missing_elements is not None
            or invalid_data_types is not None
            or file_path is not None
            or additional_context
            or user_suggestions
            or user_dicom_reference is not None
        )

        # Generate context-aware suggestions only if enhanced features are used
        suggestions = list(user_suggestions) if user_suggestions else []
        clinical_context = {}
        dicom_reference = user_dicom_reference

        if enhanced_features_used:
            clinical_context.update(additional_context)
            if dicom_reference is None:
                dicom_reference = "PS 3.5 (Data Structures and Encoding)"

            if missing_elements:
                suggestions.extend(
                    [
                        "Ensure all required DICOM elements are populated before creation",
                        "Check DICOM IOD specification for your modality",
                    ]
                )
                clinical_context["missing_elements"] = missing_elements

            if invalid_data_types:
                suggestions.extend(
                    [
                        "Verify data types match DICOM VR (Value Representation) requirements",
                        "Convert numeric values to appropriate Python types (int, float, str)",
                    ]
                )
                clinical_context["invalid_data_types"] = invalid_data_types

            if file_path:
                suggestions.extend(
                    [
                        "Verify output directory exists and is writable",
                        "Check available disk space for DICOM file",
                    ]
                )
                clinical_context["file_path"] = file_path

            if not suggestions:
                suggestions = [
                    "Verify all input data is properly formatted for DICOM creation",
                    "Check pydicom compatibility with your data structures",
                    "Enable debug logging to see detailed error information",
                ]

        super().__init__(
            message,
            *args,
            suggestions=suggestions,
            clinical_context=clinical_context,
            dicom_reference=dicom_reference,
            **kwargs,
        )


class ValidationError(PyrtDicomError):
    """
    Raised when validation of clinical or technical parameters fails.

    Used for both clinical validation (dose ranges, geometric constraints)
    and DICOM standard compliance validation.

    Common scenarios:
    - Clinical values outside reasonable ranges
    - Geometric inconsistencies
    - DICOM compliance violations
    - Patient safety constraint violations
    """

    def __init__(
        self,
        message: str,
        *args,
        parameter_name: Optional[str] = None,
        current_value: Optional[Union[str, float, int]] = None,
        valid_range: Optional[tuple] = None,
        units: Optional[str] = None,
        validation_type: str = "clinical",
        **kwargs,
    ) -> None:
        # Extract parameters from kwargs to avoid conflicts
        additional_context = kwargs.pop("clinical_context", {})
        user_suggestions = kwargs.pop("suggestions", [])
        user_dicom_reference = kwargs.pop("dicom_reference", None)

        # ValidationError always provides enhanced features and clinical guidance
        suggestions = list(user_suggestions) if user_suggestions else []
        clinical_context = {}
        dicom_ref = user_dicom_reference or "PS 3.3 (Information Object Definitions)"

        clinical_context.update(additional_context)

        if parameter_name:
            clinical_context["parameter_name"] = parameter_name

        if current_value is not None:
            clinical_context["current_value"] = current_value

        if valid_range:
            clinical_context["valid_range"] = valid_range
            suggestions.append(
                f"Adjust value to be within valid range: {valid_range[0]} - {valid_range[1]}"
            )

        if units:
            clinical_context["units"] = units

        # Validation-type specific suggestions
        if validation_type == "clinical":
            suggestions.extend(
                [
                    "Review clinical protocols for acceptable parameter ranges",
                    "Consult medical physics guidelines for your institution",
                    "Verify data entry and unit conversions are correct",
                ]
            )
            clinical_context["validation_type"] = "Clinical Safety"
        elif validation_type == "geometric":
            suggestions.extend(
                [
                    "Check coordinate system consistency across all objects",
                    "Verify image orientation and position parameters",
                    "Ensure structure boundaries are within image bounds",
                ]
            )
            dicom_ref = "PS 3.3, C.7.6.2 (Image Plane Module)"
            clinical_context["validation_type"] = "Geometric Consistency"
        elif validation_type == "dicom_compliance":
            suggestions.extend(
                [
                    "Review DICOM standard requirements for your modality",
                    "Check VR (Value Representation) constraints for DICOM tags",
                    "Validate required vs. optional element presence",
                ]
            )
            clinical_context["validation_type"] = "DICOM Standard Compliance"

        if not suggestions:
            suggestions = [
                "Review input parameters against expected ranges",
                "Check data source for potential errors or corruption",
                "Enable validation logging to see detailed constraint violations",
            ]

        super().__init__(
            message,
            *args,
            suggestions=suggestions,
            clinical_context=clinical_context,
            dicom_reference=dicom_ref,
            **kwargs,
        )


class CoordinateSystemError(PyrtDicomError):
    """
    Raised when coordinate system transformations or validations fail.

    This includes frame of reference inconsistencies, geometric transformation
    errors, and spatial relationship validation failures.

    Common scenarios:
    - Frame of Reference UID mismatches
    - Image orientation inconsistencies
    - Patient position conflicts
    - Coordinate transformation failures
    """

    def __init__(
        self,
        message: str,
        *args,  # Allow positional arguments for backward compatibility
        frame_of_reference_uid: Optional[str] = None,
        patient_position: Optional[str] = None,
        coordinate_mismatch_mm: Optional[float] = None,
        **kwargs,
    ) -> None:
        # Extract clinical_context from kwargs to avoid conflicts
        additional_context = kwargs.pop("clinical_context", {})

        # Check if any enhanced features are being used
        enhanced_features_used = (
            frame_of_reference_uid is not None
            or patient_position is not None
            or coordinate_mismatch_mm is not None
            or additional_context
            or "suggestions" in kwargs
            or "dicom_reference" in kwargs
        )

        suggestions = []
        clinical_context = {}
        dicom_reference = None

        if enhanced_features_used:
            suggestions = [
                "Ensure all related DICOM objects use the same Frame of Reference UID",
                "Verify patient positioning is consistent across all images",
                "Check image orientation vectors for mathematical validity",
            ]

            clinical_context.update(additional_context)

            if frame_of_reference_uid:
                clinical_context["frame_of_reference_uid"] = frame_of_reference_uid
                suggestions.append(
                    "Use reference CT image to establish consistent coordinate system"
                )

            if patient_position:
                clinical_context["patient_position"] = patient_position
                clinical_context["typical_values"] = (
                    "HFS (Head First Supine), HFP, FFS, FFP"
                )

            if coordinate_mismatch_mm is not None:
                clinical_context["coordinate_mismatch_mm"] = coordinate_mismatch_mm
                clinical_context["tolerance_mm"] = 1.0
                if coordinate_mismatch_mm > 5.0:
                    suggestions.insert(
                        0,
                        "Large coordinate mismatch detected - verify image registration",
                    )
                else:
                    suggestions.append(
                        "Small mismatch may be acceptable for clinical use"
                    )

            suggestions.extend(
                [
                    "Use coordinate transformation utilities to align different coordinate systems",
                    "Verify geometric parameters match between CT and RT structure/dose objects",
                ]
            )

            dicom_reference = "PS 3.3, C.7.6.2 (Image Plane Module) and C.8.8.1 (RT Structure Set Module)"

        super().__init__(
            message,
            *args,
            suggestions=suggestions,
            clinical_context=clinical_context,
            dicom_reference=dicom_reference,
            **kwargs,
        )


class UIDGenerationError(PyrtDicomError):
    """
    Raised when UID generation or management fails.

    Covers UID format errors, uniqueness violations, and UID relationship
    consistency issues critical for RT treatment traceability and clinical safety.

    Common scenarios:
    - UID format violations (invalid characters, length)
    - Duplicate UID generation affecting patient safety
    - Missing UID relationships in RT workflow
    - Root UID configuration issues for clinical systems

    Clinical Notes:
        UID generation is critical for RT treatment safety as UIDs provide
        unique identification of patient data, treatment plans, and dose
        calculations. Proper UID management ensures traceability throughout
        the radiation therapy workflow.
    """

    def __init__(
        self,
        message: str,
        *args,  # Allow positional arguments for backward compatibility
        uid_value: Optional[str] = None,
        uid_type: Optional[str] = None,
        root_uid: Optional[str] = None,
        **kwargs,
    ) -> None:
        # Extract parameters from kwargs to avoid conflicts
        additional_context = kwargs.pop("clinical_context", {})
        user_suggestions = kwargs.pop("suggestions", [])
        user_dicom_reference = kwargs.pop("dicom_reference", None)

        # UIDGenerationError always provides technical guidance - this is its purpose
        suggestions = list(user_suggestions) if user_suggestions else []
        clinical_context = {}
        dicom_reference = user_dicom_reference

        # Always provide UID generation guidance
        if not suggestions:
            suggestions = [
                "Verify UID generator configuration and root UID settings",
                "Ensure UID uniqueness across all generated objects",
                "Check UID format against DICOM standard requirements",
            ]

        clinical_context = {
            "uid_format": "1.2.826.0.1.3680043.8.498.{random_numbers}",
            "max_length": 64,
            "allowed_characters": "0-9 and periods (.)",
        }
        clinical_context.update(additional_context)

        if uid_value:
            clinical_context["uid_value"] = uid_value
            if len(uid_value) > 64:
                suggestions.insert(0, "UID exceeds maximum length of 64 characters")
            elif not uid_value.replace(".", "").replace(" ", "").isdigit():
                suggestions.insert(
                    0,
                    "UID contains invalid characters (only digits and periods allowed)",
                )

        if uid_type:
            clinical_context["uid_type"] = uid_type

        if root_uid:
            clinical_context["root_uid"] = root_uid
            suggestions.append(
                "Verify root UID is properly registered and unique to your organization"
            )
        else:
            suggestions.append(
                "Configure organizational root UID before generating instance UIDs"
            )

        suggestions.extend(
            [
                "Use hash-based UID generation for reproducible UIDs from same input",
                "Use random UID generation for maximum uniqueness guarantees",
                "Maintain UID registry to track relationships between objects",
            ]
        )

        if dicom_reference is None:
            dicom_reference = "PS 3.5, Chapter 9 (Unique Identifiers)"

        super().__init__(
            message,
            *args,
            suggestions=suggestions,
            clinical_context=clinical_context,
            dicom_reference=dicom_reference,
            **kwargs,
        )


class TemplateError(PyrtDicomError):
    """
    Raised when DICOM template or IOD structure errors occur.

    Used for missing template elements, invalid IOD structures, or
    modality-specific template issues.

    Common scenarios:
    - Missing required DICOM attributes for modality
    - Invalid attribute values for IOD
    - Template version compatibility issues
    - Modality-specific constraint violations
    """

    def __init__(
        self,
        message: str,
        *args,  # Allow positional arguments for backward compatibility
        modality: Optional[str] = None,
        missing_attributes: Optional[List[str]] = None,
        iod_reference: Optional[str] = None,
        **kwargs,
    ) -> None:
        # Extract conflicting parameters from kwargs to avoid conflicts
        additional_context = kwargs.pop("clinical_context", {})
        existing_suggestions = kwargs.pop("suggestions", [])
        existing_dicom_ref = kwargs.pop("dicom_reference", None)

        # Check if any enhanced features are being used
        enhanced_features_used = (
            modality is not None
            or missing_attributes is not None
            or iod_reference is not None
            or additional_context
            or existing_suggestions
            or existing_dicom_ref
        )

        suggestions = []
        clinical_context = {}
        dicom_ref = None

        if enhanced_features_used:
            # Start with existing suggestions if provided
            suggestions = list(existing_suggestions) if existing_suggestions else []

            # Add template-specific suggestions
            template_suggestions = [
                "Verify DICOM template matches your target modality requirements",
                "Check IOD specification for required vs. optional attributes",
                "Ensure all Type 1 (required) and Type 2 (required, empty allowed) attributes are present",
            ]
            suggestions.extend(template_suggestions)

            clinical_context.update(additional_context)
            dicom_ref = existing_dicom_ref or "PS 3.3 (Information Object Definitions)"

            if modality:
                clinical_context["modality"] = modality
                modality_refs = {
                    "CT": "A.3 (CT Image IOD)",
                    "RTSTRUCT": "A.19 (RT Structure Set IOD)",
                    "RTDOSE": "A.16 (RT Dose IOD)",
                    "RTPLAN": "A.14 (RT Plan IOD)",
                }
                if modality.upper() in modality_refs:
                    dicom_ref = f"PS 3.3, {modality_refs[modality.upper()]}"
                    suggestions.insert(
                        0, f"Review {modality} IOD specification in DICOM standard"
                    )

            if missing_attributes:
                clinical_context["missing_attributes"] = missing_attributes
                suggestions.insert(
                    0,
                    f"Populate missing required attributes: {', '.join(missing_attributes)}",
                )

            if iod_reference:
                clinical_context["iod_reference"] = iod_reference
                dicom_ref = iod_reference

            suggestions.extend(
                [
                    "Use appropriate DICOM template for your target modality",
                    "Validate template completeness before DICOM object creation",
                    "Consider using pydicom Dataset.is_valid() for IOD compliance checking",
                ]
            )

        super().__init__(
            message,
            *args,
            suggestions=suggestions,
            clinical_context=clinical_context,
            dicom_reference=dicom_ref,
            **kwargs,
        )
