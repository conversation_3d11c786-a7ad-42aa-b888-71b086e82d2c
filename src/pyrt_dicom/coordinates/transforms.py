# Copyright (C) 2024 Pirate DICOM Contributors

"""
Coordinate transformation utilities for RT DICOM creation.

This module provides coordinate system transformations between patient anatomical
coordinates and DICOM image coordinates, inspired by PyMedPhys patterns but
generalized for any input format.

Key Features:
- Patient position handling following DICOM standard orientations
- Image orientation patient vector transformations
- Sub-millimeter geometric accuracy for clinical applications
- Comprehensive validation for clinical safety

## Complete Coordinate Transformation Workflow

```python
import pyrt_dicom.coordinates as coords
import numpy as np

# 1. Set up coordinate transformer for planning CT
ct_transformer = coords.CoordinateTransformer(
    patient_position="HFS",  # Head First Supine
    pixel_spacing=(1.0, 1.0),  # 1mm x 1mm pixels
    slice_thickness=2.5,  # 2.5mm slice thickness
    image_position=(-250.0, -250.0, -150.0)  # Center of first voxel
)

# 2. Convert structure contour points from TPS to DICOM coordinates
# TPS contour points in patient coordinates (mm)
contour_points_patient = np.array([
    [50.0, 120.0, -45.0],   # Point 1: (x, y, z) in mm
    [55.0, 125.0, -45.0],   # Point 2
    [45.0, 130.0, -45.0]    # Point 3
])

# Transform to DICOM image coordinates
contour_points_dicom = ct_transformer.patient_to_dicom(contour_points_patient)
print(f"DICOM coordinates: {contour_points_dicom}")
# Output: [[300.0, 370.0, 42.0], [305.0, 375.0, 42.0], [295.0, 380.0, 42.0]]

# 3. Verify round-trip accuracy (critical for RT applications)
round_trip_patient = ct_transformer.dicom_to_patient(contour_points_dicom)
accuracy_mm = np.max(np.abs(contour_points_patient - round_trip_patient))
print(f"Round-trip accuracy: {accuracy_mm:.6f} mm")  # Should be < 1e-10 mm

# 4. Handle multiple patient positions
positions_to_test = ["HFS", "HFP", "FFS", "FFP"]
for position in positions_to_test:
    try:
        transformer = coords.CoordinateTransformer(
            patient_position=position,
            pixel_spacing=(0.5, 0.5),
            slice_thickness=1.25
        )
        print(f"{position}: {coords.DICOM_PATIENT_ORIENTATIONS[position]}")
    except coords.CoordinateSystemError as e:
        print(f"{position}: Error - {e}")

# 5. Advanced: Custom image orientation
custom_orientation = [1, 0, 0, 0, 1, 0]  # Standard axial orientation
custom_transformer = coords.CoordinateTransformer(
    patient_position="HFS",
    image_orientation=custom_orientation,
    pixel_spacing=(0.75, 0.75),
    slice_thickness=3.0
)

# Get full 4x4 transformation matrix for external tools
transform_matrix = custom_transformer.get_transformation_matrix()
print(f"Transformation matrix shape: {transform_matrix.shape}")
```

## Performance and Accuracy Examples

```python
import time

# Performance benchmark for large datasets
transformer = coords.CoordinateTransformer(
    patient_position="HFS",
    pixel_spacing=(1.0, 1.0),
    slice_thickness=2.0
)

# Generate large coordinate dataset (10,000 points)
large_dataset = np.random.rand(10000, 3) * 500  # 500mm range

# Benchmark transformation performance
start_time = time.time()
transformed_coords = transformer.patient_to_dicom(large_dataset)
transform_time = time.time() - start_time
print(f"Transformed 10,000 points in {transform_time:.3f}s")
# Typical: ~0.05s for 10,000 points

# Accuracy validation across coordinate ranges
test_ranges = {
    'small_structures': np.array([[0, 0, 0], [10, 10, 5]]),  # 10mm cube
    'organ_size': np.array([[-50, -50, -25], [50, 50, 25]]),  # 100mm cube
    'full_body': np.array([[-300, -300, -200], [300, 300, 200]])  # 600mm range
}

for test_name, coords_range in test_ranges.items():
    # Test corner points of range
    test_points = np.array([
        coords_range[0],  # Min corner
        coords_range[1],  # Max corner
        np.mean(coords_range, axis=0)  # Center point
    ])

    # Round-trip transformation
    dicom_coords = transformer.patient_to_dicom(test_points)
    patient_coords = transformer.dicom_to_patient(dicom_coords)

    # Calculate accuracy
    max_error = np.max(np.abs(test_points - patient_coords))
    print(f"{test_name}: Max error = {max_error:.2e} mm")
```

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Uses coordinate consistency
- :class:`~pyrt_dicom.validation.geometric.GeometricValidator` - Validates transformations
- :class:`~pyrt_dicom.utils.exceptions.CoordinateSystemError` - Error handling

**Related Functions**:
- :func:`validate_patient_position` - Validates position/orientation consistency
- :func:`transform_image_orientation` - Converts between patient positions
- :func:`dicom_to_patient_coordinates` - Convenience transformation function
- :func:`patient_to_dicom_coordinates` - Convenience transformation function

**See Also**:
- :mod:`~pyrt_dicom.coordinates.reference_frames` - Frame of Reference management
- DICOM PS 3.3, C.7.6.2 (Image Plane Module) - DICOM coordinate standard
"""

from typing import List, Optional, Tuple, Union

import numpy as np
from numpy.typing import NDArray

from ..utils.exceptions import CoordinateSystemError

# DICOM Patient Position definitions (from PyMedPhys orientation.py)
DICOM_PATIENT_ORIENTATIONS = {
    "HFP": "Head First-Prone",
    "HFS": "Head First-Supine",
    "HFDR": "Head First-Decubitus Right",
    "HFDL": "Head First-Decubitus Left",
    "FFDR": "Feet First-Decubitus Right",
    "FFDL": "Feet First-Decubitus Left",
    "FFP": "Feet First-Prone",
    "FFS": "Feet First-Supine",
    "LFP": "Left First-Prone",
    "LFS": "Left First-Supine",
    "RFP": "Right First-Prone",
    "RFS": "Right First-Supine",
    "AFDR": "Anterior First-Decubitus Right",
    "AFDL": "Anterior First-Decubitus Left",
    "PFDR": "Posterior First-Decubitus Right",
    "PFDL": "Posterior First-Decubitus Left",
    "SITTING": "In the sitting position, the patient's face is towards the front of the chair",
}

# Image orientation patient vectors for common orientations (from PyMedPhys)
IMAGE_ORIENTATION_MAP = {
    "FFDL": [0, 1, 0, 1, 0, 0],
    "FFDR": [0, -1, 0, -1, 0, 0],
    "FFP": [1, 0, 0, 0, -1, 0],
    "FFS": [-1, 0, 0, 0, 1, 0],
    "HFDL": [0, -1, 0, 1, 0, 0],
    "HFDR": [0, 1, 0, -1, 0, 0],
    "HFP": [-1, 0, 0, 0, -1, 0],
    "HFS": [1, 0, 0, 0, 1, 0],
}


class CoordinateTransformer:
    """Coordinate transformation utilities for RT DICOM creation.

    Provides transformations between patient anatomical coordinates and DICOM
    image coordinates with sub-millimeter accuracy for clinical applications.
    Supports all standard DICOM patient positions and image orientations.

    Clinical Notes:
        Coordinate transformations are critical for RT workflows where spatial
        accuracy directly impacts treatment delivery. This class ensures:

        **Sub-millimeter Accuracy**: Transformations maintain precision within
        clinical tolerances (typically <1mm) required for radiation therapy.

        **DICOM Standard Compliance**: Follows DICOM PS 3.3, C.7.6.2 (Image Plane
        Module) specifications for patient positions and image orientations.

        **Multi-position Support**: Handles all standard patient positions
        including HFS (Head First Supine), HFP (Head First Prone), and decubitus
        positions commonly used in RT.

        **Frame of Reference Consistency**: Ensures all transformed coordinates
        maintain spatial relationships within the same Frame of Reference UID.

        Common clinical applications:
        - Converting structure contours between TPS and DICOM coordinates
        - Aligning dose distributions with planning CT geometry
        - Validating spatial consistency across RT objects
        - Supporting multi-modality image registration workflows

    Attributes:
        patient_position: DICOM patient position code (e.g., "HFS", "HFP")
        image_orientation: 6-element image orientation patient vector
        pixel_spacing: Row and column pixel spacing in mm (for 2D transforms)
        slice_thickness: Slice thickness in mm (for 3D transforms)
        image_position: Image position patient coordinates in mm
    """

    def __init__(
        self,
        patient_position: str = "HFS",
        image_orientation: Optional[List[float]] = None,
        pixel_spacing: Optional[Tuple[float, float]] = None,
        slice_thickness: Optional[float] = None,
        image_position: Optional[Tuple[float, float, float]] = None,
    ) -> None:
        """Initialize coordinate transformer.

        Args:
            patient_position: DICOM patient position code. Must be one of the
                standard DICOM positions (HFS, HFP, FFS, FFP, etc.). Defaults
                to "HFS" (Head First Supine), the most common RT position.
            image_orientation: 6-element image orientation patient vector defining
                the direction cosines for row and column directions. If None,
                uses default orientation for the specified patient position.
            pixel_spacing: Row and column pixel spacing in millimeters as (row, col)
                tuple. Required for coordinate transformations involving spatial
                measurements. Typical values: (0.5-5.0, 0.5-5.0) mm.
            slice_thickness: Slice thickness in millimeters. Required for 3D
                coordinate transformations. Typical values: 1.0-5.0 mm for RT planning.
            image_position: Image position patient coordinates in millimeters as
                (x, y, z) tuple defining the center of the first voxel. Defaults
                to origin (0, 0, 0) if not specified.

        Raises:
            CoordinateSystemError: If patient position is invalid, image orientation
                vectors are not orthonormal, or parameters are geometrically
                inconsistent.

        Clinical Notes:
            Proper initialization is critical for accurate spatial transformations:

            **Patient Position**: Must match the actual patient setup during imaging.
            Common RT positions include:
            - HFS: Head First Supine (most common)
            - HFP: Head First Prone (breast, some brain cases)
            - FFS: Feet First Supine (pelvis, some abdomen)

            **Image Orientation**: The direction cosines define how image rows and
            columns map to patient anatomy. Incorrect orientation vectors will
            result in spatial misalignment between RT objects.

            **Pixel/Slice Spacing**: These define the physical size of image voxels.
            Values should match the planning CT parameters exactly to maintain
            geometric consistency across all RT objects.
        """
        self.patient_position = self._validate_patient_position(patient_position)
        self.image_orientation = self._validate_image_orientation(
            image_orientation, patient_position
        )
        self.pixel_spacing = pixel_spacing
        self.slice_thickness = slice_thickness
        self.image_position = image_position or (0.0, 0.0, 0.0)

        # Create transformation matrices
        self._row_cosines = np.array(self.image_orientation[:3])
        self._col_cosines = np.array(self.image_orientation[3:])
        self._slice_cosines = np.cross(self._row_cosines, self._col_cosines)

    @staticmethod
    def _validate_patient_position(patient_position: str) -> str:
        """Validate patient position against DICOM standard."""
        if patient_position not in DICOM_PATIENT_ORIENTATIONS:
            valid_positions = list(DICOM_PATIENT_ORIENTATIONS.keys())
            raise CoordinateSystemError(
                f"Invalid patient position '{patient_position}'. "
                f"Must be one of: {valid_positions}"
            )
        return patient_position

    @staticmethod
    def _validate_image_orientation(
        image_orientation: Optional[List[float]], patient_position: str
    ) -> List[float]:
        """Validate and get image orientation patient vector."""
        if image_orientation is None:
            if patient_position in IMAGE_ORIENTATION_MAP:
                return IMAGE_ORIENTATION_MAP[patient_position]
            else:
                raise CoordinateSystemError(
                    f"No default image orientation available for patient position '{patient_position}'",
                    patient_position=patient_position,
                    clinical_context={
                        "available_defaults": list(IMAGE_ORIENTATION_MAP.keys()),
                        "solution": "Provide explicit image_orientation parameter",
                    },
                )

        if len(image_orientation) != 6:
            raise CoordinateSystemError(
                f"Image orientation must have 6 elements (row and column direction cosines), got {len(image_orientation)}",
                clinical_context={
                    "required_elements": 6,
                    "current_elements": len(image_orientation),
                    "format": "[row_x, row_y, row_z, col_x, col_y, col_z]",
                    "units": "direction cosines (unit vectors)",
                },
            )

        # Validate that row and column cosines are orthogonal and normalized
        row_cosines = np.array(image_orientation[:3])
        col_cosines = np.array(image_orientation[3:])

        row_magnitude = np.linalg.norm(row_cosines)
        col_magnitude = np.linalg.norm(col_cosines)
        dot_product = np.dot(row_cosines, col_cosines)

        if not np.isclose(row_magnitude, 1.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Row direction cosines must be normalized (magnitude=1.0), "
                f"got magnitude={row_magnitude:.6f}"
            )

        if not np.isclose(col_magnitude, 1.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Column direction cosines must be normalized (magnitude=1.0), "
                f"got magnitude={col_magnitude:.6f}"
            )

        if not np.isclose(dot_product, 0.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Row and column direction cosines must be orthogonal "
                f"(dot product=0.0), got dot product={dot_product:.6f}"
            )

        return image_orientation

    def dicom_to_patient(
        self,
        coordinates: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    ) -> NDArray[np.float64]:
        """
        Transform DICOM image coordinates to patient anatomical coordinates.

        Parameters
        ----------
        coordinates : array-like
            DICOM coordinates as (row, column, slice) or array of such coordinates

        Returns
        -------
        NDArray[np.float64]
            Patient coordinates as (x, y, z) in mm

        Raises
        ------
        CoordinateSystemError
            If pixel spacing or slice thickness not set
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set for coordinate transformation"
            )

        coords = np.asarray(coordinates)
        input_was_1d = coords.ndim == 1
        if coords.ndim == 1:
            coords = coords.reshape(1, -1)

        if coords.shape[1] != 3:
            raise CoordinateSystemError(
                f"Coordinates must have 3 dimensions (row, column, slice), "
                f"got shape {coords.shape}"
            )

        # Convert image indices to physical distances
        physical_coords = np.zeros_like(coords, dtype=np.float64)
        physical_coords[:, 0] = coords[:, 0] * self.pixel_spacing[0]  # rows
        physical_coords[:, 1] = coords[:, 1] * self.pixel_spacing[1]  # columns
        physical_coords[:, 2] = coords[:, 2] * self.slice_thickness  # slices

        # Transform to patient coordinate system
        patient_coords = np.zeros_like(physical_coords)
        for i in range(len(physical_coords)):
            patient_coords[i] = (
                self.image_position
                + physical_coords[i, 0] * self._row_cosines
                + physical_coords[i, 1] * self._col_cosines
                + physical_coords[i, 2] * self._slice_cosines
            )

        return patient_coords.squeeze() if input_was_1d else patient_coords

    def patient_to_dicom(
        self,
        coordinates: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    ) -> NDArray[np.float64]:
        """
        Transform patient anatomical coordinates to DICOM image coordinates.

        Parameters
        ----------
        coordinates : array-like
            Patient coordinates as (x, y, z) in mm

        Returns
        -------
        NDArray[np.float64]
            DICOM coordinates as (row, column, slice)

        Raises
        ------
        CoordinateSystemError
            If pixel spacing or slice thickness not set
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set for coordinate transformation"
            )

        coords = np.asarray(coordinates)
        input_was_1d = coords.ndim == 1
        if coords.ndim == 1:
            coords = coords.reshape(1, -1)

        if coords.shape[1] != 3:
            raise CoordinateSystemError(
                f"Coordinates must have 3 dimensions (x, y, z), got shape {coords.shape}"
            )

        # Create transformation matrix from patient to image coordinates
        transform_matrix = np.column_stack(
            [self._row_cosines, self._col_cosines, self._slice_cosines]
        )

        # Transform to image coordinate system
        image_coords = np.zeros_like(coords, dtype=np.float64)
        image_position = np.array(self.image_position)

        for i in range(len(coords)):
            # Subtract image position to get relative coordinates
            relative_coords = coords[i] - image_position

            # Transform using inverse matrix
            physical_coords = np.linalg.solve(transform_matrix, relative_coords)

            # Convert physical distances to image indices
            image_coords[i, 0] = physical_coords[0] / self.pixel_spacing[0]  # rows
            image_coords[i, 1] = physical_coords[1] / self.pixel_spacing[1]  # columns
            image_coords[i, 2] = physical_coords[2] / self.slice_thickness  # slices

        return image_coords.squeeze() if input_was_1d else image_coords

    def get_transformation_matrix(self) -> NDArray[np.float64]:
        """
        Get 4x4 transformation matrix for homogeneous coordinates.

        Returns
        -------
        NDArray[np.float64]
            4x4 transformation matrix from image to patient coordinates
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set to get transformation matrix"
            )

        matrix = np.eye(4)

        # Set rotation part
        matrix[0, :3] = self._row_cosines * self.pixel_spacing[0]
        matrix[1, :3] = self._col_cosines * self.pixel_spacing[1]
        matrix[2, :3] = self._slice_cosines * self.slice_thickness

        # Set translation part
        matrix[:3, 3] = self.image_position

        return matrix


# Convenience functions for common operations


def validate_patient_position(
    patient_position: str, image_orientation: List[float]
) -> None:
    """
    Validate that patient position matches image orientation (PyMedPhys pattern).

    Parameters
    ----------
    patient_position : str
        DICOM patient position (e.g., 'HFS', 'HFP', 'FFS', 'FFP')
    image_orientation : List[float]
        6-element image orientation patient vector [row_x, row_y, row_z, col_x, col_y, col_z]

    Raises
    ------
    CoordinateSystemError
        If patient position doesn't match image orientation

    Clinical Context
    ----------------
    **Patient Positions** (IEC 61217 standard):
    - **HFS**: Head First Supine (most common for brain, H&N, thorax, abdomen)
    - **HFP**: Head First Prone (breast treatments, some spine cases)
    - **FFS**: Feet First Supine (pelvis, lower extremities)
    - **FFP**: Feet First Prone (rare, special positioning)

    **Image Orientation Patient** (DICOM PS 3.3):
    - First 3 values: Direction cosines of first row (left-right direction)
    - Last 3 values: Direction cosines of first column (anterior-posterior direction)
    - Must form orthogonal unit vectors
    - Defines how image pixel coordinates map to patient anatomy

    **Clinical Importance**:
    - Ensures consistent spatial interpretation across all RT objects
    - Prevents left-right flips and other geometric errors
    - Critical for accurate dose delivery and structure delineation
    - Required for multi-modality image registration

    **Tolerance**: ±1e-6 for direction cosine comparison (sub-millimeter accuracy)
    """
    if patient_position not in DICOM_PATIENT_ORIENTATIONS:
        valid_positions = list(DICOM_PATIENT_ORIENTATIONS.keys())
        raise CoordinateSystemError(
            f"Invalid patient position '{patient_position}'. "
            f"Must be one of: {valid_positions}"
        )

    if patient_position in IMAGE_ORIENTATION_MAP:
        expected_orientation = IMAGE_ORIENTATION_MAP[patient_position]
        if not np.allclose(image_orientation, expected_orientation, atol=1e-6):
            raise CoordinateSystemError(
                f"Patient position '{patient_position}' does not match image orientation. "
                f"Expected {expected_orientation}, got {image_orientation}."
            )


def transform_image_orientation(from_position: str, to_position: str) -> List[float]:
    """
    Transform image orientation from one patient position to another.

    Parameters
    ----------
    from_position : str
        Source patient position
    to_position : str
        Target patient position

    Returns
    -------
    List[float]
        6-element image orientation vector for target position

    Raises
    ------
    CoordinateSystemError
        If positions are invalid or transformation not supported

    Example
    -------
    >>> # Transform from Head First Supine to Head First Prone
    >>> hfp_orientation = transform_image_orientation("HFS", "HFP")
    >>> print(f"HFP orientation: {hfp_orientation}")
    [-1, 0, 0, 0, -1, 0]

    >>> # Get all available position transformations
    >>> available_positions = list(IMAGE_ORIENTATION_MAP.keys())
    >>> print(f"Available positions: {available_positions}")
    """
    if from_position not in IMAGE_ORIENTATION_MAP:
        available_positions = list(IMAGE_ORIENTATION_MAP.keys())
        raise CoordinateSystemError(
            f"No image orientation mapping available for position '{from_position}'",
            clinical_context={
                "available_positions": available_positions,
                "requested_position": from_position,
            },
        )

    if to_position not in IMAGE_ORIENTATION_MAP:
        available_positions = list(IMAGE_ORIENTATION_MAP.keys())
        raise CoordinateSystemError(
            f"No image orientation mapping available for position '{to_position}'",
            clinical_context={
                "available_positions": available_positions,
                "requested_position": to_position,
            },
        )

    return IMAGE_ORIENTATION_MAP[to_position]


def dicom_to_patient_coordinates(
    dicom_coords: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    patient_position: str = "HFS",
    pixel_spacing: Tuple[float, float] = (1.0, 1.0),
    slice_thickness: float = 1.0,
    image_position: Tuple[float, float, float] = (0.0, 0.0, 0.0),
    image_orientation: Optional[List[float]] = None,
) -> NDArray[np.float64]:
    """
    Convenience function for DICOM to patient coordinate transformation.

    Parameters
    ----------
    dicom_coords : array-like
        DICOM coordinates as (row, column, slice)
    patient_position : str
        DICOM patient position
    pixel_spacing : Tuple[float, float]
        Row and column pixel spacing in mm
    slice_thickness : float
        Slice thickness in mm
    image_position : Tuple[float, float, float]
        Image position patient coordinates in mm
    image_orientation : List[float], optional
        6-element image orientation patient vector

    Returns
    -------
    NDArray[np.float64]
        Patient coordinates as (x, y, z) in mm
    """
    transformer = CoordinateTransformer(
        patient_position=patient_position,
        image_orientation=image_orientation,
        pixel_spacing=pixel_spacing,
        slice_thickness=slice_thickness,
        image_position=image_position,
    )
    return transformer.dicom_to_patient(dicom_coords)


def patient_to_dicom_coordinates(
    patient_coords: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    patient_position: str = "HFS",
    pixel_spacing: Tuple[float, float] = (1.0, 1.0),
    slice_thickness: float = 1.0,
    image_position: Tuple[float, float, float] = (0.0, 0.0, 0.0),
    image_orientation: Optional[List[float]] = None,
) -> NDArray[np.float64]:
    """
    Convenience function for patient to DICOM coordinate transformation.

    Parameters
    ----------
    patient_coords : array-like
        Patient coordinates as (x, y, z) in mm
    patient_position : str
        DICOM patient position
    pixel_spacing : Tuple[float, float]
        Row and column pixel spacing in mm
    slice_thickness : float
        Slice thickness in mm
    image_position : Tuple[float, float, float]
        Image position patient coordinates in mm
    image_orientation : List[float], optional
        6-element image orientation patient vector

    Returns
    -------
    NDArray[np.float64]
        DICOM coordinates as (row, column, slice)
    """
    transformer = CoordinateTransformer(
        patient_position=patient_position,
        image_orientation=image_orientation,
        pixel_spacing=pixel_spacing,
        slice_thickness=slice_thickness,
        image_position=image_position,
    )
    return transformer.patient_to_dicom(patient_coords)


# Advanced coordinate transformation utilities


def validate_coordinate_consistency(
    transformers: List[CoordinateTransformer], tolerance_mm: float = 1.0
) -> bool:
    """
    Validate coordinate consistency across multiple transformers.

    Ensures all transformers use compatible coordinate systems for
    multi-modality RT workflows (CT, structures, dose, plan).

    Parameters
    ----------
    transformers : List[CoordinateTransformer]
        List of coordinate transformers to validate
    tolerance_mm : float, default 1.0
        Maximum allowed coordinate mismatch in mm

    Returns
    -------
    bool
        True if all transformers are coordinate-consistent

    Raises
    ------
    CoordinateSystemError
        If transformers have incompatible coordinate systems

    Examples
    --------
    Basic validation for multi-modality RT workflow:

    >>> ct_transformer = CoordinateTransformer("HFS", (1,1), 2.5)
    >>> dose_transformer = CoordinateTransformer("HFS", (2,2), 2.5)
    >>> struct_transformer = CoordinateTransformer("HFS", (1,1), 2.5)
    >>>
    >>> transformers = [ct_transformer, dose_transformer, struct_transformer]
    >>> is_consistent = validate_coordinate_consistency(transformers)
    >>> print(f"Coordinate systems consistent: {is_consistent}")

    Troubleshooting Common Issues
    ----------------------------

    **Issue 1: Patient Position Mismatch**

    >>> # This will raise CoordinateSystemError
    >>> ct_hfs = CoordinateTransformer("HFS", (1,1), 2.5)
    >>> ct_hfp = CoordinateTransformer("HFP", (1,1), 2.5)  # Different position!
    >>> try:
    ...     validate_coordinate_consistency([ct_hfs, ct_hfp])
    ... except CoordinateSystemError as e:
    ...     print(f"Error: {e}")
    ...     # Fix: Ensure all objects use same patient position

    **Issue 2: Pixel Spacing Tolerance**

    >>> # Fine pixel spacing differences may be acceptable
    >>> ct_fine = CoordinateTransformer("HFS", (0.98, 0.98), 2.5)
    >>> ct_coarse = CoordinateTransformer("HFS", (1.02, 1.02), 2.5)
    >>> # Use larger tolerance for slight differences
    >>> is_ok = validate_coordinate_consistency([ct_fine, ct_coarse], tolerance_mm=2.0)

    **Issue 3: Slice Thickness Mismatch**

    >>> # Different slice thicknesses often indicate coordinate problems
    >>> ct_thin = CoordinateTransformer("HFS", (1,1), 1.25)
    >>> dose_thick = CoordinateTransformer("HFS", (2,2), 2.5)
    >>> # This may fail - check if dose was properly interpolated to CT grid
    >>> try:
    ...     validate_coordinate_consistency([ct_thin, dose_thick])
    ... except CoordinateSystemError:
    ...     print("Coordinate mismatch - verify dose interpolation to CT grid")

    Clinical Notes
    --------------
    - **Sub-millimeter accuracy required**: RT treatments require <1mm spatial accuracy
    - **Common cause of failures**: Mixing data from different imaging sessions
    - **Best practice**: Always use planning CT as reference for all RT objects
    - **Tolerance guidelines**: Use 0.5mm for structures, 1.0mm for dose, 2.0mm for plans
    """
    if len(transformers) < 2:
        return True

    reference = transformers[0]
    test_points = np.array([[0, 0, 0], [100, 100, 50], [-100, -100, -25]])

    for i, transformer in enumerate(transformers[1:], 1):
        # Check patient position consistency
        if transformer.patient_position != reference.patient_position:
            raise CoordinateSystemError(
                f"Patient position mismatch: transformer {i} has "
                f"{transformer.patient_position}, reference has {reference.patient_position}",
                patient_position=transformer.patient_position,
            )

        # Check transformation consistency at test points
        ref_dicom = reference.patient_to_dicom(test_points)
        test_dicom = transformer.patient_to_dicom(test_points)

        max_diff = np.max(np.abs(ref_dicom - test_dicom))
        if max_diff > tolerance_mm:
            raise CoordinateSystemError(
                f"Coordinate transformation mismatch: {max_diff:.3f}mm exceeds "
                f"tolerance {tolerance_mm}mm",
                coordinate_mismatch_mm=max_diff,
            )

    return True


def estimate_transformation_accuracy(
    transformer: CoordinateTransformer,
    coordinate_range_mm: float = 500.0,
    num_test_points: int = 1000,
) -> dict[str, float]:
    """
    Estimate coordinate transformation accuracy through round-trip testing.

    Parameters
    ----------
    transformer : CoordinateTransformer
        Transformer to test
    coordinate_range_mm : float, default 500.0
        Range of coordinates to test (±range around origin)
    num_test_points : int, default 1000
        Number of random test points to use

    Returns
    -------
    Dict[str, float]
        Dictionary with accuracy statistics in mm

    Example
    -------
    >>> transformer = CoordinateTransformer("HFS", (1.0, 1.0), 2.5)
    >>> accuracy = estimate_transformation_accuracy(transformer)
    >>> print(f"Max error: {accuracy['max_error_mm']:.2e} mm")
    >>> print(f"Mean error: {accuracy['mean_error_mm']:.2e} mm")
    """
    # Generate random test points
    test_points = (np.random.rand(num_test_points, 3) - 0.5) * 2 * coordinate_range_mm

    # Round-trip transformation
    dicom_coords = transformer.patient_to_dicom(test_points)
    round_trip_coords = transformer.dicom_to_patient(dicom_coords)

    # Calculate errors
    errors = np.abs(test_points - round_trip_coords)
    point_errors = np.max(errors, axis=1)  # Max error per point

    return {
        "max_error_mm": float(np.max(point_errors)),
        "mean_error_mm": float(np.mean(point_errors)),
        "std_error_mm": float(np.std(point_errors)),
        "median_error_mm": float(np.median(point_errors)),
        "num_points_tested": num_test_points,
        "coordinate_range_mm": coordinate_range_mm,
    }
