"""
Main module for pyrt-dicom library.

This module serves as the primary entry point for the pyrt-dicom package,
providing the main library functionality and high-level API for creating
DICOM RT files from common data sources.

## Library Overview

pyrt-dicom (Pirate DICOM) is a Python library focused on creating radiotherapy
DICOM files from common data formats used in medical physics workflows.

### Supported DICOM Modalities

- **CT**: CT image series creation from 3D arrays
- **RTSTRUCT**: RT Structure Set creation from binary masks and contours  
- **RTDOSE**: RT Dose distribution creation from dose arrays
- **RTPLAN**: RT Plan creation from beam configuration data

### Key Features

- **Clinical-First API**: Designed for medical physicists and radiation oncologists
- **Comprehensive Validation**: Built-in safety checks for clinical parameters
- **PyMedPhys Integration**: Compatible with PyMedPhys coordinate systems
- **Multi-Format Input**: Support for NumPy arrays, MATLAB files, and other formats
- **Audit Logging**: Clinical audit trail for regulatory compliance

## Quick Start

```python
import pyrt_dicom as prt
import numpy as np

# Create UID generator for consistent relationships
generator = prt.RandomUIDGenerator()

# Example: Create RT Structure Set from binary masks
masks = {
    'PTV': np.load('ptv_mask.npy'),
    'OAR_Lung_L': np.load('lung_left_mask.npy'),
    'OAR_Heart': np.load('heart_mask.npy')
}

# Load reference CT
reference_ct = prt.pydicom.dcmread('planning_ct.dcm')

# Create structure set
structures = prt.RTStructureSet.from_masks(
    reference_image=reference_ct,
    masks=masks,
    patient_info={'PatientID': 'RT001', 'PatientName': 'Anonymous'}
)

# Save with validation
structures.save('rt_structures.dcm')
```

## Clinical Workflow Integration

The library is designed to integrate seamlessly with clinical physics workflows:

### Treatment Planning Systems (TPS)
- Import planning CT images and structure sets
- Export dose distributions and treatment plans
- Maintain spatial consistency across all RT objects

### Research Workflows  
- Convert research data to clinical DICOM format
- Enable data sharing between institutions
- Support reproducible research with hash-based UIDs

### Quality Assurance
- Built-in validation for clinical safety
- Geometric consistency checking
- DICOM standard compliance verification

## Project Status

This is currently an early-stage project with core framework implemented.
The main value is in the comprehensive roadmap (docs/mvp-roadmap.md) and 
project architecture rather than finished functionality.

### What's Implemented
- UID generation and management system
- Coordinate transformation utilities
- Validation framework (geometric, clinical, DICOM compliance)
- Exception handling with clinical context
- Audit logging system
- Base DICOM creator framework

### Coming Soon
- Complete RT DICOM object creators (RTSTRUCT, RTDOSE, RTPLAN)
- CLI interface for common workflows
- Integration with scikit-rt and other RT libraries
- Advanced validation and safety checks

For detailed implementation roadmap, see `docs/mvp-roadmap.md`.

## Documentation

Full documentation is available at: [Coming Soon - GitHub Pages]

For development documentation, see:
- `docs/mvp-roadmap.md` - Complete project roadmap and architecture
- `CLAUDE.md` - Development context and design decisions
- API documentation - Auto-generated from docstrings

## Support

This library is designed primarily for medical physics applications.
For clinical usage questions, consult with qualified medical physicists.

**Important**: This software is for research and educational purposes.
Always validate outputs against established clinical practices and standards.
"""
