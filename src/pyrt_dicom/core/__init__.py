"""
Core DICOM creation modules for pyrt-dicom.

This package contains the fundamental classes for creating DICOM RT objects:
- base.py: Base DICOM creator class with common functionality
- ct_series.py: CT image series creation
- rt_struct.py: RT Structure Set creation  
- rt_dose.py: RT Dose creation
- rt_plan.py: RT Plan creation
"""

# Core DICOM creator classes
from .base import BaseDicomCreator
from .ct_series import CTSeries

# Placeholder imports - will be updated as classes are implemented
# from .rt_struct import RTStructureSet
# from .rt_dose import RTDose
# from .rt_plan import RTPlan

__all__ = [
    "BaseDicomCreator",
    "CTSeries",
    # Will be populated as classes are implemented
]