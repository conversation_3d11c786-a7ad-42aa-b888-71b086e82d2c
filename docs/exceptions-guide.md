# Exception Handling in pyrt-dicom

This guide documents the comprehensive exception handling system in pyrt-dicom, designed specifically for clinical radiotherapy workflows with enhanced error reporting and actionable guidance.

## Table of Contents
- [Exception Hierarchy](#exception-hierarchy)
- [Base Exception Class](#base-exception-class)
- [Exception Types](#exception-types)
  - [ValidationError](#validationerror)
  - [DicomCreationError](#dicomcreationerror)
  - [CoordinateSystemError](#coordinatesystemerror)
  - [UIDGenerationError](#uidgenerationerror)
  - [TemplateError](#templateerror)
- [Enhanced Features](#enhanced-features)
- [Usage Examples](#usage-examples)
- [Clinical Integration](#clinical-integration)
- [Testing Exception Handling](#testing-exception-handling)

## Exception Hierarchy

```
Exception
└── PyrtDicomError (base class with enhanced features)
    ├── ValidationError (always enhanced)
    ├── DicomCreationError (conditionally enhanced)
    ├── CoordinateSystemError (conditionally enhanced)
    ├── UIDGenerationError (always enhanced)
    └── TemplateError (conditionally enhanced)
```

## Base Exception Class

### PyrtDicomError

The base class for all pyrt-dicom exceptions, providing enhanced error reporting with clinical context.

**Key Features:**
- **Actionable Suggestions** (`suggestions`): List of resolution steps
- **Clinical Context** (`clinical_context`): Dictionary with relevant clinical parameters
- **DICOM References** (`dicom_reference`): References to DICOM standard sections
- **Enhanced String Representation**: Formatted output with context and suggestions

```python
PyrtDicomError(
    message="Error description",
    suggestions=["Step 1", "Step 2"],
    clinical_context={"parameter": "value", "units": "Gy"},
    dicom_reference="PS 3.3, C.7.6.1"
)
```

## Exception Types

### ValidationError

**Always Enhanced** - Provides comprehensive clinical validation feedback for all usage patterns.

**Purpose:** Clinical and technical parameter validation with safety-focused guidance.

**Parameters:**
- `parameter_name` (str): Name of the parameter being validated
- `current_value` (Union[str, float, int]): The invalid value
- `valid_range` (tuple): Valid range (min, max)
- `units` (str): Parameter units (e.g., "Gy", "mm", "degrees")
- `validation_type` (str): Type of validation ("clinical", "geometric", "dicom_compliance")

**Validation Types:**
- **"clinical"** (default): Clinical safety validation with medical physics guidance
- **"geometric"**: Spatial/coordinate system validation
- **"dicom_compliance"**: DICOM standard compliance validation

```python
ValidationError(
    "Dose value exceeds clinical limits",
    parameter_name="prescription_dose",
    current_value=50.0,
    valid_range=(0.1, 30.0),
    units="Gy",
    validation_type="clinical"
)
```

### DicomCreationError

**Conditionally Enhanced** - Provides enhanced features when specific parameters are used.

**Purpose:** DICOM file creation and data structure errors.

**Enhanced Parameters:**
- `missing_elements` (List[str]): Missing required DICOM elements
- `invalid_data_types` (Dict[str, str]): Invalid data type mappings
- `file_path` (str): File path for I/O related errors

```python
DicomCreationError(
    "Missing required DICOM elements",
    missing_elements=["PatientID", "StudyInstanceUID"],
    file_path="/path/to/output.dcm"
)
```

### CoordinateSystemError

**Conditionally Enhanced** - Provides enhanced features when coordinate-specific parameters are used.

**Purpose:** Spatial coordinate system and geometric transformation errors.

**Enhanced Parameters:**
- `frame_of_reference_uid` (str): Frame of Reference UID
- `patient_position` (str): Patient position (e.g., "HFS", "HFP")
- `coordinate_mismatch_mm` (float): Coordinate mismatch in millimeters

```python
CoordinateSystemError(
    "Frame of Reference UID mismatch",
    frame_of_reference_uid="*******.5",
    patient_position="HFS",
    coordinate_mismatch_mm=2.5
)
```

### UIDGenerationError

**Always Enhanced** - Provides comprehensive UID format and management guidance.

**Purpose:** DICOM UID generation, format validation, and uniqueness management.

**Parameters:**
- `uid_value` (str): The problematic UID value
- `uid_type` (str): Type of UID (e.g., "StudyInstanceUID", "SeriesInstanceUID")
- `root_uid` (str): Organization root UID

```python
UIDGenerationError(
    "UID exceeds maximum length",
    uid_value="*******.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.20.21.22.23.24.25",
    uid_type="StudyInstanceUID",
    root_uid="*******.5"
)
```

### TemplateError

**Conditionally Enhanced** - Provides enhanced features when template-specific parameters are used.

**Purpose:** DICOM template and IOD (Information Object Definition) structure validation.

**Enhanced Parameters:**
- `modality` (str): DICOM modality (e.g., "CT", "RTSTRUCT", "RTDOSE", "RTPLAN")
- `missing_attributes` (List[str]): Missing required attributes
- `iod_reference` (str): Specific IOD reference

```python
TemplateError(
    "Missing required attributes for RT Structure Set",
    modality="RTSTRUCT",
    missing_attributes=["StructureSetROISequence"],
    iod_reference="PS 3.3, A.19"
)
```

## Enhanced Features

All pyrt-dicom exceptions support enhanced error reporting:

### 1. Actionable Suggestions
Automatically generated suggestions based on error type and context:
```python
exc.suggestions
# ['Review clinical protocols for acceptable parameter ranges',
#  'Consult medical physics guidelines for your institution',
#  'Verify data entry and unit conversions are correct']
```

### 2. Clinical Context
Structured clinical information relevant to the error:
```python
exc.clinical_context
# {'parameter_name': 'prescription_dose',
#  'current_value': 50.0,
#  'valid_range': (0.1, 30.0),
#  'units': 'Gy',
#  'validation_type': 'Clinical Safety'}
```

### 3. DICOM References
References to relevant DICOM standard sections:
```python
exc.dicom_reference
# "PS 3.3 (Information Object Definitions)"
```

### 4. Enhanced String Representation
Formatted error messages with full context:
```
Dose value exceeds clinical limits
Clinical Context: Current value: 50.0, Valid range: 0.1 - 30.0, Units: Gy
DICOM Reference: PS 3.3 (Information Object Definitions)
Suggestions for resolution:
  1. Adjust value to be within valid range: 0.1 - 30.0
  2. Review clinical protocols for acceptable parameter ranges
  3. Consult medical physics guidelines for your institution
  4. Verify data entry and unit conversions are correct
```

## Usage Examples

### ValidationError (Always Enhanced)

```python
# Basic usage - automatically provides clinical guidance
try:
    validate_dose_rate(750)  # cGy/min
except Exception as e:
    raise ValidationError(
        "Dose rate exceeds clinical safety limits"
    ) from e
# Result: Full clinical context, suggestions, and DICOM reference

# Enhanced usage with specific parameters
try:
    validate_prescription_dose(dose_gy)
except Exception as e:
    raise ValidationError(
        "Prescription dose validation failed",
        parameter_name="prescription_dose",
        current_value=dose_gy,
        valid_range=(0.1, 30.0),
        units="Gy",
        validation_type="clinical"
    ) from e
```

### Conditional Enhancement Examples

```python
# DicomCreationError - basic usage
raise DicomCreationError("Failed to create RT Structure Set")

# DicomCreationError - enhanced usage
raise DicomCreationError(
    "Missing required DICOM elements",
    missing_elements=["PatientID", "StudyInstanceUID"],
    file_path="/output/rt_struct.dcm"
)

# CoordinateSystemError - basic usage  
raise CoordinateSystemError("Coordinate transformation failed")

# CoordinateSystemError - enhanced usage
raise CoordinateSystemError(
    "Frame of Reference mismatch between CT and structure",
    frame_of_reference_uid="1.2.826.0.1.3680043.8.498.12345",
    patient_position="HFS",
    coordinate_mismatch_mm=1.2
)
```

### Clinical Integration Patterns

```python
import pyrt_dicom as prt
from pyrt_dicom.utils.exceptions import *

# 1. Parameter validation with clinical context
def validate_clinical_parameters(dose_gy, fractions):
    if dose_gy > 30.0:
        raise ValidationError(
            "Total dose exceeds typical clinical range",
            parameter_name="total_dose", 
            current_value=dose_gy,
            valid_range=(0.1, 30.0),
            units="Gy",
            validation_type="clinical"
        )
    
    if dose_gy / fractions > 3.0:
        raise ValidationError(
            "Dose per fraction exceeds hypofractionation limits",
            parameter_name="dose_per_fraction",
            current_value=dose_gy / fractions,
            valid_range=(1.8, 3.0),
            units="Gy/fraction",
            validation_type="clinical"
        )

# 2. Comprehensive error handling in RT workflows
try:
    # Create RT Structure with validation
    rt_struct = prt.RTStructureSet.from_masks(
        ct_reference=ct_data,
        masks={'PTV': ptv_mask, 'Heart': heart_mask},
        patient_info={'PatientID': 'RT_001', 'PatientName': 'Doe^John'}
    )
    rt_struct.save('rt_struct.dcm')
    
except ValidationError as e:
    print(f"Clinical validation failed: {e}")
    print(f"Parameter: {e.clinical_context.get('parameter_name', 'Unknown')}")
    print(f"Current value: {e.clinical_context.get('current_value')}")
    print(f"Valid range: {e.clinical_context.get('valid_range')}")
    
    # Apply suggested fixes
    print("Suggested fixes:")
    for i, suggestion in enumerate(e.suggestions, 1):
        print(f"  {i}. {suggestion}")
        
except CoordinateSystemError as e:
    print(f"Coordinate system error: {e}")
    frame_uid = e.clinical_context.get('frame_of_reference_uid')
    if frame_uid:
        print(f"Frame of Reference UID: {frame_uid}")
        
except UIDGenerationError as e:
    print(f"UID generation failed: {e}")
    print(f"UID format requirements: {e.clinical_context.get('uid_format')}")
    
except DicomCreationError as e:
    print(f"DICOM creation failed: {e}")
    missing = e.clinical_context.get('missing_elements', [])
    if missing:
        print(f"Missing elements: {missing}")
        
except PyrtDicomError as e:
    # Generic pyrt-dicom error handling
    print(f"pyrt-dicom error: {e}")
    if e.dicom_reference:
        print(f"DICOM reference: {e.dicom_reference}")
    if e.suggestions:
        print("Suggestions:", "; ".join(e.suggestions))
```

### Best Practices

1. **Use specific exception types** that match the error domain:
   ```python
   # Good - specific exception type
   raise ValidationError("Dose validation failed", ...)
   
   # Avoid - generic exceptions for domain-specific errors
   raise ValueError("Dose validation failed")
   ```

2. **Provide clinical context** when available:
   ```python
   # Good - includes clinical parameters
   raise ValidationError(
       "Dose exceeds limits",
       parameter_name="prescription_dose",
       current_value=dose,
       valid_range=(0.1, 30.0),
       units="Gy"
   )
   
   # Basic - still provides clinical guidance
   raise ValidationError("Dose exceeds limits")
   ```

3. **Chain exceptions** to preserve error context:
   ```python
   try:
       validate_dose(dose)
   except ValueError as e:
       raise ValidationError(
           "Clinical dose validation failed",
           parameter_name="dose",
           current_value=dose
       ) from e
   ```

4. **Handle enhanced features** in error processing:
   ```python
   try:
       create_rt_structure()
   except ValidationError as e:
       # Access enhanced features
       if e.clinical_context:
           log_clinical_context(e.clinical_context)
       if e.suggestions:
           suggest_fixes(e.suggestions)
       if e.dicom_reference:
           log_dicom_reference(e.dicom_reference)
   ```

## Clinical Integration

The pyrt-dicom exception system is designed specifically for clinical radiotherapy workflows:

### Clinical Safety Features

- **Parameter Validation**: Automatic validation against clinical ranges
- **Unit Awareness**: Proper handling of medical physics units (Gy, cGy, mm, etc.)
- **Workflow Context**: Exceptions provide context relevant to RT treatment planning
- **DICOM Compliance**: Built-in references to DICOM standards for clinical systems

### Integration with Clinical Systems

```python
# Example: Clinical dose validation pipeline
def clinical_dose_pipeline(prescription):
    try:
        # Validate prescription parameters
        if prescription.total_dose > 30.0:
            raise ValidationError(
                "Total dose exceeds typical clinical limits",
                parameter_name="total_dose",
                current_value=prescription.total_dose,
                valid_range=(0.1, 30.0),
                units="Gy",
                validation_type="clinical"
            )
        
        # Create DICOM RT Plan
        rt_plan = create_rt_plan(prescription)
        
    except ValidationError as e:
        # Log for clinical audit trail
        clinical_logger.error(f"Dose validation failed: {e}")
        clinical_logger.info(f"DICOM Reference: {e.dicom_reference}")
        
        # Provide user guidance
        return {
            "status": "validation_failed",
            "parameter": e.clinical_context.get("parameter_name"),
            "current_value": e.clinical_context.get("current_value"),
            "valid_range": e.clinical_context.get("valid_range"),
            "suggestions": e.suggestions
        }
```

## Testing Exception Handling

### Testing Enhanced Exceptions

```python
import pytest
from pyrt_dicom.utils.exceptions import *

def test_validation_error_enhanced():
    """Test ValidationError with enhanced features."""
    with pytest.raises(ValidationError) as exc_info:
        raise ValidationError(
            "Dose validation failed",
            parameter_name="prescription_dose",
            current_value=50.0,
            valid_range=(0.1, 30.0),
            units="Gy"
        )
    
    exc = exc_info.value
    
    # Test enhanced features
    assert exc.clinical_context['parameter_name'] == "prescription_dose"
    assert exc.clinical_context['current_value'] == 50.0
    assert exc.clinical_context['valid_range'] == (0.1, 30.0)
    assert exc.clinical_context['units'] == "Gy"
    assert len(exc.suggestions) >= 3
    assert exc.dicom_reference is not None
    
    # Test string representation includes context
    error_str = str(exc)
    assert "Clinical Context:" in error_str
    assert "DICOM Reference:" in error_str
    assert "Suggestions for resolution:" in error_str

def test_coordinate_system_error_conditional():
    """Test CoordinateSystemError conditional enhancement."""
    # Basic usage - no enhancement
    basic_error = CoordinateSystemError("Basic coordinate error")
    assert len(basic_error.suggestions) == 0
    assert len(basic_error.clinical_context) == 0
    assert basic_error.dicom_reference is None
    
    # Enhanced usage
    enhanced_error = CoordinateSystemError(
        "Frame of Reference mismatch",
        frame_of_reference_uid="*******.5",
        patient_position="HFS"
    )
    assert len(enhanced_error.suggestions) > 0
    assert enhanced_error.clinical_context['frame_of_reference_uid'] == "*******.5"
    assert enhanced_error.clinical_context['patient_position'] == "HFS"
    assert enhanced_error.dicom_reference is not None

def test_uid_generation_error_always_enhanced():
    """Test UIDGenerationError always provides enhancement."""
    # Even basic usage provides enhanced features
    basic_error = UIDGenerationError("UID generation failed")
    assert len(basic_error.suggestions) > 0  # Always provides suggestions
    assert 'uid_format' in basic_error.clinical_context
    assert 'max_length' in basic_error.clinical_context
    assert basic_error.dicom_reference is not None

def test_exception_chaining():
    """Test exception chaining preserves context."""
    original_error = ValueError("Original error")
    
    try:
        raise ValidationError(
            "Validation failed",
            parameter_name="dose"
        ) from original_error
    except ValidationError as e:
        assert e.__cause__ is original_error
        assert e.clinical_context['parameter_name'] == "dose"
```

### Testing Best Practices

1. **Test both basic and enhanced usage** for conditionally enhanced exceptions
2. **Verify clinical context** is correctly populated
3. **Check suggestion quality** - ensure suggestions are actionable
4. **Validate DICOM references** are accurate and specific
5. **Test exception chaining** preserves full error context
6. **Verify string representation** includes all enhanced features

## Advanced Usage

### Custom Exception Enhancement

```python
# Extend exceptions with domain-specific context
class RTDoseValidationError(ValidationError):
    """Specialized validation error for RT Dose objects."""
    
    def __init__(self, message, dose_grid_spacing=None, dose_units=None, **kwargs):
        # Add RT Dose specific context
        clinical_context = kwargs.get('clinical_context', {})
        clinical_context.update({
            'dose_grid_spacing': dose_grid_spacing,
            'dose_units': dose_units,
            'modality': 'RTDOSE'
        })
        kwargs['clinical_context'] = clinical_context
        
        super().__init__(message, validation_type="clinical", **kwargs)

# Usage
raise RTDoseValidationError(
    "Dose grid resolution insufficient for clinical use",
    dose_grid_spacing=(5.0, 5.0, 2.5),
    dose_units="Gy",
    parameter_name="grid_spacing"
)
```

### Exception Handler Registration

```python
# Register custom exception handlers
def handle_validation_error(exc: ValidationError):
    """Custom handler for validation errors."""
    return {
        "error_type": "validation",
        "parameter": exc.clinical_context.get("parameter_name"),
        "suggestions": exc.suggestions,
        "dicom_reference": exc.dicom_reference
    }

def handle_coordinate_error(exc: CoordinateSystemError):
    """Custom handler for coordinate errors."""
    return {
        "error_type": "coordinate_system",
        "frame_uid": exc.clinical_context.get("frame_of_reference_uid"),
        "mismatch_mm": exc.clinical_context.get("coordinate_mismatch_mm"),
        "suggestions": exc.suggestions
    }

# Exception handling dispatch
EXCEPTION_HANDLERS = {
    ValidationError: handle_validation_error,
    CoordinateSystemError: handle_coordinate_error,
}

def handle_pyrt_error(exc: PyrtDicomError):
    """Generic exception handler with dispatch."""
    handler = EXCEPTION_HANDLERS.get(type(exc))
    if handler:
        return handler(exc)
    
    # Generic handling
    return {
        "error_type": "generic",
        "message": str(exc),
        "suggestions": exc.suggestions,
        "dicom_reference": exc.dicom_reference
    }
```

## Summary

The pyrt-dicom exception system provides:

- **Clinical-First Design**: Exceptions tailored for radiotherapy workflows
- **Enhanced Error Reporting**: Actionable suggestions, clinical context, and DICOM references  
- **Flexible Enhancement**: Always enhanced (ValidationError, UIDGenerationError) or conditional (others)
- **DICOM Compliance**: Built-in references to relevant DICOM standards
- **Testing Support**: Comprehensive testing patterns for both basic and enhanced usage

This system ensures that errors in clinical RT workflows provide meaningful, actionable information to help medical physicists and developers quickly identify and resolve issues while maintaining DICOM compliance and clinical safety standards.
