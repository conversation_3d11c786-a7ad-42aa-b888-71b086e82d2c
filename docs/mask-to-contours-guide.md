# Mask-to-Contours Guide

## Overview
Below is a comprehensive, annotated Python script that:

1. Converts a 3D binary mask (numpy array) into per-slice contours using skimage.measure.find_contours.
2. Maps contour coordinates from pixel (row, col, slice) to DICOM patient space (x, y, z) using the DICOM CT image metadata.
3. Embeds these contours into a new RTSTRUCT object using pydicom.

```python
import numpy as np
import pydicom
from pydicom.sequence import Sequence
from pydicom.dataset import Dataset
from skimage.measure import find_contours

def mask_to_rtstruct(mask3d: np.ndarray, ct_series: list[str], rtstruct_template: str, output_path: str):
    """
    Convert a 3D numpy mask to RTSTRUCT with drawn contours via find_contours.
    
    mask3d: boolean array (num_slices, rows, cols)
    ct_series: sorted file paths to DICOM CT slice files
    rtstruct_template: a DICOM RTSTRUCT file path to use as template
    output_path: path to save new RTSTRUCT
    """
    # Load CT slices
    ct_datasets = [pydicom.dcmread(fp) for fp in ct_series]
    ct_datasets.sort(key=lambda ds: float(ds.ImagePositionPatient[2]))
    
    rows, cols = mask3d.shape[1], mask3d.shape[2]
    pixel_spacing = ct_datasets[0].PixelSpacing  # [row_spacing, col_spacing]
    image_orient = ct_datasets[0].ImageOrientationPatient  # [xr, xc, yr, yc, zr, zc]
    image_pos = ct_datasets[0].ImagePositionPatient  # position of first slice

    # Load empty RTSTRUCT template
    rt_ds = pydicom.dcmread(rtstruct_template)
    rt_ds.ROIContourSequence = Sequence()
    
    # Build a single ROIContour sequence for our mask:
    roi_contour = Dataset()
    roi_contour.ContourSequence = Sequence()
    roi_contour.ReferencedROINumber = 1
    # Set ROI Display Color etc if needed

    for slice_idx, ds in enumerate(ct_datasets):
        img_mask = mask3d[slice_idx]
        if not img_mask.any():
            continue

        contours = find_contours(img_mask.astype(float), level=0.5)  # :contentReference[oaicite:4]{index=4}

        for cnt in contours:
            # cnt is (n_points, 2) array [row_idx, col_idx]
            cc = Dataset()
            cc.ContourGeometricType = 'CLOSED_PLANAR'
            n_pts = cnt.shape[0]
            cc.NumberOfContourPoints = n_pts

            pts = []
            for r, c in cnt:
                x = image_pos[0] + c * pixel_spacing[1] * image_orient[1] \
                    + r * pixel_spacing[0] * image_orient[0]
                y = image_pos[1] + c * pixel_spacing[1] * image_orient[4] \
                    + r * pixel_spacing[0] * image_orient[3]
                z = ds.ImagePositionPatient[2]
                pts.extend([float(x), float(y), float(z)])

            cc.ContourData = pts
            roi_contour.ContourSequence.append(cc)

    rt_ds.ROIContourSequence.append(roi_contour)
    rt_ds.save_as(output_path)
```

## Step-by-Step Explanation
1. Load CT slices, sorted by ImagePositionPatient[2] (z‑axis) for correct anatomical order.
2. Pixel‑to‑patient transform uses:
    - PixelSpacing
    - ImageOrientationPatient
    - ImagePositionPatient
3. For each non‑zero mask slice, extract 2D contours at level 0.5 using skimage.measure.find_contours 
    - scikit-image.org
4. Map each contour point (row, col) → (x, y, z) in patient space and build ContourSequence items.
5. Save the final RTSTRUCT.dcm.

## Coordinate System and Origin Handling

The mask-to-contour conversion process accounts for coordinate systems and image origins through a **multi-layered approach** that ensures clinical-grade spatial accuracy:

### 1. **Image Position as Reference Origin**

The system establishes the origin through the `image_position` parameter in the coordinate transformer:

```python
# From rt_struct.py:252
self.coordinate_transformer = CoordinateTransformer(
    image_orientation=image_orientation,
    image_position=image_position,  # This defines the origin
    pixel_spacing=pixel_spacing
)
```

**Key Points:**
- `image_position` represents the **center of the first voxel** in DICOM patient coordinates (mm)
- Extracted from reference CT's `ImagePositionPatient` DICOM tag
- Defaults to `(0.0, 0.0, 0.0)` if not available
- Establishes the spatial reference for all subsequent coordinate transformations

### 2. **Scikit-Image to Physical Coordinate Transformation**

The core conversion in `_generate_slice_contours()` handles the origin offset:

```python
# From contour_processing.py:521-532
for point in contour_path:
    row, col = point  # Scikit-image returns array indices
    
    # Convert to physical coordinates using pixel spacing
    x = col * self.pixel_spacing[1]  # Column spacing
    y = row * self.pixel_spacing[0]  # Row spacing
    z = z_position
    
    physical_contour.append((float(x), float(y), float(z)))
```

**Critical Origin Handling:**
- Scikit-image `find_contours()` returns **relative indices** from (0,0) array origin
- Physical coordinates = array_indices × pixel_spacing
- Z-coordinate comes from slice position, not array index

### 3. **Patient Coordinate System Transformation**

The `CoordinateTransformer` handles the complete origin transformation:

```python
# From transforms.py:394-399
patient_coords[i] = (
    self.image_position +  # Apply origin offset
    physical_coords[i, 0] * self._row_cosines +
    physical_coords[i, 1] * self._col_cosines +
    physical_coords[i, 2] * self._slice_cosines
)
```

**Origin Transformation Process:**
1. **Image Position Offset**: Adds the DICOM `ImagePositionPatient` as origin translation
2. **Direction Cosine Application**: Applies proper orientation based on patient position
3. **Physical Spacing**: Converts array indices to millimeter measurements

### 4. **Clinical Coordinate System Consistency**

The system ensures origin consistency across RT objects:

```python
# From rt_struct.py:242-246
if hasattr(self.reference_image, 'FrameOfReferenceUID'):
    self._frame_of_reference_uid = self.reference_image.FrameOfReferenceUID
else:
    self._frame_of_reference_uid = self.uid_generator.generate_frame_of_reference_uid()
```

**Frame of Reference Management:**
- All structures share the same **Frame of Reference UID**
- Ensures spatial consistency with planning CT
- Critical for treatment planning system compatibility

### 5. **Multi-Level Origin Validation**

The system validates origin handling at multiple levels:

**Geometric Validation** (transforms.py:463-487):
```python
def get_transformation_matrix(self) -> NDArray[np.float64]:
    matrix = np.eye(4)
    # Set rotation part with pixel spacing
    matrix[0, :3] = self._row_cosines * self.pixel_spacing[0]
    matrix[1, :3] = self._col_cosines * self.pixel_spacing[1] 
    matrix[2, :3] = self._slice_cosines * self.slice_thickness
    # Set translation part (origin)
    matrix[:3, 3] = self.image_position
    return matrix
```

**Round-Trip Accuracy Testing** (transforms.py:797-844):
- Tests coordinate transformation accuracy through round-trip conversions
- Validates that origin transformations maintain sub-millimeter precision
- Ensures clinical-grade spatial accuracy (<0.5mm typical)

### Summary

The mask-to-contour conversion accounts for the coordinate system origin through:

1. **DICOM Image Position**: Uses reference CT's `ImagePositionPatient` as the spatial origin
2. **Physical Scaling**: Converts array indices to physical coordinates using pixel spacing
3. **Origin Translation**: Applies image position offset in final coordinate transformation
4. **Orientation Correction**: Accounts for patient position and image orientation
5. **Frame of Reference**: Maintains spatial consistency across all RT objects

This ensures that contour points generated from binary masks are spatially registered with the original imaging data, maintaining the clinical accuracy required for radiation therapy applications.

## Notes & Caveats
- The scikit-image method is asserted to be very fast, have subpixel-accurate iso-lines, and produce smooth, interpolated paths. It is a great balance of speed and quality.
- Contours per slice only: This script creates one ROI with all slice contours.
- Spatial accuracy depends on correct DICOM metadata.
- RTSTRUCT elements like StructureSetROISequence and RTROIObservationsSequence may need updating for full compatibility with clinical viewers.
- Complex shapes, nested holes, or shape simplification (e.g., via skimage.measure.approximate_polygon) should be handled before or after contour extraction.