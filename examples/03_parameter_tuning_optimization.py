"""
This example demonstrates the use of the MaskToContourConverter in a clinical setting.
It includes all scripts that have been ported to the Jupyter Notebook with the 
corresponding name.
"""
###############################################################################
# %% MaskToContourConverter: Parameter Tuning & Optimization

###############################################################################
# %% 1. Setup & Environment {#setup}

# Core imports
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Rectangle
import time
import warnings
warnings.filterwarnings('ignore')

# Interactive widgets
try:
    import ipywidgets as widgets
    from IPython.display import display, clear_output
    WIDGETS_AVAILABLE = True
    print("✅ Interactive widgets available")
except ImportError:
    WIDGETS_AVAILABLE = False
    print("⚠️  Interactive widgets not available - install ipywidgets for full functionality")

# pyrt-dicom imports
try:
    from pyrt_dicom.utils.contour_processing import MaskToContourConverter
    print("✅ MaskToContourConverter imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure pyrt-dicom is installed: pip install -e .")

# Set up plotting style
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# Helper functions for parameter analysis

def create_test_mask(shape='circle', size=100, complexity='simple'):
    """Create test masks with varying complexity for parameter testing."""
    mask = np.zeros((200, 200), dtype=bool)
    center = (100, 100)
    
    if shape == 'circle':
        y, x = np.ogrid[:200, :200]
        dist_from_center = np.sqrt((x - center[0])**2 + (y - center[1])**2)
        
        if complexity == 'simple':
            mask = dist_from_center <= size/2
        elif complexity == 'irregular':
            # Add irregular boundary with noise
            noise = np.random.normal(0, 5, (200, 200))
            mask = dist_from_center <= (size/2 + noise)
        elif complexity == 'complex':
            # Multiple circles with holes
            mask1 = dist_from_center <= size/2
            mask2 = dist_from_center <= size/4
            mask3 = np.sqrt((x - 80)**2 + (y - 80)**2) <= 15
            mask = mask1 & ~mask2 | mask3
    
    elif shape == 'rectangle':
        if complexity == 'simple':
            mask[center[1]-size//2:center[1]+size//2, 
                 center[0]-size//2:center[0]+size//2] = True
        elif complexity == 'irregular':
            # Rectangle with jagged edges
            for i in range(center[1]-size//2, center[1]+size//2):
                jitter = int(np.random.normal(0, 3))
                mask[i, center[0]-size//2+jitter:center[0]+size//2+jitter] = True
    
    return mask

def analyze_contour_quality(mask, contours, pixel_spacing=(1.0, 1.0)):
    """Analyze contour quality metrics."""
    metrics = {}
    
    # Calculate total points
    total_points = sum(len(contour) for contour in contours)
    metrics['total_points'] = total_points
    
    # Estimate file size impact (rough approximation)
    # Each point ~16 bytes in DICOM (x,y,z as 8-byte doubles)
    estimated_size_bytes = total_points * 16
    metrics['estimated_size_kb'] = estimated_size_bytes / 1024
    
    # Calculate perimeter accuracy
    if contours:
        perimeter = 0
        for contour in contours:
            if len(contour) > 2:
                # Calculate perimeter of this contour
                contour_closed = np.vstack([contour, contour[0]])  # Close the contour
                diffs = np.diff(contour_closed, axis=0)
                distances = np.sqrt(np.sum(diffs**2 * np.array(pixel_spacing)**2, axis=1))
                perimeter += np.sum(distances)
        metrics['perimeter_mm'] = perimeter
    else:
        metrics['perimeter_mm'] = 0
    
    # Calculate area preservation
    mask_area_pixels = np.sum(mask)
    mask_area_mm2 = mask_area_pixels * pixel_spacing[0] * pixel_spacing[1]
    metrics['mask_area_mm2'] = mask_area_mm2
    
    return metrics

def time_conversion(mask, converter_kwargs):
    """Time the conversion process."""
    converter = MaskToContourConverter(**converter_kwargs)
    
    start_time = time.time()
    contours = converter.convert_slice(mask, slice_index=0)
    end_time = time.time()
    
    return contours, (end_time - start_time) * 1000  # Return time in milliseconds

print("✅ Helper functions defined successfully")

###############################################################################
# %% 2. Accuracy Threshold Analysis {#accuracy-analysis}

# Create test masks for accuracy analysis
test_masks = {
    'Simple Circle': create_test_mask('circle', 80, 'simple'),
    'Irregular Circle': create_test_mask('circle', 80, 'irregular'),
    'Complex Shape': create_test_mask('circle', 80, 'complex')
}

# Display test masks
fig, axes = plt.subplots(1, 3, figsize=(15, 5))
for idx, (name, mask) in enumerate(test_masks.items()):
    axes[idx].imshow(mask, cmap='gray', alpha=0.8)
    axes[idx].set_title(f'{name}\n({np.sum(mask)} pixels)')
    axes[idx].set_axis_off()

plt.suptitle('Test Masks for Accuracy Analysis', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

print("Test masks created successfully")

# Static accuracy threshold analysis (fallback if widgets not available)
def analyze_accuracy_thresholds():
    """Analyze the impact of different accuracy thresholds."""
    
    # Test different accuracy thresholds
    accuracy_thresholds = [0.1, 0.25, 0.5, 1.0, 2.0]
    pixel_spacing = (1.25, 1.25)  # Typical CT spacing
    
    results = {}
    
    for mask_name, mask in test_masks.items():
        results[mask_name] = []
        
        for accuracy in accuracy_thresholds:
            converter_kwargs = {
                'pixel_spacing': pixel_spacing,
                'accuracy_threshold': accuracy
            }
            
            contours, processing_time = time_conversion(mask, converter_kwargs)
            metrics = analyze_contour_quality(mask, contours, pixel_spacing)
            
            result = {
                'accuracy_threshold': accuracy,
                'contours': contours,
                'processing_time_ms': processing_time,
                **metrics
            }
            results[mask_name].append(result)
    
    return results

# Run the analysis
print("Analyzing accuracy thresholds...")
accuracy_results = analyze_accuracy_thresholds()
print("✅ Analysis complete")

###############################################################################
# %% 



###############################################################################
# %% 



###############################################################################
# %% 



###############################################################################
# %% 