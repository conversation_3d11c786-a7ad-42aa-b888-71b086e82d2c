{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MaskToContourConverter: Parameter Tuning & Optimization\n", "\n", "**Learning Objectives:**\n", "- Master parameter optimization for different clinical scenarios\n", "- Understand the trade-offs between accuracy, performance, and file size\n", "- Learn interactive parameter tuning with real-time visualization\n", "- Implement clinical parameter guidelines for optimal workflow\n", "\n", "**Prerequisites:**\n", "- Basic familiarity with MaskToContourConverter (see `01_basic_mask_to_contour_usage.ipynb`)\n", "- Understanding of clinical structures (see `02_clinical_scenarios.ipynb`)\n", "\n", "**Table of Contents:**\n", "1. [Setup & Environment](#setup)\n", "2. [Accuracy Threshold Analysis](#accuracy-analysis)\n", "3. [Point Optimization Strategies](#point-optimization)\n", "4. [Clinical Parameter Guidelines](#clinical-guidelines)\n", "5. [Advanced Configuration](#advanced-config)\n", "6. [Summary & Best Practices](#summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Environment {#setup}\n", "\n", "First, let's set up our environment with all necessary imports and create helper functions for parameter analysis."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Interactive widgets available\n", "✅ MaskToContourConverter imported successfully\n"]}], "source": ["# Core imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Circle, Rectangle\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Interactive widgets\n", "try:\n", "    import ipywidgets as widgets\n", "    from IPython.display import display, clear_output\n", "    WIDGETS_AVAILABLE = True\n", "    print(\"✅ Interactive widgets available\")\n", "except ImportError:\n", "    WIDGETS_AVAILABLE = False\n", "    print(\"⚠️  Interactive widgets not available - install ipywidgets for full functionality\")\n", "\n", "# pyrt-dicom imports\n", "try:\n", "    from pyrt_dicom.utils.contour_processing import MaskToContourConverter\n", "    print(\"✅ MaskToContourConverter imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"Please ensure pyrt-dicom is installed: pip install -e .\")\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Helper functions defined successfully\n"]}], "source": ["# Helper functions for parameter analysis\n", "\n", "def create_test_mask(shape='circle', size=100, complexity='simple'):\n", "    \"\"\"Create test masks with varying complexity for parameter testing.\"\"\"\n", "    mask = np.zeros((200, 200), dtype=bool)\n", "    center = (100, 100)\n", "    \n", "    if shape == 'circle':\n", "        y, x = np.ogrid[:200, :200]\n", "        dist_from_center = np.sqrt((x - center[0])**2 + (y - center[1])**2)\n", "        \n", "        if complexity == 'simple':\n", "            mask = dist_from_center <= size/2\n", "        elif complexity == 'irregular':\n", "            # Add irregular boundary with noise\n", "            noise = np.random.normal(0, 5, (200, 200))\n", "            mask = dist_from_center <= (size/2 + noise)\n", "        elif complexity == 'complex':\n", "            # Multiple circles with holes\n", "            mask1 = dist_from_center <= size/2\n", "            mask2 = dist_from_center <= size/4\n", "            mask3 = np.sqrt((x - 80)**2 + (y - 80)**2) <= 15\n", "            mask = mask1 & ~mask2 | mask3\n", "    \n", "    elif shape == 'rectangle':\n", "        if complexity == 'simple':\n", "            mask[center[1]-size//2:center[1]+size//2, \n", "                 center[0]-size//2:center[0]+size//2] = True\n", "        elif complexity == 'irregular':\n", "            # Rectangle with jagged edges\n", "            for i in range(center[1]-size//2, center[1]+size//2):\n", "                jitter = int(np.random.normal(0, 3))\n", "                mask[i, center[0]-size//2+jitter:center[0]+size//2+jitter] = True\n", "    \n", "    return mask\n", "\n", "def analyze_contour_quality(mask, contours, pixel_spacing=(1.0, 1.0)):\n", "    \"\"\"Analyze contour quality metrics.\"\"\"\n", "    metrics = {}\n", "    \n", "    # Calculate total points\n", "    total_points = sum(len(contour) for contour in contours)\n", "    metrics['total_points'] = total_points\n", "    \n", "    # Estimate file size impact (rough approximation)\n", "    # Each point ~16 bytes in DICOM (x,y,z as 8-byte doubles)\n", "    estimated_size_bytes = total_points * 16\n", "    metrics['estimated_size_kb'] = estimated_size_bytes / 1024\n", "    \n", "    # Calculate perimeter accuracy\n", "    if contours:\n", "        perimeter = 0\n", "        for contour in contours:\n", "            if len(contour) > 2:\n", "                # Calculate perimeter of this contour\n", "                contour_closed = np.vstack([contour, contour[0]])  # Close the contour\n", "                diffs = np.diff(contour_closed, axis=0)\n", "                distances = np.sqrt(np.sum(diffs**2 * np.array(pixel_spacing)**2, axis=1))\n", "                perimeter += np.sum(distances)\n", "        metrics['perimeter_mm'] = perimeter\n", "    else:\n", "        metrics['perimeter_mm'] = 0\n", "    \n", "    # Calculate area preservation\n", "    mask_area_pixels = np.sum(mask)\n", "    mask_area_mm2 = mask_area_pixels * pixel_spacing[0] * pixel_spacing[1]\n", "    metrics['mask_area_mm2'] = mask_area_mm2\n", "    \n", "    return metrics\n", "\n", "def time_conversion(mask, converter_kwargs):\n", "    \"\"\"Time the conversion process.\"\"\"\n", "    converter = MaskToContourConverter(**converter_kwargs)\n", "    \n", "    start_time = time.time()\n", "    contour_sequences = converter.convert_mask_to_contours(\n", "        mask=mask_3d, slice_positions=[0.0]  # Single slice at z=0\n", "    )\n", "    end_time = time.time()\n", "    \n", "    return contours, (end_time - start_time) * 1000  # Return time in milliseconds\n", "\n", "print(\"✅ Helper functions defined successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Accuracy Threshold Analysis {#accuracy-analysis}\n", "\n", "The accuracy threshold is one of the most critical parameters affecting contour quality, file size, and processing time. Let's explore its impact interactively."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Test masks created successfully\n"]}], "source": ["# Create test masks for accuracy analysis\n", "test_masks = {\n", "    'Simple Circle': create_test_mask('circle', 80, 'simple'),\n", "    'Irregular Circle': create_test_mask('circle', 80, 'irregular'),\n", "    'Complex Shape': create_test_mask('circle', 80, 'complex')\n", "}\n", "\n", "# Display test masks\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "for idx, (name, mask) in enumerate(test_masks.items()):\n", "    axes[idx].imshow(mask, cmap='gray', alpha=0.8)\n", "    axes[idx].set_title(f'{name}\\n({np.sum(mask)} pixels)')\n", "    axes[idx].set_axis_off()\n", "\n", "plt.suptitle('Test Masks for Accuracy Analysis', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Test masks created successfully\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing accuracy thresholds...\n"]}, {"ename": "NameError", "evalue": "name 'mask_3d' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 35\u001b[39m\n\u001b[32m     33\u001b[39m \u001b[38;5;66;03m# Run the analysis\u001b[39;00m\n\u001b[32m     34\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAnalyzing accuracy thresholds...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m accuracy_results = \u001b[43manalyze_accuracy_thresholds\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ Analysis complete\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 20\u001b[39m, in \u001b[36manalyze_accuracy_thresholds\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m accuracy \u001b[38;5;129;01min\u001b[39;00m accuracy_thresholds:\n\u001b[32m     15\u001b[39m     converter_kwargs = {\n\u001b[32m     16\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mpixel_spacing\u001b[39m\u001b[33m'\u001b[39m: pixel_spacing,\n\u001b[32m     17\u001b[39m         \u001b[33m'\u001b[39m\u001b[33maccuracy_threshold\u001b[39m\u001b[33m'\u001b[39m: accuracy\n\u001b[32m     18\u001b[39m     }\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m     contours, processing_time = \u001b[43mtime_conversion\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmask\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconverter_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     21\u001b[39m     metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n\u001b[32m     23\u001b[39m     result = {\n\u001b[32m     24\u001b[39m         \u001b[33m'\u001b[39m\u001b[33maccuracy_threshold\u001b[39m\u001b[33m'\u001b[39m: accuracy,\n\u001b[32m     25\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mcontours\u001b[39m\u001b[33m'\u001b[39m: contours,\n\u001b[32m     26\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mprocessing_time_ms\u001b[39m\u001b[33m'\u001b[39m: processing_time,\n\u001b[32m     27\u001b[39m         **metrics\n\u001b[32m     28\u001b[39m     }\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 77\u001b[39m, in \u001b[36mtime_conversion\u001b[39m\u001b[34m(mask, converter_kwargs)\u001b[39m\n\u001b[32m     73\u001b[39m converter = MaskToContourConverter(**converter_kwargs)\n\u001b[32m     75\u001b[39m start_time = time.time()\n\u001b[32m     76\u001b[39m contour_sequences = converter.convert_mask_to_contours(\n\u001b[32m---> \u001b[39m\u001b[32m77\u001b[39m     mask=\u001b[43mmask_3d\u001b[49m, slice_positions=[\u001b[32m0.0\u001b[39m]  \u001b[38;5;66;03m# Single slice at z=0\u001b[39;00m\n\u001b[32m     78\u001b[39m )\n\u001b[32m     79\u001b[39m end_time = time.time()\n\u001b[32m     81\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m contours, (end_time - start_time) * \u001b[32m1000\u001b[39m\n", "\u001b[31mNameError\u001b[39m: name 'mask_3d' is not defined"]}], "source": ["# Static accuracy threshold analysis (fallback if widgets not available)\n", "def analyze_accuracy_thresholds():\n", "    \"\"\"Analyze the impact of different accuracy thresholds.\"\"\"\n", "    \n", "    # Test different accuracy thresholds\n", "    accuracy_thresholds = [0.1, 0.25, 0.5, 1.0, 2.0]\n", "    pixel_spacing = (1.25, 1.25)  # Typical CT spacing\n", "    \n", "    results = {}\n", "    \n", "    for mask_name, mask in test_masks.items():\n", "        results[mask_name] = []\n", "        \n", "        for accuracy in accuracy_thresholds:\n", "            converter_kwargs = {\n", "                'pixel_spacing': pixel_spacing,\n", "                'accuracy_threshold': accuracy\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "            \n", "            result = {\n", "                'accuracy_threshold': accuracy,\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                **metrics\n", "            }\n", "            results[mask_name].append(result)\n", "    \n", "    return results\n", "\n", "# Run the analysis\n", "print(\"Analyzing accuracy thresholds...\")\n", "accuracy_results = analyze_accuracy_thresholds()\n", "print(\"✅ Analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize accuracy threshold analysis results\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "mask_names = list(accuracy_results.keys())\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c']\n", "\n", "# Extract data for plotting\n", "for i, mask_name in enumerate(mask_names):\n", "    results = accuracy_results[mask_name]\n", "    \n", "    thresholds = [r['accuracy_threshold'] for r in results]\n", "    points = [r['total_points'] for r in results]\n", "    times = [r['processing_time_ms'] for r in results]\n", "    sizes = [r['estimated_size_kb'] for r in results]\n", "    \n", "    # Plot 1: Points vs Accuracy\n", "    axes[0].plot(thresholds, points, 'o-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "    \n", "    # Plot 2: Processing Time vs Accuracy\n", "    axes[1].plot(thresholds, times, 's-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "    \n", "    # Plot 3: <PERSON> Size vs Accuracy\n", "    axes[2].plot(thresholds, sizes, '^-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "\n", "# Configure subplots\n", "titles = ['Total Points vs Accuracy Threshold', 'Processing Time vs Accuracy Threshold', 'Estimated File Size vs Accuracy Threshold']\n", "ylabels = ['Total Points', 'Processing Time (ms)', 'Estimated Size (KB)']\n", "\n", "for i in range(3):\n", "    axes[i].set_title(titles[i], fontweight='bold')\n", "    axes[i].set_xlabel('Accuracy Threshold (mm)')\n", "    axes[i].set_ylabel(ylabels[i])\n", "    axes[i].legend()\n", "    axes[i].grid(True, alpha=0.3)\n", "    axes[i].set_xscale('log')\n", "\n", "# Visual comparison at different thresholds\n", "comparison_thresholds = [0.1, 0.5, 2.0]\n", "mask_to_show = 'Complex Shape'\n", "mask_data = test_masks[mask_to_show]\n", "\n", "for i, threshold in enumerate(comparison_thresholds):\n", "    ax = axes[3 + i]\n", "    \n", "    # Find the result for this threshold\n", "    result = next(r for r in accuracy_results[mask_to_show] if r['accuracy_threshold'] == threshold)\n", "    contours = result['contours']\n", "    \n", "    # Plot mask and contours\n", "    ax.imshow(mask_data, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "    \n", "    for contour in contours:\n", "        if len(contour) > 2:\n", "            contour_closed = np.vstack([contour, contour[0]])\n", "            ax.plot(contour_closed[:, 0], contour_closed[:, 1], 'r-', linewidth=2)\n", "            ax.scatter(contour[:, 0], contour[:, 1], c='red', s=20, alpha=0.7)\n", "    \n", "    ax.set_title(f'Threshold: {threshold} mm\\n{result[\"total_points\"]} points, {result[\"processing_time_ms\"]:.1f} ms')\n", "    ax.set_aspect('equal')\n", "    ax.set_xlim(50, 150)\n", "    ax.set_ylim(150, 50)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary table\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"ACCURACY THRESHOLD ANALYSIS SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "for mask_name in mask_names:\n", "    print(f\"\\n{mask_name.upper()}:\")\n", "    print(f\"{'Threshold':<12} {'Points':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Efficiency':<12}\")\n", "    print(\"-\" * 55)\n", "    \n", "    results = accuracy_results[mask_name]\n", "    for result in results:\n", "        efficiency = result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        print(f\"{result['accuracy_threshold']:<12.1f} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} {result['estimated_size_kb']:<10.1f} {efficiency:<12.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive Accuracy Threshold Widget\n", "\n", "If you have ipywidgets installed, you can interactively explore the accuracy threshold parameter:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if WIDGETS_AVAILABLE:\n", "    def interactive_accuracy_demo():\n", "        \"\"\"Interactive widget for accuracy threshold exploration.\"\"\"\n", "        \n", "        # Widget controls\n", "        accuracy_slider = widgets.FloatLogSlider(\n", "            value=0.5,\n", "            base=10,\n", "            min=-1,  # 0.1\n", "            max=0.5,  # ~3.16\n", "            step=0.1,\n", "            description='Accuracy:',\n", "            style={'description_width': '100px'}\n", "        )\n", "        \n", "        mask_dropdown = widgets.Dropdown(\n", "            options=list(test_masks.keys()),\n", "            value='Complex Shape',\n", "            description='Test Mask:',\n", "            style={'description_width': '100px'}\n", "        )\n", "        \n", "        output = widgets.Output()\n", "        \n", "        def update_plot(accuracy, mask_name):\n", "            with output:\n", "                clear_output(wait=True)\n", "                \n", "                # Get mask and convert\n", "                mask = test_masks[mask_name]\n", "                pixel_spacing = (1.25, 1.25)\n", "                \n", "                converter_kwargs = {\n", "                    'pixel_spacing': pixel_spacing,\n", "                    'accuracy_threshold': accuracy\n", "                }\n", "                \n", "                contours, processing_time = time_conversion(mask, converter_kwargs)\n", "                metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "                \n", "                # Create visualization\n", "                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "                \n", "                # Left plot: Mask with contours\n", "                ax1.imshow(mask, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "                \n", "                for contour in contours:\n", "                    if len(contour) > 2:\n", "                        contour_closed = np.vstack([contour, contour[0]])\n", "                        ax1.plot(contour_closed[:, 0], contour_closed[:, 1], 'r-', linewidth=2)\n", "                        ax1.scatter(contour[:, 0], contour[:, 1], c='red', s=15, alpha=0.7)\n", "                \n", "                ax1.set_title(f'{mask_name} - Accuracy: {accuracy:.2f} mm')\n", "                ax1.set_aspect('equal')\n", "                ax1.set_xlim(20, 180)\n", "                ax1.set_ylim(180, 20)\n", "                \n", "                # Right plot: Metrics\n", "                ax2.axis('off')\n", "                metrics_text = f\"\"\"\n", "CONVERSION METRICS\n", "{'='*30}\n", "Accuracy Threshold: {accuracy:.2f} mm\n", "Total Points: {metrics['total_points']}\n", "Processing Time: {processing_time:.1f} ms\n", "Estimated Size: {metrics['estimated_size_kb']:.1f} KB\n", "Perimeter: {metrics['perimeter_mm']:.1f} mm\n", "Mask Area: {metrics['mask_area_mm2']:.1f} mm²\n", "\n", "PERFORMANCE RATING\n", "{'='*30}\n", "Points/mm perimeter: {metrics['total_points']/metrics['perimeter_mm']:.2f}\n", "Processing Speed: {1000/processing_time:.0f} conversions/sec\n", "Storage Efficiency: {metrics['mask_area_mm2']/metrics['estimated_size_kb']:.1f} mm²/KB\n", "\"\"\"\n", "                ax2.text(0.05, 0.95, metrics_text, transform=ax2.transAxes, \n", "                        verticalalignment='top', fontfamily='monospace', fontsize=11)\n", "                \n", "                plt.tight_layout()\n", "                plt.show()\n", "        \n", "        # Set up interactive widget\n", "        interactive_widget = widgets.interactive(update_plot, \n", "                                               accuracy=accuracy_slider, \n", "                                               mask_name=mask_dropdown)\n", "        \n", "        display(interactive_widget, output)\n", "    \n", "    print(\"🎛️  Interactive accuracy threshold demo:\")\n", "    interactive_accuracy_demo()\n", "    \n", "else:\n", "    print(\"ℹ️  Install ipywidgets to enable interactive parameter tuning: pip install ipywidgets\")\n", "    print(\"   Jupyter Lab users may also need: jupyter labextension install @jupyter-widgets/jupyterlab-manager\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Point Optimization Strategies {#point-optimization}\n", "\n", "Managing the number of points in contours is crucial for balancing accuracy with file size and processing performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different max_points_per_contour settings\n", "def analyze_point_optimization():\n", "    \"\"\"Analyze the impact of point count limitations.\"\"\"\n", "    \n", "    max_points_settings = [50, 100, 200, 500, 1000, None]  # None = unlimited\n", "    test_mask = test_masks['Complex Shape']  # Use complex shape for this analysis\n", "    pixel_spacing = (1.25, 1.25)\n", "    \n", "    results = []\n", "    \n", "    for max_points in max_points_settings:\n", "        converter_kwargs = {\n", "            'pixel_spacing': pixel_spacing,\n", "            'accuracy_threshold': 0.5,  # Fixed accuracy for this test\n", "            'max_points_per_contour': max_points\n", "        }\n", "        \n", "        contours, processing_time = time_conversion(test_mask, converter_kwargs)\n", "        metrics = analyze_contour_quality(test_mask, contours, pixel_spacing)\n", "        \n", "        result = {\n", "            'max_points': max_points if max_points else 'Unlimited',\n", "            'max_points_numeric': max_points if max_points else 2000,  # For plotting\n", "            'contours': contours,\n", "            'processing_time_ms': processing_time,\n", "            **metrics\n", "        }\n", "        results.append(result)\n", "    \n", "    return results\n", "\n", "print(\"Analyzing point optimization strategies...\")\n", "point_results = analyze_point_optimization()\n", "print(\"✅ Point optimization analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize point optimization results\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "# Extract data\n", "max_points_values = [r['max_points_numeric'] for r in point_results]\n", "total_points = [r['total_points'] for r in point_results]\n", "processing_times = [r['processing_time_ms'] for r in point_results]\n", "file_sizes = [r['estimated_size_kb'] for r in point_results]\n", "perimeters = [r['perimeter_mm'] for r in point_results]\n", "\n", "# Plot 1: Total Points vs Max Points Setting\n", "axes[0].plot(max_points_values, total_points, 'o-', linewidth=2, markersize=8, color='#2E86AB')\n", "axes[0].set_title('Total Points vs Max Points Limit', fontweight='bold')\n", "axes[0].set_xlabel('Max Points per Contour')\n", "axes[0].set_ylabel('Actual Total Points')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].set_xscale('log')\n", "\n", "# Plot 2: Processing Time vs Max Points\n", "axes[1].plot(max_points_values, processing_times, 's-', linewidth=2, markersize=8, color='#A23B72')\n", "axes[1].set_title('Processing Time vs Max Points Limit', fontweight='bold')\n", "axes[1].set_xlabel('Max Points per Contour')\n", "axes[1].set_ylabel('Processing Time (ms)')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].set_xscale('log')\n", "\n", "# Plot 3: <PERSON> Size vs Max Points\n", "axes[2].plot(max_points_values, file_sizes, '^-', linewidth=2, markersize=8, color='#F18F01')\n", "axes[2].set_title('Estimated File <PERSON> vs Max Points Limit', fontweight='bold')\n", "axes[2].set_xlabel('Max Points per Contour')\n", "axes[2].set_ylabel('Estimated Size (KB)')\n", "axes[2].grid(True, alpha=0.3)\n", "axes[2].set_xscale('log')\n", "\n", "# Visual comparison at different point limits\n", "comparison_indices = [0, 2, 5]  # 50, 200, unlimited\n", "test_mask = test_masks['Complex Shape']\n", "\n", "for i, idx in enumerate(comparison_indices):\n", "    ax = axes[3 + i]\n", "    result = point_results[idx]\n", "    contours = result['contours']\n", "    \n", "    # Plot mask and contours\n", "    ax.imshow(test_mask, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "    \n", "    colors = plt.cm.viridis(np.linspace(0, 1, len(contours)))\n", "    for j, contour in enumerate(contours):\n", "        if len(contour) > 2:\n", "            contour_closed = np.vstack([contour, contour[0]])\n", "            ax.plot(contour_closed[:, 0], contour_closed[:, 1], \n", "                   color=colors[j], linewidth=2.5, alpha=0.8)\n", "            ax.scatter(contour[:, 0], contour[:, 1], \n", "                      c=[colors[j]], s=25, alpha=0.9, edgecolors='white', linewidths=0.5)\n", "    \n", "    ax.set_title(f'Max Points: {result[\"max_points\"]}\\n{result[\"total_points\"]} actual points')\n", "    ax.set_aspect('equal')\n", "    ax.set_xlim(50, 150)\n", "    ax.set_ylim(150, 50)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Efficiency analysis\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"POINT OPTIMIZATION ANALYSIS SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(f\"{'Max Points':<12} {'Actual':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Points/mm':<12} {'Efficiency':<12}\")\n", "print(\"-\" * 75)\n", "\n", "for result in point_results:\n", "    points_per_mm = result['total_points'] / result['perimeter_mm'] if result['perimeter_mm'] > 0 else 0\n", "    efficiency = result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "    \n", "    print(f\"{str(result['max_points']):<12} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} \"\n", "          f\"{result['estimated_size_kb']:<10.1f} {points_per_mm:<12.2f} {efficiency:<12.1f}\")\n", "\n", "# Recommendations\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"POINT OPTIMIZATION RECOMMENDATIONS\")\n", "print(\"=\"*80)\n", "\n", "print(\"\"\"\n", "📋 CLINICAL GUIDELINES:\n", "\n", "🔬 STEREOTACTIC/HIGH-PRECISION STRUCTURES:\n", "   • Max Points: 500-1000 per contour\n", "   • Use Case: Critical organs, small lesions, SRS targets\n", "   • Trade-off: Higher accuracy at cost of file size\n", "\n", "🏥 CONVENTIONAL RT STRUCTURES:\n", "   • Max Points: 200-500 per contour  \n", "   • Use Case: Standard PTV, most OARs\n", "   • Trade-off: Balanced accuracy and performance\n", "\n", "⚡ LARGE ORGAN STRUCTURES:\n", "   • Max Points: 100-200 per contour\n", "   • Use Case: Body outline, lungs, liver\n", "   • Trade-off: Optimized for speed and file size\n", "\n", "📊 SCREENING/BATCH PROCESSING:\n", "   • Max Points: 50-100 per contour\n", "   • Use Case: Research, automated screening\n", "   • Trade-off: Maximum speed, minimal accuracy loss\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Clinical Parameter Guidelines {#clinical-guidelines}\n", "\n", "Based on our analysis, let's establish evidence-based parameter guidelines for different clinical scenarios."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define clinical parameter profiles\n", "CLINICAL_PROFILES = {\n", "    'stereotactic_critical': {\n", "        'name': 'Stereotactic Critical Organs',\n", "        'description': 'Ultra-high precision for critical structures in SRS/SBRT',\n", "        'accuracy_threshold': 0.1,  # 0.1 mm\n", "        'max_points_per_contour': 1000,\n", "        'simplification_tolerance': 0.05,\n", "        'use_cases': ['Brainstem', 'Spinal Cord', 'Optic Chiasm', 'Critical nerves'],\n", "        'typical_structures': 'Small, critical OARs',\n", "        'priority': 'Accuracy > Speed > File Size'\n", "    },\n", "    \n", "    'stereotactic_target': {\n", "        'name': 'Stereotactic Targets',\n", "        'description': 'High precision for SRS/SBRT treatment targets',\n", "        'accuracy_threshold': 0.25,  # 0.25 mm\n", "        'max_points_per_contour': 500,\n", "        'simplification_tolerance': 0.1,\n", "        'use_cases': ['GTV', 'PTV for SRS', 'Small metastases'],\n", "        'typical_structures': 'Treatment targets <5cm',\n", "        'priority': 'Accuracy > File Size > Speed'\n", "    },\n", "    \n", "    'conventional_critical': {\n", "        'name': 'Conventional Critical',\n", "        'description': 'Standard precision for important normal tissues',\n", "        'accuracy_threshold': 0.5,  # 0.5 mm\n", "        'max_points_per_contour': 300,\n", "        'simplification_tolerance': 0.2,\n", "        'use_cases': ['Heart', 'Lungs', 'Kidneys', 'Eyes'],\n", "        'typical_structures': 'Important OARs',\n", "        'priority': 'Balanced accuracy and performance'\n", "    },\n", "    \n", "    'conventional_target': {\n", "        'name': 'Conventional Targets',\n", "        'description': 'Standard precision for conventional RT targets',\n", "        'accuracy_threshold': 0.5,  # 0.5 mm\n", "        'max_points_per_contour': 200,\n", "        'simplification_tolerance': 0.25,\n", "        'use_cases': ['CTV', 'PTV for conventional RT', 'Boost volumes'],\n", "        'typical_structures': 'Treatment volumes >5cm',\n", "        'priority': 'Balanced accuracy and speed'\n", "    },\n", "    \n", "    'large_organ': {\n", "        'name': 'Large Organ Structures',\n", "        'description': 'Performance-optimized for large anatomical structures',\n", "        'accuracy_threshold': 1.0,  # 1.0 mm\n", "        'max_points_per_contour': 150,\n", "        'simplification_tolerance': 0.5,\n", "        'use_cases': ['Body outline', 'Liver', 'Large bones', 'External contour'],\n", "        'typical_structures': 'Large anatomical regions',\n", "        'priority': 'Speed > File Size > Accuracy'\n", "    },\n", "    \n", "    'research_batch': {\n", "        'name': 'Research/Batch Processing',\n", "        'description': 'Maximum speed for large-scale analysis',\n", "        'accuracy_threshold': 2.0,  # 2.0 mm\n", "        'max_points_per_contour': 100,\n", "        'simplification_tolerance': 1.0,\n", "        'use_cases': ['Population studies', 'Automated screening', 'Atlas creation'],\n", "        'typical_structures': 'Any structures for research',\n", "        'priority': 'Speed >> File Size >> Accuracy'\n", "    }\n", "}\n", "\n", "def create_converter_from_profile(profile_name, pixel_spacing=(1.25, 1.25), slice_thickness=2.5):\n", "    \"\"\"Create a MaskToContourConverter with clinical profile settings.\"\"\"\n", "    \n", "    if profile_name not in CLINICAL_PROFILES:\n", "        raise ValueError(f\"Unknown profile: {profile_name}. Available: {list(CLINICAL_PROFILES.keys())}\")\n", "    \n", "    profile = CLINICAL_PROFILES[profile_name]\n", "    \n", "    converter_kwargs = {\n", "        'pixel_spacing': pixel_spacing,\n", "        'slice_thickness': slice_thickness,\n", "        'accuracy_threshold': profile['accuracy_threshold'],\n", "        'max_points_per_contour': profile['max_points_per_contour']\n", "    }\n", "    \n", "    return MaskToContourConverter(**converter_kwargs), profile\n", "\n", "print(\"✅ Clinical parameter profiles defined\")\n", "print(f\"Available profiles: {list(CLINICAL_PROFILES.keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test all clinical profiles\n", "def test_clinical_profiles():\n", "    \"\"\"Test all clinical profiles on various mask types.\"\"\"\n", "    \n", "    profile_results = {}\n", "    pixel_spacing = (1.25, 1.25)\n", "    \n", "    for profile_name in CLINICAL_PROFILES.keys():\n", "        profile_results[profile_name] = {}\n", "        \n", "        for mask_name, mask in test_masks.items():\n", "            # Create converter with profile settings\n", "            profile = CLINICAL_PROFILES[profile_name]\n", "            converter_kwargs = {\n", "                'pixel_spacing': pixel_spacing,\n", "                'accuracy_threshold': profile['accuracy_threshold'],\n", "                'max_points_per_contour': profile['max_points_per_contour']\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "            \n", "            profile_results[profile_name][mask_name] = {\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                **metrics\n", "            }\n", "    \n", "    return profile_results\n", "\n", "print(\"Testing all clinical profiles...\")\n", "profile_test_results = test_clinical_profiles()\n", "print(\"✅ Clinical profile testing complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize clinical profile comparison\n", "fig, axes = plt.subplots(2, 3, figsize=(20, 14))\n", "axes = axes.flatten()\n", "\n", "profile_names = list(CLINICAL_PROFILES.keys())\n", "colors = plt.cm.Set3(np.linspace(0, 1, len(profile_names)))\n", "\n", "# Aggregate metrics across all mask types\n", "for i, mask_name in enumerate(test_masks.keys()):\n", "    points_data = []\n", "    times_data = []\n", "    sizes_data = []\n", "    profile_labels = []\n", "    \n", "    for j, profile_name in enumerate(profile_names):\n", "        result = profile_test_results[profile_name][mask_name]\n", "        points_data.append(result['total_points'])\n", "        times_data.append(result['processing_time_ms'])\n", "        sizes_data.append(result['estimated_size_kb'])\n", "        profile_labels.append(profile_name.replace('_', '\\n'))\n", "    \n", "    # Bar plots for each mask type\n", "    x_pos = np.arange(len(profile_names))\n", "    \n", "    if i < 3:  # Points comparison\n", "        bars = axes[i].bar(x_pos, points_data, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "        axes[i].set_title(f'{mask_name}\\nTotal Points by Profile', fontweight='bold')\n", "        axes[i].set_ylabel('Total Points')\n", "        axes[i].set_yscale('log')\n", "        \n", "        # Add value labels on bars\n", "        for bar, value in zip(bars, points_data):\n", "            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1, \n", "                        f'{value}', ha='center', va='bottom', fontsize=8, fontweight='bold')\n", "    \n", "    else:  # Time comparison  \n", "        bars = axes[i].bar(x_pos, times_data, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "        axes[i].set_title(f'{mask_name}\\nProcessing Time by Profile', fontweight='bold')\n", "        axes[i].set_ylabel('Processing Time (ms)')\n", "        \n", "        # Add value labels on bars\n", "        for bar, value in zip(bars, times_data):\n", "            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.05, \n", "                        f'{value:.1f}', ha='center', va='bottom', fontsize=8, fontweight='bold')\n", "    \n", "    axes[i].set_xticks(x_pos)\n", "    axes[i].set_xticklabels(profile_labels, rotation=45, ha='right', fontsize=9)\n", "    axes[i].grid(True, alpha=0.3, axis='y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Clinical Profile Summary Table\n", "print(\"\\n\" + \"=\"*120)\n", "print(\"CLINICAL PARAMETER PROFILES SUMMARY\")\n", "print(\"=\"*120)\n", "\n", "for profile_name, profile in CLINICAL_PROFILES.items():\n", "    print(f\"\\n🏥 {profile['name'].upper()}\")\n", "    print(f\"   Description: {profile['description']}\")\n", "    print(f\"   Accuracy: {profile['accuracy_threshold']} mm | Max Points: {profile['max_points_per_contour']} | Priority: {profile['priority']}\")\n", "    print(f\"   Use Cases: {', '.join(profile['use_cases'])}\")\n", "    \n", "    # Show performance across test masks\n", "    print(f\"   Performance:\")\n", "    for mask_name in test_masks.keys():\n", "        result = profile_test_results[profile_name][mask_name]\n", "        print(f\"     {mask_name}: {result['total_points']} points, {result['processing_time_ms']:.1f} ms, {result['estimated_size_kb']:.1f} KB\")\n", "    print(\"-\" * 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Advanced Configuration {#advanced-config}\n", "\n", "Explore advanced configuration options including custom coordinate transformations and preprocessing strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced configuration examples\n", "def demonstrate_advanced_config():\n", "    \"\"\"Demonstrate advanced configuration options.\"\"\"\n", "    \n", "    # Create a more complex test mask for advanced testing\n", "    complex_mask = np.zeros((300, 300), dtype=bool)\n", "    \n", "    # Create multiple components with different characteristics\n", "    # Main structure\n", "    y, x = np.ogrid[:300, :300]\n", "    main_structure = ((x - 150)**2 + (y - 150)**2) <= 80**2\n", "    \n", "    # Internal hole\n", "    hole = ((x - 150)**2 + (y - 150)**2) <= 30**2\n", "    \n", "    # Separate small structure\n", "    small_structure = ((x - 100)**2 + (y - 100)**2) <= 15**2\n", "    \n", "    # Thin connecting bridge\n", "    bridge = (np.abs(x - y) <= 5) & (x >= 100) & (x <= 150) & (y >= 100) & (y <= 150)\n", "    \n", "    complex_mask = (main_structure & ~hole) | small_structure | bridge\n", "    \n", "    return complex_mask\n", "\n", "# Create advanced test mask\n", "advanced_mask = demonstrate_advanced_config()\n", "\n", "# Display the complex mask\n", "plt.figure(figsize=(10, 8))\n", "plt.imshow(advanced_mask, cmap='gray', alpha=0.8)\n", "plt.title('Advanced Test Mask\\n(Multi-component structure with hole and bridge)', fontweight='bold', fontsize=14)\n", "plt.axis('off')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Advanced test mask created: {np.sum(advanced_mask)} pixels\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different coordinate configurations\n", "def analyze_coordinate_configurations():\n", "    \"\"\"Analyze impact of different coordinate system configurations.\"\"\"\n", "    \n", "    coordinate_configs = [\n", "        {\n", "            'name': 'High-res CT',\n", "            'pixel_spacing': (0.625, 0.625),  # 0.625 mm pixels\n", "            'slice_thickness': 1.25,\n", "            'description': 'High-resolution diagnostic CT'\n", "        },\n", "        {\n", "            'name': 'Standard CT', \n", "            'pixel_spacing': (1.25, 1.25),   # 1.25 mm pixels\n", "            'slice_thickness': 2.5,\n", "            'description': 'Standard treatment planning CT'\n", "        },\n", "        {\n", "            'name': 'Low-res CT',\n", "            'pixel_spacing': (2.0, 2.0),     # 2.0 mm pixels  \n", "            'slice_thickness': 5.0,\n", "            'description': 'Low-resolution or older CT protocol'\n", "        },\n", "        {\n", "            'name': 'MR T1',\n", "            'pixel_spacing': (0.9, 0.9),     # 0.9 mm pixels\n", "            'slice_thickness': 3.0,\n", "            'description': 'Typical MR T1-weighted sequence'\n", "        }\n", "    ]\n", "    \n", "    results = []\n", "    \n", "    for config in coordinate_configs:\n", "        # Use conventional_critical profile as baseline\n", "        profile = CLINICAL_PROFILES['conventional_critical']\n", "        \n", "        converter_kwargs = {\n", "            'pixel_spacing': config['pixel_spacing'],\n", "            'slice_thickness': config['slice_thickness'],\n", "            'accuracy_threshold': profile['accuracy_threshold'],\n", "            'max_points_per_contour': profile['max_points_per_contour']\n", "        }\n", "        \n", "        contours, processing_time = time_conversion(advanced_mask, converter_kwargs)\n", "        metrics = analyze_contour_quality(advanced_mask, contours, config['pixel_spacing'])\n", "        \n", "        result = {\n", "            **config,\n", "            'contours': contours,\n", "            'processing_time_ms': processing_time,\n", "            **metrics\n", "        }\n", "        results.append(result)\n", "    \n", "    return results\n", "\n", "print(\"Analyzing coordinate system configurations...\")\n", "coord_results = analyze_coordinate_configurations()\n", "print(\"✅ Coordinate configuration analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize coordinate configuration results\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Extract data for plotting\n", "config_names = [r['name'] for r in coord_results]\n", "pixel_sizes = [r['pixel_spacing'][0] for r in coord_results]  # Assuming square pixels\n", "total_points = [r['total_points'] for r in coord_results]\n", "perimeters = [r['perimeter_mm'] for r in coord_results]\n", "processing_times = [r['processing_time_ms'] for r in coord_results]\n", "file_sizes = [r['estimated_size_kb'] for r in coord_results]\n", "\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "\n", "# Plot 1: Points vs Pixel Size\n", "axes[0,0].scatter(pixel_sizes, total_points, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[0,0].annotate(name, (pixel_sizes[i], total_points[i]), \n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[0,0].set_xlabel('Pixel Size (mm)')\n", "axes[0,0].set_ylabel('Total Points')\n", "axes[0,0].set_title('Total Points vs Pixel Size', fontweight='bold')\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Perimeter vs Pixel Size  \n", "axes[0,1].scatter(pixel_sizes, perimeters, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[0,1].annotate(name, (pixel_sizes[i], perimeters[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[0,1].set_xlabel('Pixel Size (mm)')\n", "axes[0,1].set_ylabel('Perimeter (mm)')\n", "axes[0,1].set_title('Measured Perimeter vs Pixel Size', fontweight='bold')\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Processing Time vs Pixel Size\n", "axes[1,0].scatter(pixel_sizes, processing_times, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[1,0].annotate(name, (pixel_sizes[i], processing_times[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[1,0].set_xlabel('Pixel Size (mm)')\n", "axes[1,0].set_ylabel('Processing Time (ms)')\n", "axes[1,0].set_title('Processing Time vs Pixel Size', fontweight='bold')\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Visual comparison of contours\n", "axes[1,1].imshow(advanced_mask, cmap='gray', alpha=0.6, extent=[0, 300, 300, 0])\n", "\n", "# Show contours from two different resolutions for comparison\n", "high_res_contours = coord_results[0]['contours']  # High-res CT\n", "low_res_contours = coord_results[2]['contours']   # Low-res CT\n", "\n", "# Plot high-res contours in blue\n", "for contour in high_res_contours:\n", "    if len(contour) > 2:\n", "        contour_closed = np.vstack([contour, contour[0]])\n", "        axes[1,1].plot(contour_closed[:, 0], contour_closed[:, 1], 'b-', linewidth=2, alpha=0.8, label='High-res (0.625mm)')\n", "\n", "# Plot low-res contours in red\n", "for contour in low_res_contours:\n", "    if len(contour) > 2:\n", "        contour_closed = np.vstack([contour, contour[0]])\n", "        axes[1,1].plot(contour_closed[:, 0], contour_closed[:, 1], 'r--', linewidth=2, alpha=0.8, label='Low-res (2.0mm)')\n", "\n", "axes[1,1].set_title('Contour Comparison: High-res vs Low-res', fontweight='bold')\n", "axes[1,1].set_xlim(50, 250)\n", "axes[1,1].set_ylim(250, 50)\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Detailed results table\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"COORDINATE CONFIGURATION ANALYSIS\")\n", "print(\"=\"*100)\n", "\n", "print(f\"{'Configuration':<15} {'Pixel(mm)':<12} {'Slice(mm)':<12} {'Points':<8} {'Perimeter':<12} {'Time(ms)':<10} {'Size(KB)':<10}\")\n", "print(\"-\" * 95)\n", "\n", "for result in coord_results:\n", "    print(f\"{result['name']:<15} {result['pixel_spacing'][0]:<12.3f} {result['slice_thickness']:<12.1f} \"\n", "          f\"{result['total_points']:<8} {result['perimeter_mm']:<12.1f} {result['processing_time_ms']:<10.1f} \"\n", "          f\"{result['estimated_size_kb']:<10.1f}\")\n", "\n", "print(\"\\n📋 KEY OBSERVATIONS:\")\n", "print(\"• Higher resolution → More points, better geometric accuracy\")\n", "print(\"• Lower resolution → Faster processing, smaller files, some accuracy loss\")\n", "print(\"• Perimeter measurements affected by pixel size resolution\")\n", "print(\"• Processing time scales roughly with number of detected edge pixels\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error Handling and Recovery Strategies\n", "\n", "Let's demonstrate robust error handling for various edge cases:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demonstrate error handling and recovery\n", "def demonstrate_error_handling():\n", "    \"\"\"Test error handling with various problematic inputs.\"\"\"\n", "    \n", "    test_cases = {\n", "        'empty_mask': np.zeros((100, 100), dtype=bool),\n", "        'single_pixel': np.zeros((100, 100), dtype=bool),\n", "        'thin_line': np.zeros((100, 100), dtype=bool),\n", "        'all_true': np.ones((100, 100), dtype=bool),\n", "        'tiny_mask': np.zeros((5, 5), dtype=bool)\n", "    }\n", "    \n", "    # Set up specific problematic cases\n", "    test_cases['single_pixel'][50, 50] = True\n", "    test_cases['thin_line'][50, 20:80] = True  # 1-pixel wide line\n", "    test_cases['tiny_mask'][2, 2] = True\n", "    \n", "    results = {}\n", "    \n", "    for case_name, mask in test_cases.items():\n", "        print(f\"\\nTesting: {case_name} ({np.sum(mask)} pixels)\")\n", "        \n", "        try:\n", "            # Use conventional_target profile\n", "            profile = CLINICAL_PROFILES['conventional_target']\n", "            converter_kwargs = {\n", "                'pixel_spacing': (1.25, 1.25),\n", "                'accuracy_threshold': profile['accuracy_threshold'],\n", "                'max_points_per_contour': profile['max_points_per_contour']\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, (1.25, 1.25))\n", "            \n", "            results[case_name] = {\n", "                'success': True,\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                'error': None,\n", "                **metrics\n", "            }\n", "            \n", "            print(f\"  ✅ Success: {len(contours)} contours, {metrics['total_points']} points\")\n", "            \n", "        except Exception as e:\n", "            results[case_name] = {\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'contours': [],\n", "                'processing_time_ms': 0,\n", "                'error': str(e),\n", "                'total_points': 0\n", "            }\n", "            \n", "            print(f\"  ❌ Error: {e}\")\n", "    \n", "    return test_cases, results\n", "\n", "# Run error handling tests\n", "print(\"Testing error handling and edge cases...\")\n", "edge_cases, edge_results = demonstrate_error_handling()\n", "print(\"\\n✅ Error handling tests complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize edge cases and their handling\n", "fig, axes = plt.subplots(2, len(edge_cases), figsize=(20, 8))\n", "if len(edge_cases) == 1:\n", "    axes = axes.reshape(-1, 1)\n", "\n", "for i, (case_name, mask) in enumerate(edge_cases.items()):\n", "    result = edge_results[case_name]\n", "    \n", "    # Top row: Input masks\n", "    axes[0, i].imshow(mask, cmap='gray', alpha=0.8)\n", "    axes[0, i].set_title(f'{case_name.replace(\"_\", \" \").title()}\\n{np.sum(mask)} pixels')\n", "    axes[0, i].set_axis_off()\n", "    \n", "    # Bottom row: Results with contours\n", "    axes[1, i].imshow(mask, cmap='gray', alpha=0.6)\n", "    \n", "    if result['success'] and result['contours']:\n", "        colors = plt.cm.viridis(np.linspace(0, 1, len(result['contours'])))\n", "        for j, contour in enumerate(result['contours']):\n", "            if len(contour) > 2:\n", "                contour_closed = np.vstack([contour, contour[0]])\n", "                axes[1, i].plot(contour_closed[:, 0], contour_closed[:, 1], \n", "                               color=colors[j], linewidth=2, alpha=0.9)\n", "                axes[1, i].scatter(contour[:, 0], contour[:, 1], \n", "                                 c=[colors[j]], s=30, alpha=0.8, edgecolors='white')\n", "        \n", "        status = f\"✅ {len(result['contours'])} contours\\n{result['total_points']} points\"\n", "        color = 'green'\n", "    else:\n", "        if result['success']:\n", "            status = \"⚠️ No contours\\nEmpty result\"\n", "            color = 'orange'\n", "        else:\n", "            status = f\"❌ Error\\n{result['error'][:20]}...\"\n", "            color = 'red'\n", "    \n", "    axes[1, i].set_title(status, color=color, fontweight='bold')\n", "    axes[1, i].set_axis_off()\n", "\n", "plt.suptitle('Edge Case Handling: Input Masks and Conversion Results', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Error handling summary\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EDGE CASE HANDLING SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "success_count = sum(1 for r in edge_results.values() if r['success'])\n", "total_count = len(edge_results)\n", "\n", "print(f\"\\n📊 OVERALL RESULTS: {success_count}/{total_count} cases handled successfully ({100*success_count/total_count:.1f}%)\\n\")\n", "\n", "for case_name, result in edge_results.items():\n", "    print(f\"🔸 {case_name.replace('_', ' ').title()}:\")\n", "    if result['success']:\n", "        if result['total_points'] > 0:\n", "            print(f\"   ✅ Successfully processed → {len(result['contours'])} contours, {result['total_points']} points\")\n", "        else:\n", "            print(f\"   ⚠️  Processed but no contours generated (expected for empty/invalid masks)\")\n", "    else:\n", "        print(f\"   ❌ Error: {result['error']}\")\n", "    print()\n", "\n", "print(\"\\n📋 ROBUST HANDLING GUIDELINES:\")\n", "print(\"\"\"\n", "🛡️ PREPROCESSING STRATEGIES:\n", "   • Validate mask dimensions and content before conversion\n", "   • Remove single-pixel artifacts with morphological operations\n", "   • Check for minimum structure size requirements\n", "   • Apply median filtering for noisy masks\n", "\n", "⚡ PERFORMANCE OPTIMIZATION:\n", "   • Skip conversion for empty masks (0 pixels)\n", "   • Use faster settings for very large masks (>100K pixels)\n", "   • Implement timeout for extremely complex structures\n", "   • Cache results for repeated conversions\n", "\n", "🔧 ERROR RECOVERY:\n", "   • Graceful fallback to simpler algorithms for problematic cases\n", "   • Provide meaningful error messages with suggested solutions\n", "   • Log processing statistics for performance monitoring\n", "   • Implement retry logic with relaxed parameters\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Summary & Best Practices {#summary}\n", "\n", "Key takeaways and recommendations for optimal MaskToContourConverter usage in clinical workflows."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive performance comparison chart\n", "def create_performance_matrix():\n", "    \"\"\"Create a comprehensive performance matrix for all tested configurations.\"\"\"\n", "    \n", "    # Compile data from all our analyses\n", "    matrix_data = []\n", "    \n", "    # Clinical profiles on complex shape\n", "    for profile_name, profile in CLINICAL_PROFILES.items():\n", "        result = profile_test_results[profile_name]['Complex Shape']\n", "        matrix_data.append({\n", "            'Configuration': f\"{profile['name']}\",\n", "            'Category': 'Clinical Profile',\n", "            'Accuracy (mm)': profile['accuracy_threshold'],\n", "            'Max Points': profile['max_points_per_contour'],\n", "            'Actual Points': result['total_points'],\n", "            'Time (ms)': result['processing_time_ms'],\n", "            'Size (KB)': result['estimated_size_kb'],\n", "            'Efficiency': result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        })\n", "    \n", "    # Coordinate configurations\n", "    for result in coord_results:\n", "        matrix_data.append({\n", "            'Configuration': result['name'],\n", "            'Category': 'Coordinate System',\n", "            'Accuracy (mm)': 0.5,  # Fixed for this test\n", "            'Max Points': 300,     # Fixed for this test\n", "            'Actual Points': result['total_points'],\n", "            'Time (ms)': result['processing_time_ms'],\n", "            'Size (KB)': result['estimated_size_kb'],\n", "            'Efficiency': result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        })\n", "    \n", "    return matrix_data\n", "\n", "performance_matrix = create_performance_matrix()\n", "\n", "# Create performance visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(18, 14))\n", "\n", "# Separate clinical profiles and coordinate systems\n", "clinical_data = [d for d in performance_matrix if d['Category'] == 'Clinical Profile']\n", "coord_data = [d for d in performance_matrix if d['Category'] == 'Coordinate System']\n", "\n", "# Plot 1: Clinical Profiles - Points vs Time\n", "clinical_points = [d['Actual Points'] for d in clinical_data]\n", "clinical_times = [d['Time (ms)'] for d in clinical_data]\n", "clinical_names = [d['Configuration'] for d in clinical_data]\n", "\n", "scatter1 = axes[0,0].scatter(clinical_times, clinical_points, s=150, alpha=0.7, \n", "                            c=range(len(clinical_data)), cmap='viridis', edgecolors='black')\n", "for i, name in enumerate(clinical_names):\n", "    axes[0,0].annotate(name.replace(' ', '\\n'), (clinical_times[i], clinical_points[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')\n", "axes[0,0].set_xlabel('Processing Time (ms)')\n", "axes[0,0].set_ylabel('Total Points')\n", "axes[0,0].set_title('Clinical Profiles: Points vs Processing Time', fontweight='bold')\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Clinical Profiles - Accuracy vs File Size\n", "clinical_accuracy = [d['Accuracy (mm)'] for d in clinical_data]\n", "clinical_sizes = [d['Size (KB)'] for d in clinical_data]\n", "\n", "scatter2 = axes[0,1].scatter(clinical_accuracy, clinical_sizes, s=150, alpha=0.7,\n", "                            c=range(len(clinical_data)), cmap='viridis', edgecolors='black')\n", "for i, name in enumerate(clinical_names):\n", "    axes[0,1].annotate(name.replace(' ', '\\n'), (clinical_accuracy[i], clinical_sizes[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')\n", "axes[0,1].set_xlabel('Accuracy Threshold (mm)')\n", "axes[0,1].set_ylabel('Estimated File Size (KB)')\n", "axes[0,1].set_title('Clinical Profiles: Accuracy vs File Size', fontweight='bold')\n", "axes[0,1].set_xscale('log')\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Coordinate Systems Performance\n", "coord_efficiency = [d['Efficiency'] for d in coord_data]\n", "coord_names = [d['Configuration'] for d in coord_data]\n", "coord_points = [d['Actual Points'] for d in coord_data]\n", "\n", "bars = axes[1,0].bar(range(len(coord_data)), coord_efficiency, \n", "                    color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.8)\n", "axes[1,0].set_xticks(range(len(coord_data)))\n", "axes[1,0].set_xticklabels(coord_names, rotation=45, ha='right')\n", "axes[1,0].set_ylabel('Efficiency (Points/ms)')\n", "axes[1,0].set_title('Coordinate Systems: Processing Efficiency', fontweight='bold')\n", "axes[1,0].grid(True, alpha=0.3, axis='y')\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, coord_efficiency):\n", "    axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.02,\n", "                  f'{value:.1f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 4: Overall Performance Comparison\n", "all_configs = [d['Configuration'] for d in performance_matrix]\n", "all_efficiency = [d['Efficiency'] for d in performance_matrix]\n", "categories = [d['Category'] for d in performance_matrix]\n", "\n", "# Color by category\n", "colors = ['#FF9999' if cat == 'Clinical Profile' else '#9999FF' for cat in categories]\n", "\n", "bars = axes[1,1].barh(range(len(performance_matrix)), all_efficiency, color=colors, alpha=0.8)\n", "axes[1,1].set_yticks(range(len(performance_matrix)))\n", "axes[1,1].set_yticklabels([c.replace(' ', '\\n') for c in all_configs], fontsize=8)\n", "axes[1,1].set_xlabel('Efficiency (Points/ms)')\n", "axes[1,1].set_title('Overall Performance Comparison', fontweight='bold')\n", "axes[1,1].grid(True, alpha=0.3, axis='x')\n", "\n", "# Add legend\n", "from matplotlib.patches import Patch\n", "legend_elements = [Patch(facecolor='#FF9999', alpha=0.8, label='Clinical Profile'),\n", "                  Patch(facecolor='#9999FF', alpha=0.8, label='Coordinate System')]\n", "axes[1,1].legend(handles=legend_elements, loc='lower right')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Comprehensive performance analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final recommendations and best practices summary\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"🎯 MASKTOCONTOURCONVERTER: PARAMETER OPTIMIZATION BEST PRACTICES\")\n", "print(\"=\"*100)\n", "\n", "print(\"\"\"\n", "🏆 OPTIMAL PARAMETER SELECTION STRATEGY:\n", "\n", "1️⃣ IDENTIFY STRUCTURE TYPE:\n", "   • Critical organs requiring sub-mm precision → stereotactic_critical profile\n", "   • Treatment targets needing high accuracy → stereotactic_target profile  \n", "   • Standard clinical structures → conventional_critical or conventional_target\n", "   • Large anatomical regions → large_organ profile\n", "   • Research/batch processing → research_batch profile\n", "\n", "2️⃣ CONSIDER IMAGING PARAMETERS:\n", "   • High-resolution CT (≤1mm pixels) → Can use tighter accuracy thresholds\n", "   • Standard CT (1-2mm pixels) → Balance accuracy with performance  \n", "   • Low-resolution imaging (>2mm pixels) → Focus on efficiency over precision\n", "\n", "3️⃣ BALANCE COMPETING PRIORITIES:\n", "   • Accuracy ↔ File Size: Lower accuracy threshold = more points = larger files\n", "   • Speed ↔ Precision: Higher max points = better accuracy = slower processing\n", "   • Memory ↔ Quality: More points = higher memory usage during processing\n", "\n", "\"\"\")\n", "\n", "print(\"\\n📊 EVIDENCE-BASED RECOMMENDATIONS:\")\n", "print(\"-\" * 50)\n", "\n", "# Calculate best performers in each category\n", "clinical_profiles = [d for d in performance_matrix if d['Category'] == 'Clinical Profile']\n", "\n", "# Find best balance (efficiency vs accuracy)\n", "best_balance = max(clinical_profiles, key=lambda x: x['Efficiency'] / x['Accuracy (mm)'])\n", "fastest = max(clinical_profiles, key=lambda x: x['Efficiency'])\n", "most_accurate = min(clinical_profiles, key=lambda x: x['Accuracy (mm)'])\n", "smallest_files = min(clinical_profiles, key=lambda x: x['Size (KB)'])\n", "\n", "print(f\"\"\"\n", "🏅 TOP PERFORMERS:\n", "\n", "⚖️  BEST OVERALL BALANCE: {best_balance['Configuration']}\n", "   • Accuracy: {best_balance['Accuracy (mm)']} mm\n", "   • Processing: {best_balance['Time (ms)']:.1f} ms\n", "   • File size: {best_balance['Size (KB)']:.1f} KB\n", "   • Efficiency: {best_balance['Efficiency']:.1f} points/ms\n", "\n", "⚡ FASTEST PROCESSING: {fastest['Configuration']}\n", "   • Efficiency: {fastest['Efficiency']:.1f} points/ms\n", "   • Processing: {fastest['Time (ms)']:.1f} ms\n", "   • Trade-off: {fastest['Accuracy (mm)']} mm accuracy\n", "\n", "🎯 HIGHEST ACCURACY: {most_accurate['Configuration']}\n", "   • Accuracy: {most_accurate['Accuracy (mm)']} mm\n", "   • Points: {most_accurate['Actual Points']}\n", "   • Trade-off: {most_accurate['Time (ms)']:.1f} ms processing\n", "\n", "💾 SMALLEST FILES: {smallest_files['Configuration']}\n", "   • File size: {smallest_files['Size (KB)']:.1f} KB\n", "   • Points: {smallest_files['Actual Points']}\n", "   • Trade-off: {smallest_files['Accuracy (mm)']} mm accuracy\n", "\"\"\")\n", "\n", "print(\"\\n🛠️ IMPLEMENTATION CHECKLIST:\")\n", "print(\"-\" * 30)\n", "\n", "print(\"\"\"\n", "✅ PRE-PROCESSING:\n", "   □ Validate mask dimensions and content\n", "   □ Check pixel spacing and slice thickness values\n", "   □ Remove single-pixel artifacts if needed\n", "   □ Verify mask has sufficient resolution for accuracy requirements\n", "\n", "✅ PARAMETER SELECTION:\n", "   □ Choose clinical profile based on structure importance and size\n", "   □ Adjust accuracy threshold based on imaging resolution\n", "   □ Set max points based on file size constraints\n", "   □ Consider processing time requirements for workflow\n", "\n", "✅ QUALITY ASSURANCE:\n", "   □ Verify contour closure for DICOM compliance\n", "   □ Check geometric accuracy against known structures\n", "   □ Validate volume preservation within acceptable limits\n", "   □ Monitor processing performance and file sizes\n", "\n", "✅ ERROR HANDLING:\n", "   □ Implement graceful handling of empty masks\n", "   □ Set up fallback parameters for problematic cases\n", "   □ Log conversion statistics for monitoring\n", "   □ Provide meaningful error messages to users\n", "\"\"\")\n", "\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"📚 SUMMARY: PARAMETER TUNING & OPTIMIZATION MASTERY ACHIEVED\")\n", "print(\"=\"*100)\n", "\n", "print(f\"\"\"\n", "This notebook has provided comprehensive guidance for optimizing MaskToContourConverter \n", "parameters across diverse clinical scenarios. You now have:\n", "\n", "📈 Evidence-based parameter selection strategies\n", "🎛️ Interactive tools for real-time parameter exploration  \n", "🏥 Clinical profiles for standardized workflows\n", "⚙️ Advanced configuration options for specialized needs\n", "🛡️ Robust error handling for production environments\n", "\n", "The combination of accuracy analysis, point optimization, clinical guidelines, and \n", "advanced configuration provides a complete framework for achieving optimal \n", "mask-to-contour conversion performance in any radiotherapy workflow.\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## Next Steps\n", "\n", "Continue your MaskToContourConverter learning journey:\n", "\n", "- **Complex Geometry & Edge Cases** → `04_complex_geometry_edge_cases.ipynb`\n", "- **Integration & Comparisons** → `05_integration_comparisons.ipynb`\n", "- **Return to Basics** → `01_basic_mask_to_contour_usage.ipynb`\n", "- **Clinical Applications** → `02_clinical_scenarios.ipynb`\n", "\n", "---\n", "\n", "**📧 Questions or Issues?**\n", "- Check the pyrt-dicom documentation\n", "- Review the source code in `src/pyrt_dicom/utils/contour_processing.py`\n", "- Refer to the comprehensive test suite for additional examples\n", "\n", "**🎯 Key Takeaway:**\n", "Parameter optimization is about finding the right balance between accuracy, performance, and file size for your specific clinical workflow. Use the evidence-based profiles as starting points, then fine-tune based on your requirements and constraints."]}], "metadata": {"kernelspec": {"display_name": "pyrt-dicom", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}