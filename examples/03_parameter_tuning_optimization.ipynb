{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MaskToContourConverter: Parameter Tuning & Optimization\n", "\n", "**Learning Objectives:**\n", "- Master parameter optimization for different clinical scenarios\n", "- Understand the trade-offs between accuracy, performance, and file size\n", "- Learn interactive parameter tuning with real-time visualization\n", "- Implement clinical parameter guidelines for optimal workflow\n", "\n", "**Prerequisites:**\n", "- Basic familiarity with MaskToContourConverter (see `01_basic_mask_to_contour_usage.ipynb`)\n", "- Understanding of clinical structures (see `02_clinical_scenarios.ipynb`)\n", "\n", "**Table of Contents:**\n", "1. [Setup & Environment](#setup)\n", "2. [Accuracy Threshold Analysis](#accuracy-analysis)\n", "3. [Point Optimization Strategies](#point-optimization)\n", "4. [Clinical Parameter Guidelines](#clinical-guidelines)\n", "5. [Advanced Configuration](#advanced-config)\n", "6. [Summary & Best Practices](#summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Environment {#setup}\n", "\n", "First, let's set up our environment with all necessary imports and create helper functions for parameter analysis."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Interactive widgets available\n", "✅ MaskToContourConverter imported successfully\n"]}], "source": ["# Core imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Circle, Rectangle\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Interactive widgets\n", "try:\n", "    import ipywidgets as widgets\n", "    from IPython.display import display, clear_output\n", "    WIDGETS_AVAILABLE = True\n", "    print(\"✅ Interactive widgets available\")\n", "except ImportError:\n", "    WIDGETS_AVAILABLE = False\n", "    print(\"⚠️  Interactive widgets not available - install ipywidgets for full functionality\")\n", "\n", "# pyrt-dicom imports\n", "try:\n", "    from pyrt_dicom.utils.contour_processing import MaskToContourConverter\n", "    print(\"✅ MaskToContourConverter imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"Please ensure pyrt-dicom is installed: pip install -e .\")\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Helper functions defined successfully\n"]}], "source": ["# Helper functions for parameter analysis\n", "\n", "def create_test_mask(shape='circle', size=100, complexity='simple'):\n", "    \"\"\"Create test masks with varying complexity for parameter testing.\"\"\"\n", "    mask = np.zeros((200, 200), dtype=bool)\n", "    center = (100, 100)\n", "    \n", "    if shape == 'circle':\n", "        y, x = np.ogrid[:200, :200]\n", "        dist_from_center = np.sqrt((x - center[0])**2 + (y - center[1])**2)\n", "        \n", "        if complexity == 'simple':\n", "            mask = dist_from_center <= size/2\n", "        elif complexity == 'irregular':\n", "            # Add irregular boundary with noise\n", "            noise = np.random.normal(0, 5, (200, 200))\n", "            mask = dist_from_center <= (size/2 + noise)\n", "        elif complexity == 'complex':\n", "            # Multiple circles with holes\n", "            mask1 = dist_from_center <= size/2\n", "            mask2 = dist_from_center <= size/4\n", "            mask3 = np.sqrt((x - 80)**2 + (y - 80)**2) <= 15\n", "            mask = mask1 & ~mask2 | mask3\n", "    \n", "    elif shape == 'rectangle':\n", "        if complexity == 'simple':\n", "            mask[center[1]-size//2:center[1]+size//2, \n", "                 center[0]-size//2:center[0]+size//2] = True\n", "        elif complexity == 'irregular':\n", "            # Rectangle with jagged edges\n", "            for i in range(center[1]-size//2, center[1]+size//2):\n", "                jitter = int(np.random.normal(0, 3))\n", "                mask[i, center[0]-size//2+jitter:center[0]+size//2+jitter] = True\n", "    \n", "    return mask\n", "\n", "def analyze_contour_quality(mask, contours, pixel_spacing=(1.0, 1.0)):\n", "    \"\"\"Analyze contour quality metrics.\"\"\"\n", "    metrics = {}\n", "    \n", "    # Calculate total points\n", "    total_points = sum(len(contour) for contour in contours)\n", "    metrics['total_points'] = total_points\n", "    \n", "    # Estimate file size impact (rough approximation)\n", "    # Each point ~16 bytes in DICOM (x,y,z as 8-byte doubles)\n", "    estimated_size_bytes = total_points * 16\n", "    metrics['estimated_size_kb'] = estimated_size_bytes / 1024\n", "    \n", "    # Calculate perimeter accuracy\n", "    if contours:\n", "        perimeter = 0\n", "        for contour in contours:\n", "            if len(contour) > 2:\n", "                # Calculate perimeter of this contour\n", "                contour_closed = np.vstack([contour, contour[0]])  # Close the contour\n", "                diffs = np.diff(contour_closed, axis=0)\n", "                distances = np.sqrt(np.sum(diffs**2 * np.array(pixel_spacing)**2, axis=1))\n", "                perimeter += np.sum(distances)\n", "        metrics['perimeter_mm'] = perimeter\n", "    else:\n", "        metrics['perimeter_mm'] = 0\n", "    \n", "    # Calculate area preservation\n", "    mask_area_pixels = np.sum(mask)\n", "    mask_area_mm2 = mask_area_pixels * pixel_spacing[0] * pixel_spacing[1]\n", "    metrics['mask_area_mm2'] = mask_area_mm2\n", "    \n", "    return metrics\n", "\n", "def time_conversion(mask, converter_kwargs):\n", "    \"\"\"Time the conversion process.\"\"\"\n", "    converter = MaskToContourConverter(**converter_kwargs)\n", "    \n", "    start_time = time.time()\n", "    contours = converter.convert_slice(mask, slice_index=0)\n", "    end_time = time.time()\n", "    \n", "    return contours, (end_time - start_time) * 1000  # Return time in milliseconds\n", "\n", "print(\"✅ Helper functions defined successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Accuracy Threshold Analysis {#accuracy-analysis}\n", "\n", "The accuracy threshold is one of the most critical parameters affecting contour quality, file size, and processing time. Let's explore its impact interactively."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Test masks created successfully\n"]}], "source": ["# Create test masks for accuracy analysis\n", "test_masks = {\n", "    'Simple Circle': create_test_mask('circle', 80, 'simple'),\n", "    'Irregular Circle': create_test_mask('circle', 80, 'irregular'),\n", "    'Complex Shape': create_test_mask('circle', 80, 'complex')\n", "}\n", "\n", "# Display test masks\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "for idx, (name, mask) in enumerate(test_masks.items()):\n", "    axes[idx].imshow(mask, cmap='gray', alpha=0.8)\n", "    axes[idx].set_title(f'{name}\\n({np.sum(mask)} pixels)')\n", "    axes[idx].set_axis_off()\n", "\n", "plt.suptitle('Test Masks for Accuracy Analysis', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Test masks created successfully\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing accuracy thresholds...\n"]}, {"ename": "AttributeError", "evalue": "'MaskToContourConverter' object has no attribute 'convert_slice'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 35\u001b[39m\n\u001b[32m     33\u001b[39m \u001b[38;5;66;03m# Run the analysis\u001b[39;00m\n\u001b[32m     34\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAnalyzing accuracy thresholds...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m accuracy_results = \u001b[43manalyze_accuracy_thresholds\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ Analysis complete\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 20\u001b[39m, in \u001b[36manalyze_accuracy_thresholds\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m accuracy \u001b[38;5;129;01min\u001b[39;00m accuracy_thresholds:\n\u001b[32m     15\u001b[39m     converter_kwargs = {\n\u001b[32m     16\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mpixel_spacing\u001b[39m\u001b[33m'\u001b[39m: pixel_spacing,\n\u001b[32m     17\u001b[39m         \u001b[33m'\u001b[39m\u001b[33maccuracy_threshold\u001b[39m\u001b[33m'\u001b[39m: accuracy\n\u001b[32m     18\u001b[39m     }\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m     contours, processing_time = \u001b[43mtime_conversion\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmask\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconverter_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     21\u001b[39m     metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n\u001b[32m     23\u001b[39m     result = {\n\u001b[32m     24\u001b[39m         \u001b[33m'\u001b[39m\u001b[33maccuracy_threshold\u001b[39m\u001b[33m'\u001b[39m: accuracy,\n\u001b[32m     25\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mcontours\u001b[39m\u001b[33m'\u001b[39m: contours,\n\u001b[32m     26\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mprocessing_time_ms\u001b[39m\u001b[33m'\u001b[39m: processing_time,\n\u001b[32m     27\u001b[39m         **metrics\n\u001b[32m     28\u001b[39m     }\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 76\u001b[39m, in \u001b[36mtime_conversion\u001b[39m\u001b[34m(mask, converter_kwargs)\u001b[39m\n\u001b[32m     73\u001b[39m converter = MaskToContourConverter(**converter_kwargs)\n\u001b[32m     75\u001b[39m start_time = time.time()\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m contours = \u001b[43mconverter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconvert_slice\u001b[49m(mask, slice_index=\u001b[32m0\u001b[39m)\n\u001b[32m     77\u001b[39m end_time = time.time()\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m contours, (end_time - start_time) * \u001b[32m1000\u001b[39m\n", "\u001b[31mAttributeError\u001b[39m: 'MaskToContourConverter' object has no attribute 'convert_slice'"]}], "source": ["# Static accuracy threshold analysis (fallback if widgets not available)\n", "def analyze_accuracy_thresholds():\n", "    \"\"\"Analyze the impact of different accuracy thresholds.\"\"\"\n", "    \n", "    # Test different accuracy thresholds\n", "    accuracy_thresholds = [0.1, 0.25, 0.5, 1.0, 2.0]\n", "    pixel_spacing = (1.25, 1.25)  # Typical CT spacing\n", "    \n", "    results = {}\n", "    \n", "    for mask_name, mask in test_masks.items():\n", "        results[mask_name] = []\n", "        \n", "        for accuracy in accuracy_thresholds:\n", "            converter_kwargs = {\n", "                'pixel_spacing': pixel_spacing,\n", "                'accuracy_threshold': accuracy\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "            \n", "            result = {\n", "                'accuracy_threshold': accuracy,\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                **metrics\n", "            }\n", "            results[mask_name].append(result)\n", "    \n", "    return results\n", "\n", "# Run the analysis\n", "print(\"Analyzing accuracy thresholds...\")\n", "accuracy_results = analyze_accuracy_thresholds()\n", "print(\"✅ Analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize accuracy threshold analysis results\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "mask_names = list(accuracy_results.keys())\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c']\n", "\n", "# Extract data for plotting\n", "for i, mask_name in enumerate(mask_names):\n", "    results = accuracy_results[mask_name]\n", "    \n", "    thresholds = [r['accuracy_threshold'] for r in results]\n", "    points = [r['total_points'] for r in results]\n", "    times = [r['processing_time_ms'] for r in results]\n", "    sizes = [r['estimated_size_kb'] for r in results]\n", "    \n", "    # Plot 1: Points vs Accuracy\n", "    axes[0].plot(thresholds, points, 'o-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "    \n", "    # Plot 2: Processing Time vs Accuracy\n", "    axes[1].plot(thresholds, times, 's-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "    \n", "    # Plot 3: <PERSON> Size vs Accuracy\n", "    axes[2].plot(thresholds, sizes, '^-', color=colors[i], label=mask_name, linewidth=2, markersize=6)\n", "\n", "# Configure subplots\n", "titles = ['Total Points vs Accuracy Threshold', 'Processing Time vs Accuracy Threshold', 'Estimated File Size vs Accuracy Threshold']\n", "ylabels = ['Total Points', 'Processing Time (ms)', 'Estimated Size (KB)']\n", "\n", "for i in range(3):\n", "    axes[i].set_title(titles[i], fontweight='bold')\n", "    axes[i].set_xlabel('Accuracy Threshold (mm)')\n", "    axes[i].set_ylabel(ylabels[i])\n", "    axes[i].legend()\n", "    axes[i].grid(True, alpha=0.3)\n", "    axes[i].set_xscale('log')\n", "\n", "# Visual comparison at different thresholds\n", "comparison_thresholds = [0.1, 0.5, 2.0]\n", "mask_to_show = 'Complex Shape'\n", "mask_data = test_masks[mask_to_show]\n", "\n", "for i, threshold in enumerate(comparison_thresholds):\n", "    ax = axes[3 + i]\n", "    \n", "    # Find the result for this threshold\n", "    result = next(r for r in accuracy_results[mask_to_show] if r['accuracy_threshold'] == threshold)\n", "    contours = result['contours']\n", "    \n", "    # Plot mask and contours\n", "    ax.imshow(mask_data, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "    \n", "    for contour in contours:\n", "        if len(contour) > 2:\n", "            contour_closed = np.vstack([contour, contour[0]])\n", "            ax.plot(contour_closed[:, 0], contour_closed[:, 1], 'r-', linewidth=2)\n", "            ax.scatter(contour[:, 0], contour[:, 1], c='red', s=20, alpha=0.7)\n", "    \n", "    ax.set_title(f'Threshold: {threshold} mm\\n{result[\"total_points\"]} points, {result[\"processing_time_ms\"]:.1f} ms')\n", "    ax.set_aspect('equal')\n", "    ax.set_xlim(50, 150)\n", "    ax.set_ylim(150, 50)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary table\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"ACCURACY THRESHOLD ANALYSIS SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "for mask_name in mask_names:\n", "    print(f\"\\n{mask_name.upper()}:\")\n", "    print(f\"{'Threshold':<12} {'Points':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Efficiency':<12}\")\n", "    print(\"-\" * 55)\n", "    \n", "    results = accuracy_results[mask_name]\n", "    for result in results:\n", "        efficiency = result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        print(f\"{result['accuracy_threshold']:<12.1f} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} {result['estimated_size_kb']:<10.1f} {efficiency:<12.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive Accuracy Threshold Widget\n", "\n", "If you have ipywidgets installed, you can interactively explore the accuracy threshold parameter:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if WIDGETS_AVAILABLE:\n", "    def interactive_accuracy_demo():\n", "        \"\"\"Interactive widget for accuracy threshold exploration.\"\"\"\n", "        \n", "        # Widget controls\n", "        accuracy_slider = widgets.FloatLogSlider(\n", "            value=0.5,\n", "            base=10,\n", "            min=-1,  # 0.1\n", "            max=0.5,  # ~3.16\n", "            step=0.1,\n", "            description='Accuracy:',\n", "            style={'description_width': '100px'}\n", "        )\n", "        \n", "        mask_dropdown = widgets.Dropdown(\n", "            options=list(test_masks.keys()),\n", "            value='Complex Shape',\n", "            description='Test Mask:',\n", "            style={'description_width': '100px'}\n", "        )\n", "        \n", "        output = widgets.Output()\n", "        \n", "        def update_plot(accuracy, mask_name):\n", "            with output:\n", "                clear_output(wait=True)\n", "                \n", "                # Get mask and convert\n", "                mask = test_masks[mask_name]\n", "                pixel_spacing = (1.25, 1.25)\n", "                \n", "                converter_kwargs = {\n", "                    'pixel_spacing': pixel_spacing,\n", "                    'accuracy_threshold': accuracy\n", "                }\n", "                \n", "                contours, processing_time = time_conversion(mask, converter_kwargs)\n", "                metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "                \n", "                # Create visualization\n", "                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "                \n", "                # Left plot: Mask with contours\n", "                ax1.imshow(mask, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "                \n", "                for contour in contours:\n", "                    if len(contour) > 2:\n", "                        contour_closed = np.vstack([contour, contour[0]])\n", "                        ax1.plot(contour_closed[:, 0], contour_closed[:, 1], 'r-', linewidth=2)\n", "                        ax1.scatter(contour[:, 0], contour[:, 1], c='red', s=15, alpha=0.7)\n", "                \n", "                ax1.set_title(f'{mask_name} - Accuracy: {accuracy:.2f} mm')\n", "                ax1.set_aspect('equal')\n", "                ax1.set_xlim(20, 180)\n", "                ax1.set_ylim(180, 20)\n", "                \n", "                # Right plot: Metrics\n", "                ax2.axis('off')\n", "                metrics_text = f\"\"\"\n", "CONVERSION METRICS\n", "{'='*30}\n", "Accuracy Threshold: {accuracy:.2f} mm\n", "Total Points: {metrics['total_points']}\n", "Processing Time: {processing_time:.1f} ms\n", "Estimated Size: {metrics['estimated_size_kb']:.1f} KB\n", "Perimeter: {metrics['perimeter_mm']:.1f} mm\n", "Mask Area: {metrics['mask_area_mm2']:.1f} mm²\n", "\n", "PERFORMANCE RATING\n", "{'='*30}\n", "Points/mm perimeter: {metrics['total_points']/metrics['perimeter_mm']:.2f}\n", "Processing Speed: {1000/processing_time:.0f} conversions/sec\n", "Storage Efficiency: {metrics['mask_area_mm2']/metrics['estimated_size_kb']:.1f} mm²/KB\n", "\"\"\"\n", "                ax2.text(0.05, 0.95, metrics_text, transform=ax2.transAxes, \n", "                        verticalalignment='top', fontfamily='monospace', fontsize=11)\n", "                \n", "                plt.tight_layout()\n", "                plt.show()\n", "        \n", "        # Set up interactive widget\n", "        interactive_widget = widgets.interactive(update_plot, \n", "                                               accuracy=accuracy_slider, \n", "                                               mask_name=mask_dropdown)\n", "        \n", "        display(interactive_widget, output)\n", "    \n", "    print(\"🎛️  Interactive accuracy threshold demo:\")\n", "    interactive_accuracy_demo()\n", "    \n", "else:\n", "    print(\"ℹ️  Install ipywidgets to enable interactive parameter tuning: pip install ipywidgets\")\n", "    print(\"   Jupyter Lab users may also need: jupyter labextension install @jupyter-widgets/jupyterlab-manager\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Point Optimization Strategies {#point-optimization}\n", "\n", "Managing the number of points in contours is crucial for balancing accuracy with file size and processing performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different max_points_per_contour settings\n", "def analyze_point_optimization():\n", "    \"\"\"Analyze the impact of point count limitations.\"\"\"\n", "    \n", "    max_points_settings = [50, 100, 200, 500, 1000, None]  # None = unlimited\n", "    test_mask = test_masks['Complex Shape']  # Use complex shape for this analysis\n", "    pixel_spacing = (1.25, 1.25)\n", "    \n", "    results = []\n", "    \n", "    for max_points in max_points_settings:\n", "        converter_kwargs = {\n", "            'pixel_spacing': pixel_spacing,\n", "            'accuracy_threshold': 0.5,  # Fixed accuracy for this test\n", "            'max_points_per_contour': max_points\n", "        }\n", "        \n", "        contours, processing_time = time_conversion(test_mask, converter_kwargs)\n", "        metrics = analyze_contour_quality(test_mask, contours, pixel_spacing)\n", "        \n", "        result = {\n", "            'max_points': max_points if max_points else 'Unlimited',\n", "            'max_points_numeric': max_points if max_points else 2000,  # For plotting\n", "            'contours': contours,\n", "            'processing_time_ms': processing_time,\n", "            **metrics\n", "        }\n", "        results.append(result)\n", "    \n", "    return results\n", "\n", "print(\"Analyzing point optimization strategies...\")\n", "point_results = analyze_point_optimization()\n", "print(\"✅ Point optimization analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize point optimization results\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "# Extract data\n", "max_points_values = [r['max_points_numeric'] for r in point_results]\n", "total_points = [r['total_points'] for r in point_results]\n", "processing_times = [r['processing_time_ms'] for r in point_results]\n", "file_sizes = [r['estimated_size_kb'] for r in point_results]\n", "perimeters = [r['perimeter_mm'] for r in point_results]\n", "\n", "# Plot 1: Total Points vs Max Points Setting\n", "axes[0].plot(max_points_values, total_points, 'o-', linewidth=2, markersize=8, color='#2E86AB')\n", "axes[0].set_title('Total Points vs Max Points Limit', fontweight='bold')\n", "axes[0].set_xlabel('Max Points per Contour')\n", "axes[0].set_ylabel('Actual Total Points')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].set_xscale('log')\n", "\n", "# Plot 2: Processing Time vs Max Points\n", "axes[1].plot(max_points_values, processing_times, 's-', linewidth=2, markersize=8, color='#A23B72')\n", "axes[1].set_title('Processing Time vs Max Points Limit', fontweight='bold')\n", "axes[1].set_xlabel('Max Points per Contour')\n", "axes[1].set_ylabel('Processing Time (ms)')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].set_xscale('log')\n", "\n", "# Plot 3: <PERSON> Size vs Max Points\n", "axes[2].plot(max_points_values, file_sizes, '^-', linewidth=2, markersize=8, color='#F18F01')\n", "axes[2].set_title('Estimated File <PERSON> vs Max Points Limit', fontweight='bold')\n", "axes[2].set_xlabel('Max Points per Contour')\n", "axes[2].set_ylabel('Estimated Size (KB)')\n", "axes[2].grid(True, alpha=0.3)\n", "axes[2].set_xscale('log')\n", "\n", "# Visual comparison at different point limits\n", "comparison_indices = [0, 2, 5]  # 50, 200, unlimited\n", "test_mask = test_masks['Complex Shape']\n", "\n", "for i, idx in enumerate(comparison_indices):\n", "    ax = axes[3 + i]\n", "    result = point_results[idx]\n", "    contours = result['contours']\n", "    \n", "    # Plot mask and contours\n", "    ax.imshow(test_mask, cmap='gray', alpha=0.6, extent=[0, 200, 200, 0])\n", "    \n", "    colors = plt.cm.viridis(np.linspace(0, 1, len(contours)))\n", "    for j, contour in enumerate(contours):\n", "        if len(contour) > 2:\n", "            contour_closed = np.vstack([contour, contour[0]])\n", "            ax.plot(contour_closed[:, 0], contour_closed[:, 1], \n", "                   color=colors[j], linewidth=2.5, alpha=0.8)\n", "            ax.scatter(contour[:, 0], contour[:, 1], \n", "                      c=[colors[j]], s=25, alpha=0.9, edgecolors='white', linewidths=0.5)\n", "    \n", "    ax.set_title(f'Max Points: {result[\"max_points\"]}\\n{result[\"total_points\"]} actual points')\n", "    ax.set_aspect('equal')\n", "    ax.set_xlim(50, 150)\n", "    ax.set_ylim(150, 50)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Efficiency analysis\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"POINT OPTIMIZATION ANALYSIS SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(f\"{'Max Points':<12} {'Actual':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Points/mm':<12} {'Efficiency':<12}\")\n", "print(\"-\" * 75)\n", "\n", "for result in point_results:\n", "    points_per_mm = result['total_points'] / result['perimeter_mm'] if result['perimeter_mm'] > 0 else 0\n", "    efficiency = result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "    \n", "    print(f\"{str(result['max_points']):<12} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} \"\n", "          f\"{result['estimated_size_kb']:<10.1f} {points_per_mm:<12.2f} {efficiency:<12.1f}\")\n", "\n", "# Recommendations\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"POINT OPTIMIZATION RECOMMENDATIONS\")\n", "print(\"=\"*80)\n", "\n", "print(\"\"\"\n", "📋 CLINICAL GUIDELINES:\n", "\n", "🔬 STEREOTACTIC/HIGH-PRECISION STRUCTURES:\n", "   • Max Points: 500-1000 per contour\n", "   • Use Case: Critical organs, small lesions, SRS targets\n", "   • Trade-off: Higher accuracy at cost of file size\n", "\n", "🏥 CONVENTIONAL RT STRUCTURES:\n", "   • Max Points: 200-500 per contour  \n", "   • Use Case: Standard PTV, most OARs\n", "   • Trade-off: Balanced accuracy and performance\n", "\n", "⚡ LARGE ORGAN STRUCTURES:\n", "   • Max Points: 100-200 per contour\n", "   • Use Case: Body outline, lungs, liver\n", "   • Trade-off: Optimized for speed and file size\n", "\n", "📊 SCREENING/BATCH PROCESSING:\n", "   • Max Points: 50-100 per contour\n", "   • Use Case: Research, automated screening\n", "   • Trade-off: Maximum speed, minimal accuracy loss\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Clinical Parameter Guidelines {#clinical-guidelines}\n", "\n", "Based on our analysis, let's establish evidence-based parameter guidelines for different clinical scenarios."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define clinical parameter profiles\n", "CLINICAL_PROFILES = {\n", "    'stereotactic_critical': {\n", "        'name': 'Stereotactic Critical Organs',\n", "        'description': 'Ultra-high precision for critical structures in SRS/SBRT',\n", "        'accuracy_threshold': 0.1,  # 0.1 mm\n", "        'max_points_per_contour': 1000,\n", "        'simplification_tolerance': 0.05,\n", "        'use_cases': ['Brainstem', 'Spinal Cord', 'Optic Chiasm', 'Critical nerves'],\n", "        'typical_structures': 'Small, critical OARs',\n", "        'priority': 'Accuracy > Speed > File Size'\n", "    },\n", "    \n", "    'stereotactic_target': {\n", "        'name': 'Stereotactic Targets',\n", "        'description': 'High precision for SRS/SBRT treatment targets',\n", "        'accuracy_threshold': 0.25,  # 0.25 mm\n", "        'max_points_per_contour': 500,\n", "        'simplification_tolerance': 0.1,\n", "        'use_cases': ['GTV', 'PTV for SRS', 'Small metastases'],\n", "        'typical_structures': 'Treatment targets <5cm',\n", "        'priority': 'Accuracy > File Size > Speed'\n", "    },\n", "    \n", "    'conventional_critical': {\n", "        'name': 'Conventional Critical',\n", "        'description': 'Standard precision for important normal tissues',\n", "        'accuracy_threshold': 0.5,  # 0.5 mm\n", "        'max_points_per_contour': 300,\n", "        'simplification_tolerance': 0.2,\n", "        'use_cases': ['Heart', 'Lungs', 'Kidneys', 'Eyes'],\n", "        'typical_structures': 'Important OARs',\n", "        'priority': 'Balanced accuracy and performance'\n", "    },\n", "    \n", "    'conventional_target': {\n", "        'name': 'Conventional Targets',\n", "        'description': 'Standard precision for conventional RT targets',\n", "        'accuracy_threshold': 0.5,  # 0.5 mm\n", "        'max_points_per_contour': 200,\n", "        'simplification_tolerance': 0.25,\n", "        'use_cases': ['CTV', 'PTV for conventional RT', 'Boost volumes'],\n", "        'typical_structures': 'Treatment volumes >5cm',\n", "        'priority': 'Balanced accuracy and speed'\n", "    },\n", "    \n", "    'large_organ': {\n", "        'name': 'Large Organ Structures',\n", "        'description': 'Performance-optimized for large anatomical structures',\n", "        'accuracy_threshold': 1.0,  # 1.0 mm\n", "        'max_points_per_contour': 150,\n", "        'simplification_tolerance': 0.5,\n", "        'use_cases': ['Body outline', 'Liver', 'Large bones', 'External contour'],\n", "        'typical_structures': 'Large anatomical regions',\n", "        'priority': 'Speed > File Size > Accuracy'\n", "    },\n", "    \n", "    'research_batch': {\n", "        'name': 'Research/Batch Processing',\n", "        'description': 'Maximum speed for large-scale analysis',\n", "        'accuracy_threshold': 2.0,  # 2.0 mm\n", "        'max_points_per_contour': 100,\n", "        'simplification_tolerance': 1.0,\n", "        'use_cases': ['Population studies', 'Automated screening', 'Atlas creation'],\n", "        'typical_structures': 'Any structures for research',\n", "        'priority': 'Speed >> File Size >> Accuracy'\n", "    }\n", "}\n", "\n", "def create_converter_from_profile(profile_name, pixel_spacing=(1.25, 1.25), slice_thickness=2.5):\n", "    \"\"\"Create a MaskToContourConverter with clinical profile settings.\"\"\"\n", "    \n", "    if profile_name not in CLINICAL_PROFILES:\n", "        raise ValueError(f\"Unknown profile: {profile_name}. Available: {list(CLINICAL_PROFILES.keys())}\")\n", "    \n", "    profile = CLINICAL_PROFILES[profile_name]\n", "    \n", "    converter_kwargs = {\n", "        'pixel_spacing': pixel_spacing,\n", "        'slice_thickness': slice_thickness,\n", "        'accuracy_threshold': profile['accuracy_threshold'],\n", "        'max_points_per_contour': profile['max_points_per_contour']\n", "    }\n", "    \n", "    return MaskToContourConverter(**converter_kwargs), profile\n", "\n", "print(\"✅ Clinical parameter profiles defined\")\n", "print(f\"Available profiles: {list(CLINICAL_PROFILES.keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test all clinical profiles\n", "def test_clinical_profiles():\n", "    \"\"\"Test all clinical profiles on various mask types.\"\"\"\n", "    \n", "    profile_results = {}\n", "    pixel_spacing = (1.25, 1.25)\n", "    \n", "    for profile_name in CLINICAL_PROFILES.keys():\n", "        profile_results[profile_name] = {}\n", "        \n", "        for mask_name, mask in test_masks.items():\n", "            # Create converter with profile settings\n", "            profile = CLINICAL_PROFILES[profile_name]\n", "            converter_kwargs = {\n", "                'pixel_spacing': pixel_spacing,\n", "                'accuracy_threshold': profile['accuracy_threshold'],\n", "                'max_points_per_contour': profile['max_points_per_contour']\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, pixel_spacing)\n", "            \n", "            profile_results[profile_name][mask_name] = {\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                **metrics\n", "            }\n", "    \n", "    return profile_results\n", "\n", "print(\"Testing all clinical profiles...\")\n", "profile_test_results = test_clinical_profiles()\n", "print(\"✅ Clinical profile testing complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize clinical profile comparison\n", "fig, axes = plt.subplots(2, 3, figsize=(20, 14))\n", "axes = axes.flatten()\n", "\n", "profile_names = list(CLINICAL_PROFILES.keys())\n", "colors = plt.cm.Set3(np.linspace(0, 1, len(profile_names)))\n", "\n", "# Aggregate metrics across all mask types\n", "for i, mask_name in enumerate(test_masks.keys()):\n", "    points_data = []\n", "    times_data = []\n", "    sizes_data = []\n", "    profile_labels = []\n", "    \n", "    for j, profile_name in enumerate(profile_names):\n", "        result = profile_test_results[profile_name][mask_name]\n", "        points_data.append(result['total_points'])\n", "        times_data.append(result['processing_time_ms'])\n", "        sizes_data.append(result['estimated_size_kb'])\n", "        profile_labels.append(profile_name.replace('_', '\\n'))\n", "    \n", "    # Bar plots for each mask type\n", "    x_pos = np.arange(len(profile_names))\n", "    \n", "    if i < 3:  # Points comparison\n", "        bars = axes[i].bar(x_pos, points_data, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "        axes[i].set_title(f'{mask_name}\\nTotal Points by Profile', fontweight='bold')\n", "        axes[i].set_ylabel('Total Points')\n", "        axes[i].set_yscale('log')\n", "        \n", "        # Add value labels on bars\n", "        for bar, value in zip(bars, points_data):\n", "            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1, \n", "                        f'{value}', ha='center', va='bottom', fontsize=8, fontweight='bold')\n", "    \n", "    else:  # Time comparison  \n", "        bars = axes[i].bar(x_pos, times_data, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "        axes[i].set_title(f'{mask_name}\\nProcessing Time by Profile', fontweight='bold')\n", "        axes[i].set_ylabel('Processing Time (ms)')\n", "        \n", "        # Add value labels on bars\n", "        for bar, value in zip(bars, times_data):\n", "            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.05, \n", "                        f'{value:.1f}', ha='center', va='bottom', fontsize=8, fontweight='bold')\n", "    \n", "    axes[i].set_xticks(x_pos)\n", "    axes[i].set_xticklabels(profile_labels, rotation=45, ha='right', fontsize=9)\n", "    axes[i].grid(True, alpha=0.3, axis='y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Clinical Profile Summary Table\n", "print(\"\\n\" + \"=\"*120)\n", "print(\"CLINICAL PARAMETER PROFILES SUMMARY\")\n", "print(\"=\"*120)\n", "\n", "for profile_name, profile in CLINICAL_PROFILES.items():\n", "    print(f\"\\n🏥 {profile['name'].upper()}\")\n", "    print(f\"   Description: {profile['description']}\")\n", "    print(f\"   Accuracy: {profile['accuracy_threshold']} mm | Max Points: {profile['max_points_per_contour']} | Priority: {profile['priority']}\")\n", "    print(f\"   Use Cases: {', '.join(profile['use_cases'])}\")\n", "    \n", "    # Show performance across test masks\n", "    print(f\"   Performance:\")\n", "    for mask_name in test_masks.keys():\n", "        result = profile_test_results[profile_name][mask_name]\n", "        print(f\"     {mask_name}: {result['total_points']} points, {result['processing_time_ms']:.1f} ms, {result['estimated_size_kb']:.1f} KB\")\n", "    print(\"-\" * 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Advanced Configuration {#advanced-config}\n", "\n", "Explore advanced configuration options including custom coordinate transformations and preprocessing strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced configuration examples\n", "def demonstrate_advanced_config():\n", "    \"\"\"Demonstrate advanced configuration options.\"\"\"\n", "    \n", "    # Create a more complex test mask for advanced testing\n", "    complex_mask = np.zeros((300, 300), dtype=bool)\n", "    \n", "    # Create multiple components with different characteristics\n", "    # Main structure\n", "    y, x = np.ogrid[:300, :300]\n", "    main_structure = ((x - 150)**2 + (y - 150)**2) <= 80**2\n", "    \n", "    # Internal hole\n", "    hole = ((x - 150)**2 + (y - 150)**2) <= 30**2\n", "    \n", "    # Separate small structure\n", "    small_structure = ((x - 100)**2 + (y - 100)**2) <= 15**2\n", "    \n", "    # Thin connecting bridge\n", "    bridge = (np.abs(x - y) <= 5) & (x >= 100) & (x <= 150) & (y >= 100) & (y <= 150)\n", "    \n", "    complex_mask = (main_structure & ~hole) | small_structure | bridge\n", "    \n", "    return complex_mask\n", "\n", "# Create advanced test mask\n", "advanced_mask = demonstrate_advanced_config()\n", "\n", "# Display the complex mask\n", "plt.figure(figsize=(10, 8))\n", "plt.imshow(advanced_mask, cmap='gray', alpha=0.8)\n", "plt.title('Advanced Test Mask\\n(Multi-component structure with hole and bridge)', fontweight='bold', fontsize=14)\n", "plt.axis('off')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Advanced test mask created: {np.sum(advanced_mask)} pixels\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different coordinate configurations\n", "def analyze_coordinate_configurations():\n", "    \"\"\"Analyze impact of different coordinate system configurations.\"\"\"\n", "    \n", "    coordinate_configs = [\n", "        {\n", "            'name': 'High-res CT',\n", "            'pixel_spacing': (0.625, 0.625),  # 0.625 mm pixels\n", "            'slice_thickness': 1.25,\n", "            'description': 'High-resolution diagnostic CT'\n", "        },\n", "        {\n", "            'name': 'Standard CT', \n", "            'pixel_spacing': (1.25, 1.25),   # 1.25 mm pixels\n", "            'slice_thickness': 2.5,\n", "            'description': 'Standard treatment planning CT'\n", "        },\n", "        {\n", "            'name': 'Low-res CT',\n", "            'pixel_spacing': (2.0, 2.0),     # 2.0 mm pixels  \n", "            'slice_thickness': 5.0,\n", "            'description': 'Low-resolution or older CT protocol'\n", "        },\n", "        {\n", "            'name': 'MR T1',\n", "            'pixel_spacing': (0.9, 0.9),     # 0.9 mm pixels\n", "            'slice_thickness': 3.0,\n", "            'description': 'Typical MR T1-weighted sequence'\n", "        }\n", "    ]\n", "    \n", "    results = []\n", "    \n", "    for config in coordinate_configs:\n", "        # Use conventional_critical profile as baseline\n", "        profile = CLINICAL_PROFILES['conventional_critical']\n", "        \n", "        converter_kwargs = {\n", "            'pixel_spacing': config['pixel_spacing'],\n", "            'slice_thickness': config['slice_thickness'],\n", "            'accuracy_threshold': profile['accuracy_threshold'],\n", "            'max_points_per_contour': profile['max_points_per_contour']\n", "        }\n", "        \n", "        contours, processing_time = time_conversion(advanced_mask, converter_kwargs)\n", "        metrics = analyze_contour_quality(advanced_mask, contours, config['pixel_spacing'])\n", "        \n", "        result = {\n", "            **config,\n", "            'contours': contours,\n", "            'processing_time_ms': processing_time,\n", "            **metrics\n", "        }\n", "        results.append(result)\n", "    \n", "    return results\n", "\n", "print(\"Analyzing coordinate system configurations...\")\n", "coord_results = analyze_coordinate_configurations()\n", "print(\"✅ Coordinate configuration analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize coordinate configuration results\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Extract data for plotting\n", "config_names = [r['name'] for r in coord_results]\n", "pixel_sizes = [r['pixel_spacing'][0] for r in coord_results]  # Assuming square pixels\n", "total_points = [r['total_points'] for r in coord_results]\n", "perimeters = [r['perimeter_mm'] for r in coord_results]\n", "processing_times = [r['processing_time_ms'] for r in coord_results]\n", "file_sizes = [r['estimated_size_kb'] for r in coord_results]\n", "\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "\n", "# Plot 1: Points vs Pixel Size\n", "axes[0,0].scatter(pixel_sizes, total_points, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[0,0].annotate(name, (pixel_sizes[i], total_points[i]), \n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[0,0].set_xlabel('Pixel Size (mm)')\n", "axes[0,0].set_ylabel('Total Points')\n", "axes[0,0].set_title('Total Points vs Pixel Size', fontweight='bold')\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Perimeter vs Pixel Size  \n", "axes[0,1].scatter(pixel_sizes, perimeters, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[0,1].annotate(name, (pixel_sizes[i], perimeters[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[0,1].set_xlabel('Pixel Size (mm)')\n", "axes[0,1].set_ylabel('Perimeter (mm)')\n", "axes[0,1].set_title('Measured Perimeter vs Pixel Size', fontweight='bold')\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Processing Time vs Pixel Size\n", "axes[1,0].scatter(pixel_sizes, processing_times, c=colors, s=150, alpha=0.8, edgecolors='black', linewidths=1)\n", "for i, name in enumerate(config_names):\n", "    axes[1,0].annotate(name, (pixel_sizes[i], processing_times[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')\n", "axes[1,0].set_xlabel('Pixel Size (mm)')\n", "axes[1,0].set_ylabel('Processing Time (ms)')\n", "axes[1,0].set_title('Processing Time vs Pixel Size', fontweight='bold')\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Visual comparison of contours\n", "axes[1,1].imshow(advanced_mask, cmap='gray', alpha=0.6, extent=[0, 300, 300, 0])\n", "\n", "# Show contours from two different resolutions for comparison\n", "high_res_contours = coord_results[0]['contours']  # High-res CT\n", "low_res_contours = coord_results[2]['contours']   # Low-res CT\n", "\n", "# Plot high-res contours in blue\n", "for contour in high_res_contours:\n", "    if len(contour) > 2:\n", "        contour_closed = np.vstack([contour, contour[0]])\n", "        axes[1,1].plot(contour_closed[:, 0], contour_closed[:, 1], 'b-', linewidth=2, alpha=0.8, label='High-res (0.625mm)')\n", "\n", "# Plot low-res contours in red\n", "for contour in low_res_contours:\n", "    if len(contour) > 2:\n", "        contour_closed = np.vstack([contour, contour[0]])\n", "        axes[1,1].plot(contour_closed[:, 0], contour_closed[:, 1], 'r--', linewidth=2, alpha=0.8, label='Low-res (2.0mm)')\n", "\n", "axes[1,1].set_title('Contour Comparison: High-res vs Low-res', fontweight='bold')\n", "axes[1,1].set_xlim(50, 250)\n", "axes[1,1].set_ylim(250, 50)\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Detailed results table\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"COORDINATE CONFIGURATION ANALYSIS\")\n", "print(\"=\"*100)\n", "\n", "print(f\"{'Configuration':<15} {'Pixel(mm)':<12} {'Slice(mm)':<12} {'Points':<8} {'Perimeter':<12} {'Time(ms)':<10} {'Size(KB)':<10}\")\n", "print(\"-\" * 95)\n", "\n", "for result in coord_results:\n", "    print(f\"{result['name']:<15} {result['pixel_spacing'][0]:<12.3f} {result['slice_thickness']:<12.1f} \"\n", "          f\"{result['total_points']:<8} {result['perimeter_mm']:<12.1f} {result['processing_time_ms']:<10.1f} \"\n", "          f\"{result['estimated_size_kb']:<10.1f}\")\n", "\n", "print(\"\\n📋 KEY OBSERVATIONS:\")\n", "print(\"• Higher resolution → More points, better geometric accuracy\")\n", "print(\"• Lower resolution → Faster processing, smaller files, some accuracy loss\")\n", "print(\"• Perimeter measurements affected by pixel size resolution\")\n", "print(\"• Processing time scales roughly with number of detected edge pixels\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error Handling and Recovery Strategies\n", "\n", "Let's demonstrate robust error handling for various edge cases:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demonstrate error handling and recovery\n", "def demonstrate_error_handling():\n", "    \"\"\"Test error handling with various problematic inputs.\"\"\"\n", "    \n", "    test_cases = {\n", "        'empty_mask': np.zeros((100, 100), dtype=bool),\n", "        'single_pixel': np.zeros((100, 100), dtype=bool),\n", "        'thin_line': np.zeros((100, 100), dtype=bool),\n", "        'all_true': np.ones((100, 100), dtype=bool),\n", "        'tiny_mask': np.zeros((5, 5), dtype=bool)\n", "    }\n", "    \n", "    # Set up specific problematic cases\n", "    test_cases['single_pixel'][50, 50] = True\n", "    test_cases['thin_line'][50, 20:80] = True  # 1-pixel wide line\n", "    test_cases['tiny_mask'][2, 2] = True\n", "    \n", "    results = {}\n", "    \n", "    for case_name, mask in test_cases.items():\n", "        print(f\"\\nTesting: {case_name} ({np.sum(mask)} pixels)\")\n", "        \n", "        try:\n", "            # Use conventional_target profile\n", "            profile = CLINICAL_PROFILES['conventional_target']\n", "            converter_kwargs = {\n", "                'pixel_spacing': (1.25, 1.25),\n", "                'accuracy_threshold': profile['accuracy_threshold'],\n", "                'max_points_per_contour': profile['max_points_per_contour']\n", "            }\n", "            \n", "            contours, processing_time = time_conversion(mask, converter_kwargs)\n", "            metrics = analyze_contour_quality(mask, contours, (1.25, 1.25))\n", "            \n", "            results[case_name] = {\n", "                'success': True,\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                'error': None,\n", "                **metrics\n", "            }\n", "            \n", "            print(f\"  ✅ Success: {len(contours)} contours, {metrics['total_points']} points\")\n", "            \n", "        except Exception as e:\n", "            results[case_name] = {\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'contours': [],\n", "                'processing_time_ms': 0,\n", "                'error': str(e),\n", "                'total_points': 0\n", "            }\n", "            \n", "            print(f\"  ❌ Error: {e}\")\n", "    \n", "    return test_cases, results\n", "\n", "# Run error handling tests\n", "print(\"Testing error handling and edge cases...\")\n", "edge_cases, edge_results = demonstrate_error_handling()\n", "print(\"\\n✅ Error handling tests complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize edge cases and their handling\n", "fig, axes = plt.subplots(2, len(edge_cases), figsize=(20, 8))\n", "if len(edge_cases) == 1:\n", "    axes = axes.reshape(-1, 1)\n", "\n", "for i, (case_name, mask) in enumerate(edge_cases.items()):\n", "    result = edge_results[case_name]\n", "    \n", "    # Top row: Input masks\n", "    axes[0, i].imshow(mask, cmap='gray', alpha=0.8)\n", "    axes[0, i].set_title(f'{case_name.replace(\"_\", \" \").title()}\\n{np.sum(mask)} pixels')\n", "    axes[0, i].set_axis_off()\n", "    \n", "    # Bottom row: Results with contours\n", "    axes[1, i].imshow(mask, cmap='gray', alpha=0.6)\n", "    \n", "    if result['success'] and result['contours']:\n", "        colors = plt.cm.viridis(np.linspace(0, 1, len(result['contours'])))\n", "        for j, contour in enumerate(result['contours']):\n", "            if len(contour) > 2:\n", "                contour_closed = np.vstack([contour, contour[0]])\n", "                axes[1, i].plot(contour_closed[:, 0], contour_closed[:, 1], \n", "                               color=colors[j], linewidth=2, alpha=0.9)\n", "                axes[1, i].scatter(contour[:, 0], contour[:, 1], \n", "                                 c=[colors[j]], s=30, alpha=0.8, edgecolors='white')\n", "        \n", "        status = f\"✅ {len(result['contours'])} contours\\n{result['total_points']} points\"\n", "        color = 'green'\n", "    else:\n", "        if result['success']:\n", "            status = \"⚠️ No contours\\nEmpty result\"\n", "            color = 'orange'\n", "        else:\n", "            status = f\"❌ Error\\n{result['error'][:20]}...\"\n", "            color = 'red'\n", "    \n", "    axes[1, i].set_title(status, color=color, fontweight='bold')\n", "    axes[1, i].set_axis_off()\n", "\n", "plt.suptitle('Edge Case Handling: Input Masks and Conversion Results', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Error handling summary\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EDGE CASE HANDLING SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "success_count = sum(1 for r in edge_results.values() if r['success'])\n", "total_count = len(edge_results)\n", "\n", "print(f\"\\n📊 OVERALL RESULTS: {success_count}/{total_count} cases handled successfully ({100*success_count/total_count:.1f}%)\\n\")\n", "\n", "for case_name, result in edge_results.items():\n", "    print(f\"🔸 {case_name.replace('_', ' ').title()}:\")\n", "    if result['success']:\n", "        if result['total_points'] > 0:\n", "            print(f\"   ✅ Successfully processed → {len(result['contours'])} contours, {result['total_points']} points\")\n", "        else:\n", "            print(f\"   ⚠️  Processed but no contours generated (expected for empty/invalid masks)\")\n", "    else:\n", "        print(f\"   ❌ Error: {result['error']}\")\n", "    print()\n", "\n", "print(\"\\n📋 ROBUST HANDLING GUIDELINES:\")\n", "print(\"\"\"\n", "🛡️ PREPROCESSING STRATEGIES:\n", "   • Validate mask dimensions and content before conversion\n", "   • Remove single-pixel artifacts with morphological operations\n", "   • Check for minimum structure size requirements\n", "   • Apply median filtering for noisy masks\n", "\n", "⚡ PERFORMANCE OPTIMIZATION:\n", "   • Skip conversion for empty masks (0 pixels)\n", "   • Use faster settings for very large masks (>100K pixels)\n", "   • Implement timeout for extremely complex structures\n", "   • Cache results for repeated conversions\n", "\n", "🔧 ERROR RECOVERY:\n", "   • Graceful fallback to simpler algorithms for problematic cases\n", "   • Provide meaningful error messages with suggested solutions\n", "   • Log processing statistics for performance monitoring\n", "   • Implement retry logic with relaxed parameters\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Summary & Best Practices {#summary}\n", "\n", "Key takeaways and recommendations for optimal MaskToContourConverter usage in clinical workflows."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive performance comparison chart\n", "def create_performance_matrix():\n", "    \"\"\"Create a comprehensive performance matrix for all tested configurations.\"\"\"\n", "    \n", "    # Compile data from all our analyses\n", "    matrix_data = []\n", "    \n", "    # Clinical profiles on complex shape\n", "    for profile_name, profile in CLINICAL_PROFILES.items():\n", "        result = profile_test_results[profile_name]['Complex Shape']\n", "        matrix_data.append({\n", "            'Configuration': f\"{profile['name']}\",\n", "            'Category': 'Clinical Profile',\n", "            'Accuracy (mm)': profile['accuracy_threshold'],\n", "            'Max Points': profile['max_points_per_contour'],\n", "            'Actual Points': result['total_points'],\n", "            'Time (ms)': result['processing_time_ms'],\n", "            'Size (KB)': result['estimated_size_kb'],\n", "            'Efficiency': result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        })\n", "    \n", "    # Coordinate configurations\n", "    for result in coord_results:\n", "        matrix_data.append({\n", "            'Configuration': result['name'],\n", "            'Category': 'Coordinate System',\n", "            'Accuracy (mm)': 0.5,  # Fixed for this test\n", "            'Max Points': 300,     # Fixed for this test\n", "            'Actual Points': result['total_points'],\n", "            'Time (ms)': result['processing_time_ms'],\n", "            'Size (KB)': result['estimated_size_kb'],\n", "            'Efficiency': result['total_points'] / result['processing_time_ms'] if result['processing_time_ms'] > 0 else 0\n", "        })\n", "    \n", "    return matrix_data\n", "\n", "performance_matrix = create_performance_matrix()\n", "\n", "# Create performance visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(18, 14))\n", "\n", "# Separate clinical profiles and coordinate systems\n", "clinical_data = [d for d in performance_matrix if d['Category'] == 'Clinical Profile']\n", "coord_data = [d for d in performance_matrix if d['Category'] == 'Coordinate System']\n", "\n", "# Plot 1: Clinical Profiles - Points vs Time\n", "clinical_points = [d['Actual Points'] for d in clinical_data]\n", "clinical_times = [d['Time (ms)'] for d in clinical_data]\n", "clinical_names = [d['Configuration'] for d in clinical_data]\n", "\n", "scatter1 = axes[0,0].scatter(clinical_times, clinical_points, s=150, alpha=0.7, \n", "                            c=range(len(clinical_data)), cmap='viridis', edgecolors='black')\n", "for i, name in enumerate(clinical_names):\n", "    axes[0,0].annotate(name.replace(' ', '\\n'), (clinical_times[i], clinical_points[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')\n", "axes[0,0].set_xlabel('Processing Time (ms)')\n", "axes[0,0].set_ylabel('Total Points')\n", "axes[0,0].set_title('Clinical Profiles: Points vs Processing Time', fontweight='bold')\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Clinical Profiles - Accuracy vs File Size\n", "clinical_accuracy = [d['Accuracy (mm)'] for d in clinical_data]\n", "clinical_sizes = [d['Size (KB)'] for d in clinical_data]\n", "\n", "scatter2 = axes[0,1].scatter(clinical_accuracy, clinical_sizes, s=150, alpha=0.7,\n", "                            c=range(len(clinical_data)), cmap='viridis', edgecolors='black')\n", "for i, name in enumerate(clinical_names):\n", "    axes[0,1].annotate(name.replace(' ', '\\n'), (clinical_accuracy[i], clinical_sizes[i]),\n", "                      xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')\n", "axes[0,1].set_xlabel('Accuracy Threshold (mm)')\n", "axes[0,1].set_ylabel('Estimated File Size (KB)')\n", "axes[0,1].set_title('Clinical Profiles: Accuracy vs File Size', fontweight='bold')\n", "axes[0,1].set_xscale('log')\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Coordinate Systems Performance\n", "coord_efficiency = [d['Efficiency'] for d in coord_data]\n", "coord_names = [d['Configuration'] for d in coord_data]\n", "coord_points = [d['Actual Points'] for d in coord_data]\n", "\n", "bars = axes[1,0].bar(range(len(coord_data)), coord_efficiency, \n", "                    color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.8)\n", "axes[1,0].set_xticks(range(len(coord_data)))\n", "axes[1,0].set_xticklabels(coord_names, rotation=45, ha='right')\n", "axes[1,0].set_ylabel('Efficiency (Points/ms)')\n", "axes[1,0].set_title('Coordinate Systems: Processing Efficiency', fontweight='bold')\n", "axes[1,0].grid(True, alpha=0.3, axis='y')\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, coord_efficiency):\n", "    axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.02,\n", "                  f'{value:.1f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 4: Overall Performance Comparison\n", "all_configs = [d['Configuration'] for d in performance_matrix]\n", "all_efficiency = [d['Efficiency'] for d in performance_matrix]\n", "categories = [d['Category'] for d in performance_matrix]\n", "\n", "# Color by category\n", "colors = ['#FF9999' if cat == 'Clinical Profile' else '#9999FF' for cat in categories]\n", "\n", "bars = axes[1,1].barh(range(len(performance_matrix)), all_efficiency, color=colors, alpha=0.8)\n", "axes[1,1].set_yticks(range(len(performance_matrix)))\n", "axes[1,1].set_yticklabels([c.replace(' ', '\\n') for c in all_configs], fontsize=8)\n", "axes[1,1].set_xlabel('Efficiency (Points/ms)')\n", "axes[1,1].set_title('Overall Performance Comparison', fontweight='bold')\n", "axes[1,1].grid(True, alpha=0.3, axis='x')\n", "\n", "# Add legend\n", "from matplotlib.patches import Patch\n", "legend_elements = [Patch(facecolor='#FF9999', alpha=0.8, label='Clinical Profile'),\n", "                  Patch(facecolor='#9999FF', alpha=0.8, label='Coordinate System')]\n", "axes[1,1].legend(handles=legend_elements, loc='lower right')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Comprehensive performance analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final recommendations and best practices summary\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"🎯 MASKTOCONTOURCONVERTER: PARAMETER OPTIMIZATION BEST PRACTICES\")\n", "print(\"=\"*100)\n", "\n", "print(\"\"\"\n", "🏆 OPTIMAL PARAMETER SELECTION STRATEGY:\n", "\n", "1️⃣ IDENTIFY STRUCTURE TYPE:\n", "   • Critical organs requiring sub-mm precision → stereotactic_critical profile\n", "   • Treatment targets needing high accuracy → stereotactic_target profile  \n", "   • Standard clinical structures → conventional_critical or conventional_target\n", "   • Large anatomical regions → large_organ profile\n", "   • Research/batch processing → research_batch profile\n", "\n", "2️⃣ CONSIDER IMAGING PARAMETERS:\n", "   • High-resolution CT (≤1mm pixels) → Can use tighter accuracy thresholds\n", "   • Standard CT (1-2mm pixels) → Balance accuracy with performance  \n", "   • Low-resolution imaging (>2mm pixels) → Focus on efficiency over precision\n", "\n", "3️⃣ BALANCE COMPETING PRIORITIES:\n", "   • Accuracy ↔ File Size: Lower accuracy threshold = more points = larger files\n", "   • Speed ↔ Precision: Higher max points = better accuracy = slower processing\n", "   • Memory ↔ Quality: More points = higher memory usage during processing\n", "\n", "\"\"\")\n", "\n", "print(\"\\n📊 EVIDENCE-BASED RECOMMENDATIONS:\")\n", "print(\"-\" * 50)\n", "\n", "# Calculate best performers in each category\n", "clinical_profiles = [d for d in performance_matrix if d['Category'] == 'Clinical Profile']\n", "\n", "# Find best balance (efficiency vs accuracy)\n", "best_balance = max(clinical_profiles, key=lambda x: x['Efficiency'] / x['Accuracy (mm)'])\n", "fastest = max(clinical_profiles, key=lambda x: x['Efficiency'])\n", "most_accurate = min(clinical_profiles, key=lambda x: x['Accuracy (mm)'])\n", "smallest_files = min(clinical_profiles, key=lambda x: x['Size (KB)'])\n", "\n", "print(f\"\"\"\n", "🏅 TOP PERFORMERS:\n", "\n", "⚖️  BEST OVERALL BALANCE: {best_balance['Configuration']}\n", "   • Accuracy: {best_balance['Accuracy (mm)']} mm\n", "   • Processing: {best_balance['Time (ms)']:.1f} ms\n", "   • File size: {best_balance['Size (KB)']:.1f} KB\n", "   • Efficiency: {best_balance['Efficiency']:.1f} points/ms\n", "\n", "⚡ FASTEST PROCESSING: {fastest['Configuration']}\n", "   • Efficiency: {fastest['Efficiency']:.1f} points/ms\n", "   • Processing: {fastest['Time (ms)']:.1f} ms\n", "   • Trade-off: {fastest['Accuracy (mm)']} mm accuracy\n", "\n", "🎯 HIGHEST ACCURACY: {most_accurate['Configuration']}\n", "   • Accuracy: {most_accurate['Accuracy (mm)']} mm\n", "   • Points: {most_accurate['Actual Points']}\n", "   • Trade-off: {most_accurate['Time (ms)']:.1f} ms processing\n", "\n", "💾 SMALLEST FILES: {smallest_files['Configuration']}\n", "   • File size: {smallest_files['Size (KB)']:.1f} KB\n", "   • Points: {smallest_files['Actual Points']}\n", "   • Trade-off: {smallest_files['Accuracy (mm)']} mm accuracy\n", "\"\"\")\n", "\n", "print(\"\\n🛠️ IMPLEMENTATION CHECKLIST:\")\n", "print(\"-\" * 30)\n", "\n", "print(\"\"\"\n", "✅ PRE-PROCESSING:\n", "   □ Validate mask dimensions and content\n", "   □ Check pixel spacing and slice thickness values\n", "   □ Remove single-pixel artifacts if needed\n", "   □ Verify mask has sufficient resolution for accuracy requirements\n", "\n", "✅ PARAMETER SELECTION:\n", "   □ Choose clinical profile based on structure importance and size\n", "   □ Adjust accuracy threshold based on imaging resolution\n", "   □ Set max points based on file size constraints\n", "   □ Consider processing time requirements for workflow\n", "\n", "✅ QUALITY ASSURANCE:\n", "   □ Verify contour closure for DICOM compliance\n", "   □ Check geometric accuracy against known structures\n", "   □ Validate volume preservation within acceptable limits\n", "   □ Monitor processing performance and file sizes\n", "\n", "✅ ERROR HANDLING:\n", "   □ Implement graceful handling of empty masks\n", "   □ Set up fallback parameters for problematic cases\n", "   □ Log conversion statistics for monitoring\n", "   □ Provide meaningful error messages to users\n", "\"\"\")\n", "\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"📚 SUMMARY: PARAMETER TUNING & OPTIMIZATION MASTERY ACHIEVED\")\n", "print(\"=\"*100)\n", "\n", "print(f\"\"\"\n", "This notebook has provided comprehensive guidance for optimizing MaskToContourConverter \n", "parameters across diverse clinical scenarios. You now have:\n", "\n", "📈 Evidence-based parameter selection strategies\n", "🎛️ Interactive tools for real-time parameter exploration  \n", "🏥 Clinical profiles for standardized workflows\n", "⚙️ Advanced configuration options for specialized needs\n", "🛡️ Robust error handling for production environments\n", "\n", "The combination of accuracy analysis, point optimization, clinical guidelines, and \n", "advanced configuration provides a complete framework for achieving optimal \n", "mask-to-contour conversion performance in any radiotherapy workflow.\n", "\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## Next Steps\n", "\n", "Continue your MaskToContourConverter learning journey:\n", "\n", "- **Complex Geometry & Edge Cases** → `04_complex_geometry_edge_cases.ipynb`\n", "- **Integration & Comparisons** → `05_integration_comparisons.ipynb`\n", "- **Return to Basics** → `01_basic_mask_to_contour_usage.ipynb`\n", "- **Clinical Applications** → `02_clinical_scenarios.ipynb`\n", "\n", "---\n", "\n", "**📧 Questions or Issues?**\n", "- Check the pyrt-dicom documentation\n", "- Review the source code in `src/pyrt_dicom/utils/contour_processing.py`\n", "- Refer to the comprehensive test suite for additional examples\n", "\n", "**🎯 Key Takeaway:**\n", "Parameter optimization is about finding the right balance between accuracy, performance, and file size for your specific clinical workflow. Use the evidence-based profiles as starting points, then fine-tune based on your requirements and constraints."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}