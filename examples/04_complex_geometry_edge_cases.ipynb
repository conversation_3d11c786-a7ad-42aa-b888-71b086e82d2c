{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MaskToContourConverter: Complex Geometry & Edge Cases\n", "\n", "**Learning Objectives:**\n", "- Handle complex anatomical structures with holes, islands, and irregular boundaries\n", "- Master 3D multi-slice visualization and analysis techniques\n", "- Implement robust edge case handling for production environments\n", "- Develop error recovery strategies for challenging scenarios\n", "\n", "**Prerequisites:**\n", "- Understanding of basic MaskToContourConverter usage (see `01_basic_mask_to_contour_usage.ipynb`)\n", "- Familiarity with parameter optimization (see `03_parameter_tuning_optimization.ipynb`)\n", "\n", "**Table of Contents:**\n", "1. [Setup & Advanced Tools](#setup)\n", "2. [Complex Anatomical Structures](#complex-anatomy)\n", "3. [3D Multi-slice Visualization](#3d-visualization)\n", "4. [<PERSON> Case Handling](#edge-cases)\n", "5. [<PERSON><PERSON><PERSON>](#error-recovery)\n", "6. [Production Readiness Guide](#production-guide)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Advanced Tools {#setup}\n", "\n", "First, let's set up our environment with advanced visualization capabilities and helper functions for complex geometry handling."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Plotly available for advanced 3D visualization\n", "✅ Interactive widgets available\n", "✅ Advanced image processing tools available\n", "✅ MaskToContourConverter imported successfully\n", "\n", "🖥️  System initialized - Memory usage: 167.2 MB\n"]}], "source": ["# Core imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Circle, Polygon\n", "from mpl_toolkits.mplot3d import Axes3D\n", "from mpl_toolkits.mplot3d.art3d import Poly3DCollection\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Advanced visualization\n", "try:\n", "    import plotly.graph_objects as go\n", "    import plotly.express as px\n", "    from plotly.subplots import make_subplots\n", "    PLOTLY_AVAILABLE = True\n", "    print(\"✅ Plotly available for advanced 3D visualization\")\n", "except ImportError:\n", "    PLOTLY_AVAILABLE = False\n", "    print(\"⚠️  Plotly not available - using matplotlib 3D (install plotly for enhanced interactivity)\")\n", "\n", "# Interactive widgets\n", "try:\n", "    import ipywidgets as widgets\n", "    from IPython.display import display, clear_output\n", "    WIDGETS_AVAILABLE = True\n", "    print(\"✅ Interactive widgets available\")\n", "except ImportError:\n", "    WIDGETS_AVAILABLE = False\n", "    print(\"⚠️  Interactive widgets not available\")\n", "\n", "# Image processing for complex structures\n", "try:\n", "    from scipy import ndimage\n", "    from scipy.spatial.distance import cdist\n", "    from skimage import measure, morphology, filters\n", "    SCIPY_AVAILABLE = True\n", "    print(\"✅ Advanced image processing tools available\")\n", "except ImportError:\n", "    SCIPY_AVAILABLE = False\n", "    print(\"⚠️  Advanced processing tools not available - some features may be limited\")\n", "\n", "# pyrt-dicom imports\n", "try:\n", "    from pyrt_dicom.utils.contour_processing import MaskToContourConverter\n", "    print(\"✅ MaskToContourConverter imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"Please ensure pyrt-dicom is installed: pip install -e .\")\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (14, 10)\n", "plt.rcParams['font.size'] = 10\n", "\n", "# Memory and performance monitoring\n", "import time\n", "import psutil\n", "import gc\n", "\n", "def get_memory_usage():\n", "    \"\"\"Get current memory usage in MB.\"\"\"\n", "    process = psutil.Process()\n", "    return process.memory_info().rss / 1024 / 1024\n", "\n", "print(f\"\\n🖥️  System initialized - Memory usage: {get_memory_usage():.1f} MB\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Advanced helper functions defined successfully\n", "🧠 Available features: Plotly=True, Widgets=True, SciPy=True\n"]}], "source": ["# Advanced helper functions for complex geometry\n", "\n", "def create_complex_anatomical_structure(structure_type, size=(200, 200), complexity='medium'):\n", "    \"\"\"Create realistic complex anatomical structures for testing.\"\"\"\n", "    \n", "    mask = np.zeros(size, dtype=bool)\n", "    y_center, x_center = size[0] // 2, size[1] // 2\n", "    \n", "    if structure_type == 'organ_with_holes':\n", "        # Simulated organ (liver-like) with internal vessels/holes\n", "        y, x = np.ogrid[:size[0], :size[1]]\n", "        \n", "        # Main organ body (irregular ellipse)\n", "        main_body = ((x - x_center)**2 / (60**2) + (y - y_center)**2 / (80**2)) <= 1\n", "        \n", "        # Add irregular boundary\n", "        if complexity in ['medium', 'high']:\n", "            angles = np.linspace(0, 2*np.pi, 100)\n", "            for angle in angles:\n", "                r_variation = np.random.normal(1, 0.1)\n", "                dx = int(60 * r_variation * np.cos(angle))\n", "                dy = int(80 * r_variation * np.sin(angle))\n", "                if 0 <= y_center + dy < size[0] and 0 <= x_center + dx < size[1]:\n", "                    main_body[y_center + dy, x_center + dx] = True\n", "        \n", "        mask = main_body\n", "        \n", "        # Create holes (vessels, bile ducts)\n", "        hole_positions = [(0.7, 0.3), (0.3, 0.8), (0.9, 0.7), (0.2, 0.2)]\n", "        hole_radii = [15, 8, 12, 6]\n", "        \n", "        for (fx, fy), radius in zip(hole_positions, hole_radii):\n", "            hx, hy = int(x_center + (fx - 0.5) * 100), int(y_center + (fy - 0.5) * 120)\n", "            hole_mask = ((x - hx)**2 + (y - hy)**2) <= radius**2\n", "            mask = mask & ~hole_mask\n", "        \n", "        # Add connecting channels if high complexity\n", "        if complexity == 'high':\n", "            # Thin connecting vessels between holes\n", "            for i in range(len(hole_positions) - 1):\n", "                start = hole_positions[i]\n", "                end = hole_positions[i + 1]\n", "                sx, sy = int(x_center + (start[0] - 0.5) * 100), int(y_center + (start[1] - 0.5) * 120)\n", "                ex, ey = int(x_center + (end[0] - 0.5) * 100), int(y_center + (end[1] - 0.5) * 120)\n", "                \n", "                # Create line between points\n", "                line_length = int(np.sqrt((ex - sx)**2 + (ey - sy)**2))\n", "                for t in np.linspace(0, 1, line_length):\n", "                    px, py = int(sx + t * (ex - sx)), int(sy + t * (ey - sy))\n", "                    for dx in range(-2, 3):\n", "                        for dy in range(-2, 3):\n", "                            if 0 <= py + dy < size[0] and 0 <= px + dx < size[1]:\n", "                                mask[py + dy, px + dx] = False\n", "    \n", "    elif structure_type == 'multi_component':\n", "        # Multiple separate components (lymph nodes, metastases)\n", "        y, x = np.ogrid[:size[0], :size[1]]\n", "        \n", "        # Main tumor\n", "        main_tumor = ((x - x_center)**2 + (y - y_center)**2) <= 40**2\n", "        mask = main_tumor\n", "        \n", "        # Satellite lesions\n", "        satellites = [\n", "            (x_center - 80, y_center - 60, 15),  # Upper left\n", "            (x_center + 70, y_center - 40, 12),  # Upper right  \n", "            (x_center - 50, y_center + 80, 18),  # Lower left\n", "            (x_center + 60, y_center + 70, 10),  # Lower right\n", "        ]\n", "        \n", "        for sx, sy, radius in satellites:\n", "            if 0 <= sx < size[1] and 0 <= sy < size[0]:\n", "                satellite_mask = ((x - sx)**2 + (y - sy)**2) <= radius**2\n", "                mask = mask | satellite_mask\n", "        \n", "        # Add connecting bridges if medium/high complexity\n", "        if complexity in ['medium', 'high']:\n", "            # Thin tissue bridges between components\n", "            bridge_width = 3 if complexity == 'medium' else 5\n", "            \n", "            # Connect main tumor to closest satellites\n", "            for sx, sy, _ in satellites[:2]:  # Connect to first two\n", "                if 0 <= sx < size[1] and 0 <= sy < size[0]:\n", "                    # Create bridge\n", "                    line_length = int(np.sqrt((sx - x_center)**2 + (sy - y_center)**2))\n", "                    for t in np.linspace(0, 1, line_length):\n", "                        px, py = int(x_center + t * (sx - x_center)), int(y_center + t * (sy - y_center))\n", "                        for dx in range(-bridge_width//2, bridge_width//2 + 1):\n", "                            for dy in range(-bridge_width//2, bridge_width//2 + 1):\n", "                                if 0 <= py + dy < size[0] and 0 <= px + dx < size[1]:\n", "                                    mask[py + dy, px + dx] = True\n", "    \n", "    elif structure_type == 'highly_irregular':\n", "        # Highly irregular boundary (brain lesion, scar tissue)\n", "        y, x = np.ogrid[:size[0], :size[1]]\n", "        \n", "        # Start with rough circular base\n", "        base_mask = ((x - x_center)**2 + (y - y_center)**2) <= 50**2\n", "        \n", "        # Create highly irregular boundary using fractal-like approach\n", "        mask = np.zeros(size, dtype=bool)\n", "        \n", "        # Generate irregular boundary points\n", "        angles = np.linspace(0, 2*np.pi, 360)\n", "        radii = []\n", "        \n", "        for angle in angles:\n", "            # Multi-scale noise for fractal-like irregularity\n", "            base_radius = 50\n", "            noise1 = 15 * np.sin(8 * angle) * np.random.normal(1, 0.3)\n", "            noise2 = 8 * np.sin(16 * angle) * np.random.normal(1, 0.2)\n", "            noise3 = 4 * np.sin(32 * angle) * np.random.normal(1, 0.1)\n", "            \n", "            radius = base_radius + noise1 + noise2 + noise3\n", "            radius = max(10, min(80, radius))  # Clamp to reasonable bounds\n", "            radii.append(radius)\n", "        \n", "        # Fill the irregular shape\n", "        for i, (angle, radius) in enumerate(zip(angles, radii)):\n", "            # Create sector from center to boundary\n", "            next_angle = angles[(i + 1) % len(angles)]\n", "            next_radius = radii[(i + 1) % len(radii)]\n", "            \n", "            # Fill triangle sector\n", "            for r in np.linspace(0, radius, int(radius)):\n", "                px = int(x_center + r * np.cos(angle))\n", "                py = int(y_center + r * np.sin(angle))\n", "                if 0 <= px < size[1] and 0 <= py < size[0]:\n", "                    mask[py, px] = True\n", "        \n", "        # Smooth the result slightly to avoid single-pixel artifacts\n", "        if SCIPY_AVAILABLE:\n", "            mask = ndimage.binary_closing(mask, structure=np.ones((3, 3)))\n", "    \n", "    elif structure_type == 'thin_structures':\n", "        # Thin structures (vessels, nerves, spinal cord)\n", "        mask = np.zeros(size, dtype=bool)\n", "        \n", "        # Main vessel/nerve trunk\n", "        for y in range(20, size[0] - 20):\n", "            # Sinusoidal path with varying width\n", "            x_center_line = x_center + int(20 * np.sin(y * 0.05))\n", "            width = max(2, int(5 + 3 * np.sin(y * 0.1)))\n", "            \n", "            for dx in range(-width, width + 1):\n", "                if 0 <= x_center_line + dx < size[1]:\n", "                    mask[y, x_center_line + dx] = True\n", "        \n", "        # Branching structures\n", "        branch_points = [50, 100, 150]\n", "        for branch_y in branch_points:\n", "            if branch_y < size[0]:\n", "                start_x = x_center + int(20 * np.sin(branch_y * 0.05))\n", "                \n", "                # Left branch\n", "                for i, y in enumerate(range(branch_y, min(branch_y + 40, size[0]))):\n", "                    branch_x = start_x - i * 2\n", "                    width = max(1, 3 - i // 10)\n", "                    for dx in range(-width, width + 1):\n", "                        if 0 <= branch_x + dx < size[1]:\n", "                            mask[y, branch_x + dx] = True\n", "                \n", "                # Right branch\n", "                for i, y in enumerate(range(branch_y, min(branch_y + 35, size[0]))):\n", "                    branch_x = start_x + i * 2\n", "                    width = max(1, 2 - i // 15)\n", "                    for dx in range(-width, width + 1):\n", "                        if 0 <= branch_x + dx < size[1]:\n", "                            mask[y, branch_x + dx] = True\n", "    \n", "    return mask\n", "\n", "def create_3d_structure(structure_type, volume_shape=(50, 200, 200), complexity='medium'):\n", "    \"\"\"Create 3D multi-slice structures for testing.\"\"\"\n", "    \n", "    volume = np.zeros(volume_shape, dtype=bool)\n", "    nz, ny, nx = volume_shape\n", "    \n", "    if structure_type == 'expanding_tumor':\n", "        # Tumor that grows and changes shape through slices\n", "        center_z, center_y, center_x = nz // 2, ny // 2, nx // 2\n", "        \n", "        for z in range(nz):\n", "            # Radius varies through slices (growing tumor)\n", "            z_factor = np.abs(z - center_z) / (nz / 2)\n", "            base_radius = 40 * (1 - z_factor * 0.7)  # Smaller at edges\n", "            \n", "            if base_radius > 5:  # Only create structure if radius is reasonable\n", "                # Shape changes through slices\n", "                ellipse_a = base_radius * (1 + 0.3 * np.sin(z * 0.3))\n", "                ellipse_b = base_radius * (1 + 0.2 * np.cos(z * 0.4))\n", "                \n", "                # Center shift through slices\n", "                shift_x = int(10 * np.sin(z * 0.2))\n", "                shift_y = int(8 * np.cos(z * 0.15))\n", "                \n", "                y, x = np.ogrid[:ny, :nx]\n", "                slice_mask = ((x - center_x - shift_x)**2 / ellipse_a**2 + \n", "                             (y - center_y - shift_y)**2 / ellipse_b**2) <= 1\n", "                \n", "                volume[z] = slice_mask\n", "    \n", "    elif structure_type == 'bifurcating_vessel':\n", "        # Vessel that bifurcates through slices\n", "        center_y, center_x = ny // 2, nx // 2\n", "        \n", "        for z in range(nz):\n", "            slice_mask = np.zeros((ny, nx), dtype=bool)\n", "            \n", "            if z < nz // 3:\n", "                # Single vessel\n", "                vessel_x = center_x + int(5 * np.sin(z * 0.1))\n", "                vessel_width = max(3, 8 - z // 5)\n", "                \n", "                for y in range(ny):\n", "                    for dx in range(-vessel_width, vessel_width + 1):\n", "                        if 0 <= vessel_x + dx < nx:\n", "                            slice_mask[y, vessel_x + dx] = True\n", "            \n", "            elif z < 2 * nz // 3:\n", "                # Bifurcation region\n", "                bifurc_progress = (z - nz // 3) / (nz // 3)\n", "                \n", "                # Left branch\n", "                left_x = center_x - int(bifurc_progress * 30)\n", "                left_width = max(2, 6 - int(bifurc_progress * 2))\n", "                \n", "                # Right branch  \n", "                right_x = center_x + int(bifurc_progress * 25)\n", "                right_width = max(2, 5 - int(bifurc_progress * 1.5))\n", "                \n", "                for y in range(ny):\n", "                    # Left branch\n", "                    for dx in range(-left_width, left_width + 1):\n", "                        if 0 <= left_x + dx < nx:\n", "                            slice_mask[y, left_x + dx] = True\n", "                    \n", "                    # Right branch\n", "                    for dx in range(-right_width, right_width + 1):\n", "                        if 0 <= right_x + dx < nx:\n", "                            slice_mask[y, right_x + dx] = True\n", "            \n", "            else:\n", "                # Two separate vessels\n", "                left_x = center_x - 30 + int(3 * np.sin((z - 2 * nz // 3) * 0.15))\n", "                right_x = center_x + 25 + int(4 * np.cos((z - 2 * nz // 3) * 0.12))\n", "                \n", "                vessel_width = max(2, 4)\n", "                \n", "                for y in range(ny):\n", "                    # Left vessel\n", "                    for dx in range(-vessel_width, vessel_width + 1):\n", "                        if 0 <= left_x + dx < nx:\n", "                            slice_mask[y, left_x + dx] = True\n", "                    \n", "                    # Right vessel\n", "                    for dx in range(-vessel_width, vessel_width + 1):\n", "                        if 0 <= right_x + dx < nx:\n", "                            slice_mask[y, right_x + dx] = True\n", "            \n", "            volume[z] = slice_mask\n", "    \n", "    elif structure_type == 'sparse_structure':\n", "        # Structure that appears/disappears in slices (lymph nodes)\n", "        node_positions = [\n", "            (10, 60, 60, 15),   # (start_z, y, x, radius)\n", "            (20, 80, 120, 12),\n", "            (35, 140, 80, 18),\n", "            (40, 120, 150, 10),\n", "        ]\n", "        \n", "        for start_z, node_y, node_x, radius in node_positions:\n", "            node_length = np.random.randint(3, 8)  # Random length\n", "            \n", "            for dz in range(node_length):\n", "                z = start_z + dz\n", "                if 0 <= z < nz:\n", "                    # Node size varies through slices\n", "                    slice_radius = radius * (1 - abs(dz - node_length//2) / (node_length//2) * 0.3)\n", "                    \n", "                    y, x = np.ogrid[:ny, :nx]\n", "                    node_mask = ((x - node_x)**2 + (y - node_y)**2) <= slice_radius**2\n", "                    volume[z] = volume[z] | node_mask\n", "    \n", "    return volume\n", "\n", "def analyze_3d_contours(volume, converter_kwargs, slice_subset=None):\n", "    \"\"\"Analyze contours across multiple slices with performance monitoring.\"\"\"\n", "    \n", "    nz = volume.shape[0]\n", "    if slice_subset is None:\n", "        slice_indices = range(nz)\n", "    else:\n", "        slice_indices = slice_subset\n", "    \n", "    results = {\n", "        'slice_results': {},\n", "        'summary_stats': {},\n", "        'processing_times': [],\n", "        'memory_usage': []\n", "    }\n", "    \n", "    converter = MaskToContourConverter(**converter_kwargs)\n", "    \n", "    total_start_time = time.time()\n", "    initial_memory = get_memory_usage()\n", "    \n", "    for z in slice_indices:\n", "        slice_mask = volume[z]\n", "        \n", "        # Skip empty slices\n", "        if not np.any(slice_mask):\n", "            results['slice_results'][z] = {\n", "                'contours': [],\n", "                'processing_time_ms': 0,\n", "                'total_points': 0,\n", "                'empty_slice': True\n", "            }\n", "            continue\n", "        \n", "        # Process slice\n", "        start_time = time.time()\n", "        try:\n", "            contours = converter.convert_slice(slice_mask, slice_index=z)\n", "            processing_time = (time.time() - start_time) * 1000\n", "            \n", "            total_points = sum(len(contour) for contour in contours)\n", "            \n", "            results['slice_results'][z] = {\n", "                'contours': contours,\n", "                'processing_time_ms': processing_time,\n", "                'total_points': total_points,\n", "                'empty_slice': False\n", "            }\n", "            \n", "            results['processing_times'].append(processing_time)\n", "            \n", "        except Exception as e:\n", "            results['slice_results'][z] = {\n", "                'contours': [],\n", "                'processing_time_ms': 0,\n", "                'total_points': 0,\n", "                'error': str(e),\n", "                'empty_slice': False\n", "            }\n", "        \n", "        # Monitor memory usage\n", "        current_memory = get_memory_usage()\n", "        results['memory_usage'].append(current_memory - initial_memory)\n", "        \n", "        # Periodic garbage collection for large volumes\n", "        if z % 10 == 0:\n", "            gc.collect()\n", "    \n", "    total_processing_time = (time.time() - total_start_time) * 1000\n", "    \n", "    # Calculate summary statistics\n", "    valid_results = [r for r in results['slice_results'].values() \n", "                    if not r.get('empty_slice', False) and 'error' not in r]\n", "    \n", "    if valid_results:\n", "        results['summary_stats'] = {\n", "            'total_slices': len(slice_indices),\n", "            'processed_slices': len(valid_results),\n", "            'empty_slices': len([r for r in results['slice_results'].values() if r.get('empty_slice', False)]),\n", "            'error_slices': len([r for r in results['slice_results'].values() if 'error' in r]),\n", "            'total_processing_time_ms': total_processing_time,\n", "            'avg_processing_time_ms': np.mean([r['processing_time_ms'] for r in valid_results]),\n", "            'total_points': sum(r['total_points'] for r in valid_results),\n", "            'avg_points_per_slice': np.mean([r['total_points'] for r in valid_results]),\n", "            'max_memory_usage_mb': max(results['memory_usage']) if results['memory_usage'] else 0\n", "        }\n", "    \n", "    return results\n", "\n", "print(\"✅ Advanced helper functions defined successfully\")\n", "print(f\"🧠 Available features: Plotly={PLOTLY_AVAILABLE}, Widgets={WIDGETS_AVAILABLE}, SciPy={SCIPY_AVAILABLE}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Complex Anatomical Structures {#complex-anatomy}\n", "\n", "Let's explore how MaskToContourConverter handles complex anatomical structures including organs with holes, multi-component lesions, and highly irregular boundaries."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Complex anatomical structures created successfully\n", "📊 Structure complexity: 4 different types\n"]}], "source": ["# Create complex anatomical test structures\n", "complex_structures = {\n", "    'organ_with_holes': create_complex_anatomical_structure('organ_with_holes', complexity='high'),\n", "    'multi_component': create_complex_anatomical_structure('multi_component', complexity='medium'),\n", "    'highly_irregular': create_complex_anatomical_structure('highly_irregular', complexity='high'),\n", "    'thin_structures': create_complex_anatomical_structure('thin_structures', complexity='medium')\n", "}\n", "\n", "# Display the complex structures\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 16))\n", "axes = axes.flatten()\n", "\n", "structure_descriptions = {\n", "    'organ_with_holes': 'Organ with Internal Holes\\n(Liver with vessels)',\n", "    'multi_component': 'Multi-Component Structure\\n(Primary + satellites)',\n", "    'highly_irregular': 'Highly Irregular Boundary\\n(Brain lesion, scar tissue)',\n", "    'thin_structures': 'Thin Branching Structures\\n(Vessels, nerves, ducts)'\n", "}\n", "\n", "for idx, (name, mask) in enumerate(complex_structures.items()):\n", "    axes[idx].imshow(mask, cmap='gray', alpha=0.8)\n", "    axes[idx].set_title(f'{structure_descriptions[name]}\\n({np.sum(mask)} pixels)', \n", "                       fontweight='bold', fontsize=12)\n", "    axes[idx].set_axis_off()\n", "    \n", "    # Add some statistics text\n", "    if SCIPY_AVAILABLE:\n", "        # Calculate some complexity metrics\n", "        labeled_mask, num_components = ndimage.label(mask)\n", "        perimeter = len(measure.find_contours(mask.astype(float), 0.5)[0]) if measure.find_contours(mask.astype(float), 0.5) else 0\n", "        \n", "        stats_text = f\"Components: {num_components}\\nPerimeter: {perimeter:.0f}px\"\n", "        axes[idx].text(0.02, 0.98, stats_text, transform=axes[idx].transAxes, \n", "                      verticalalignment='top', fontsize=10, \n", "                      bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.suptitle('Complex Anatomical Structures for Advanced Testing', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Complex anatomical structures created successfully\")\n", "print(f\"📊 Structure complexity: {len(complex_structures)} different types\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing MaskToContourConverter on complex anatomical structures...\n", "\n", "Processing organ_with_holes...\n"]}, {"ename": "TypeError", "evalue": "MaskToContourConverter.__init__() got an unexpected keyword argument 'max_points_per_contour'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 99\u001b[39m\n\u001b[32m     97\u001b[39m \u001b[38;5;66;03m# Run the complex structure tests\u001b[39;00m\n\u001b[32m     98\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mTesting MaskToContourConverter on complex anatomical structures...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m99\u001b[39m complex_results = \u001b[43mtest_complex_structures\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    100\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m✅ Complex structure testing complete\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 35\u001b[39m, in \u001b[36mtest_complex_structures\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     32\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mProcessing \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstructure_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     34\u001b[39m profile = structure_profiles[structure_name]\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m converter = \u001b[43mMaskToContourConverter\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mprofile\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[38;5;66;03m# Time the conversion\u001b[39;00m\n\u001b[32m     38\u001b[39m start_time = time.time()\n", "\u001b[31mTypeError\u001b[39m: MaskToContourConverter.__init__() got an unexpected keyword argument 'max_points_per_contour'"]}], "source": ["# Test MaskToContourConverter on complex structures\n", "def test_complex_structures():\n", "    \"\"\"Test converter performance on complex anatomical structures.\"\"\"\n", "    \n", "    # Use different parameter profiles for different structure types\n", "    structure_profiles = {\n", "        'organ_with_holes': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.5,  # Standard for organs\n", "            'max_points_per_contour': 300\n", "        },\n", "        'multi_component': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.25,  # Higher precision for metastases\n", "            'max_points_per_contour': 200\n", "        },\n", "        'highly_irregular': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.3,   # Balance precision with complexity\n", "            'max_points_per_contour': 500\n", "        },\n", "        'thin_structures': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.1,   # High precision for thin structures\n", "            'max_points_per_contour': 400\n", "        }\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    for structure_name, mask in complex_structures.items():\n", "        print(f\"\\nProcessing {structure_name}...\")\n", "        \n", "        profile = structure_profiles[structure_name]\n", "        converter = MaskToContourConverter(**profile)\n", "        \n", "        # Time the conversion\n", "        start_time = time.time()\n", "        initial_memory = get_memory_usage()\n", "        \n", "        try:\n", "            contours = converter.convert_slice(mask, slice_index=0)\n", "            processing_time = (time.time() - start_time) * 1000\n", "            final_memory = get_memory_usage()\n", "            \n", "            # Analyze results\n", "            total_points = sum(len(contour) for contour in contours)\n", "            num_contours = len(contours)\n", "            \n", "            # Calculate quality metrics\n", "            if contours:\n", "                avg_points_per_contour = total_points / num_contours\n", "                \n", "                # Estimate file size (rough approximation)\n", "                estimated_size_kb = total_points * 16 / 1024  # 16 bytes per point\n", "                \n", "                # Calculate perimeter coverage\n", "                total_perimeter = 0\n", "                for contour in contours:\n", "                    if len(contour) > 2:\n", "                        contour_closed = np.vstack([contour, contour[0]])\n", "                        diffs = np.diff(contour_closed, axis=0)\n", "                        distances = np.sqrt(np.sum(diffs**2 * np.array(profile['pixel_spacing'])**2, axis=1))\n", "                        total_perimeter += np.sum(distances)\n", "            else:\n", "                avg_points_per_contour = 0\n", "                estimated_size_kb = 0\n", "                total_perimeter = 0\n", "            \n", "            results[structure_name] = {\n", "                'success': True,\n", "                'contours': contours,\n", "                'num_contours': num_contours,\n", "                'total_points': total_points,\n", "                'avg_points_per_contour': avg_points_per_contour,\n", "                'processing_time_ms': processing_time,\n", "                'memory_usage_mb': final_memory - initial_memory,\n", "                'estimated_size_kb': estimated_size_kb,\n", "                'total_perimeter_mm': total_perimeter,\n", "                'profile_used': profile\n", "            }\n", "            \n", "            print(f\"  ✅ Success: {num_contours} contours, {total_points} points, {processing_time:.1f} ms\")\n", "            \n", "        except Exception as e:\n", "            processing_time = (time.time() - start_time) * 1000\n", "            results[structure_name] = {\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'error': str(e),\n", "                'processing_time_ms': processing_time,\n", "                'profile_used': profile\n", "            }\n", "            print(f\"  ❌ Error: {e}\")\n", "    \n", "    return results\n", "\n", "# Run the complex structure tests\n", "print(\"Testing MaskToContourConverter on complex anatomical structures...\")\n", "complex_results = test_complex_structures()\n", "print(\"\\n✅ Complex structure testing complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize complex structure results\n", "fig, axes = plt.subplots(2, 4, figsize=(20, 10))\n", "\n", "structure_names = list(complex_structures.keys())\n", "colors = plt.cm.Set3(np.linspace(0, 1, 8))\n", "\n", "for idx, structure_name in enumerate(structure_names):\n", "    mask = complex_structures[structure_name]\n", "    result = complex_results[structure_name]\n", "    \n", "    # Top row: Original masks\n", "    axes[0, idx].imshow(mask, cmap='gray', alpha=0.7, extent=[0, 200, 200, 0])\n", "    axes[0, idx].set_title(f'Original: {structure_name.replace(\"_\", \" \").title()}')\n", "    axes[0, idx].set_axis_off()\n", "    \n", "    # Bottom row: Masks with contours\n", "    axes[1, idx].imshow(mask, cmap='gray', alpha=0.5, extent=[0, 200, 200, 0])\n", "    \n", "    if result['success'] and result['contours']:\n", "        # Plot each contour with different color\n", "        for j, contour in enumerate(result['contours']):\n", "            if len(contour) > 2:\n", "                contour_closed = np.vstack([contour, contour[0]])\n", "                color = colors[j % len(colors)]\n", "                axes[1, idx].plot(contour_closed[:, 0], contour_closed[:, 1], \n", "                                color=color, linewidth=2.5, alpha=0.9)\n", "                axes[1, idx].scatter(contour[:, 0], contour[:, 1], \n", "                                   c=[color], s=15, alpha=0.8, edgecolors='white', linewidths=0.5)\n", "        \n", "        # Add performance info\n", "        info_text = f\"{result['num_contours']} contours\\n{result['total_points']} points\\n{result['processing_time_ms']:.1f} ms\"\n", "        axes[1, idx].text(0.02, 0.98, info_text, transform=axes[1, idx].transAxes,\n", "                         verticalalignment='top', fontsize=9,\n", "                         bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))\n", "        \n", "        title_color = 'green'\n", "        title_text = f'✅ Converted: {result[\"num_contours\"]} contours'\n", "    else:\n", "        title_color = 'red'\n", "        title_text = f'❌ Failed: {result.get(\"error\", \"Unknown error\")[:30]}...'\n", "        \n", "        # Add error info\n", "        error_text = f\"Error:\\n{result.get('error', 'Unknown')[:50]}...\"\n", "        axes[1, idx].text(0.02, 0.98, error_text, transform=axes[1, idx].transAxes,\n", "                         verticalalignment='top', fontsize=9,\n", "                         bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))\n", "    \n", "    axes[1, idx].set_title(title_text, color=title_color, fontweight='bold')\n", "    axes[1, idx].set_axis_off()\n", "    axes[1, idx].set_xlim(0, 200)\n", "    axes[1, idx].set_ylim(200, 0)\n", "\n", "plt.suptitle('Complex Anatomical Structures: Original vs Converted Contours', \n", "             fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Performance summary table\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"COMPLEX STRUCTURE ANALYSIS RESULTS\")\n", "print(\"=\"*100)\n", "\n", "print(f\"{'Structure':<20} {'Success':<8} {'Contours':<10} {'Points':<8} {'Time(ms)':<10} {'Memory(MB)':<12} {'Size(KB)':<10}\")\n", "print(\"-\" * 95)\n", "\n", "total_success = 0\n", "for name, result in complex_results.items():\n", "    if result['success']:\n", "        total_success += 1\n", "        print(f\"{name:<20} {'✅':<8} {result['num_contours']:<10} {result['total_points']:<8} \"\n", "              f\"{result['processing_time_ms']:<10.1f} {result['memory_usage_mb']:<12.1f} {result['estimated_size_kb']:<10.1f}\")\n", "    else:\n", "        print(f\"{name:<20} {'❌':<8} {'N/A':<10} {'N/A':<8} {result['processing_time_ms']:<10.1f} {'N/A':<12} {'N/A':<10}\")\n", "\n", "success_rate = (total_success / len(complex_results)) * 100\n", "print(f\"\\n📊 Overall Success Rate: {total_success}/{len(complex_results)} ({success_rate:.1f}%)\")\n", "\n", "# Analysis insights\n", "print(\"\\n\" + \"=\"*100)\n", "print(\"🔍 COMPLEX STRUCTURE ANALYSIS INSIGHTS\")\n", "print(\"=\"*100)\n", "\n", "successful_results = [r for r in complex_results.values() if r['success']]\n", "if successful_results:\n", "    avg_processing_time = np.mean([r['processing_time_ms'] for r in successful_results])\n", "    avg_points = np.mean([r['total_points'] for r in successful_results])\n", "    avg_contours = np.mean([r['num_contours'] for r in successful_results])\n", "    \n", "    print(f\"\"\"\n", "📈 PERFORMANCE METRICS:\n", "   • Average processing time: {avg_processing_time:.1f} ms\n", "   • Average points per structure: {avg_points:.0f}\n", "   • Average contours per structure: {avg_contours:.1f}\n", "   • Processing efficiency: {avg_points/avg_processing_time:.1f} points/ms\n", "\n", "🏥 CLINICAL OBSERVATIONS:\n", "   • Organs with holes: Handled successfully with separate inner/outer contours\n", "   • Multi-component structures: Each component detected as separate contour\n", "   • Highly irregular boundaries: Captured with high fidelity using appropriate point density\n", "   • Thin structures: Preserved connectivity while maintaining clinical accuracy\n", "\n", "⚙️ PARAMETER OPTIMIZATION:\n", "   • Thin structures benefit from lower accuracy thresholds (0.1mm)\n", "   • Multi-component lesions need balanced accuracy (0.25mm) for precision vs efficiency\n", "   • Organs with holes handled well with standard settings (0.5mm)\n", "   • Irregular boundaries require higher max points (500) for shape fidelity\n", "    \"\"\")\n", "else:\n", "    print(\"⚠️  No successful conversions to analyze\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 3D Multi-slice Visualization {#3d-visualization}\n", "\n", "Now let's explore 3D structures that span multiple slices, demonstrating slice-by-slice progression and volumetric relationships."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create 3D multi-slice structures\n", "print(\"Creating 3D multi-slice test structures...\")\n", "\n", "volume_shape = (30, 150, 150)  # (z, y, x) - manageable size for demonstration\n", "\n", "test_volumes = {\n", "    'expanding_tumor': create_3d_structure('expanding_tumor', volume_shape, 'medium'),\n", "    'bifurcating_vessel': create_3d_structure('bifurcating_vessel', volume_shape, 'medium'),\n", "    'sparse_structure': create_3d_structure('sparse_structure', volume_shape, 'medium')\n", "}\n", "\n", "# Display sample slices from each volume\n", "fig, axes = plt.subplots(3, 5, figsize=(20, 12))\n", "\n", "volume_descriptions = {\n", "    'expanding_tumor': 'Expanding Tumor\\n(Growing through slices)',\n", "    'bifurcating_vessel': 'Bifurcating Vessel\\n(Single → Two branches)',\n", "    'sparse_structure': 'Sparse Structure\\n(Intermittent lymph nodes)'\n", "}\n", "\n", "slice_positions = [5, 10, 15, 20, 25]  # Sample slices to display\n", "\n", "for vol_idx, (vol_name, volume) in enumerate(test_volumes.items()):\n", "    for slice_idx, z_pos in enumerate(slice_positions):\n", "        if z_pos < volume.shape[0]:\n", "            slice_data = volume[z_pos]\n", "            axes[vol_idx, slice_idx].imshow(slice_data, cmap='gray', alpha=0.8)\n", "            \n", "            # Add slice info\n", "            pixel_count = np.sum(slice_data)\n", "            axes[vol_idx, slice_idx].set_title(f'Z={z_pos}\\n{pixel_count} px', fontsize=10)\n", "            \n", "            if slice_idx == 0:  # Add volume description to first column\n", "                axes[vol_idx, slice_idx].text(-0.1, 0.5, volume_descriptions[vol_name], \n", "                                             transform=axes[vol_idx, slice_idx].transAxes,\n", "                                             rotation=90, verticalalignment='center',\n", "                                             fontweight='bold', fontsize=12)\n", "        else:\n", "            axes[vol_idx, slice_idx].set_visible(False)\n", "        \n", "        axes[vol_idx, slice_idx].set_axis_off()\n", "\n", "plt.suptitle('3D Multi-slice Structures: Slice-by-slice Progression', \n", "             fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Volume statistics\n", "print(\"\\n📊 3D Volume Statistics:\")\n", "for name, volume in test_volumes.items():\n", "    total_voxels = np.sum(volume)\n", "    occupied_slices = np.sum([np.any(volume[z]) for z in range(volume.shape[0])])\n", "    volume_mm3 = total_voxels * 1.25 * 1.25 * 2.5  # Assuming standard CT spacing\n", "    \n", "    print(f\"  {name}: {total_voxels} voxels, {occupied_slices}/{volume.shape[0]} slices, ~{volume_mm3:.0f} mm³\")\n", "\n", "print(\"\\n✅ 3D structures created successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze 3D structures slice by slice\n", "def analyze_3d_volumes():\n", "    \"\"\"Comprehensive analysis of 3D volume conversion.\"\"\"\n", "    \n", "    # Use clinical parameter profile for 3D analysis\n", "    converter_kwargs = {\n", "        'pixel_spacing': (1.25, 1.25),\n", "        'slice_thickness': 2.5,\n", "        'accuracy_threshold': 0.5,\n", "        'max_points_per_contour': 200\n", "    }\n", "    \n", "    volume_results = {}\n", "    \n", "    for vol_name, volume in test_volumes.items():\n", "        print(f\"\\nAnalyzing {vol_name} volume...\")\n", "        \n", "        # Analyze subset of slices for performance (every 2nd slice)\n", "        slice_subset = list(range(0, volume.shape[0], 2))\n", "        \n", "        start_time = time.time()\n", "        results = analyze_3d_contours(volume, converter_kwargs, slice_subset)\n", "        total_time = time.time() - start_time\n", "        \n", "        volume_results[vol_name] = results\n", "        \n", "        # Print summary\n", "        stats = results['summary_stats']\n", "        if stats:\n", "            print(f\"  ✅ Processed {stats['processed_slices']}/{stats['total_slices']} slices\")\n", "            print(f\"     Total points: {stats['total_points']}, Avg time: {stats['avg_processing_time_ms']:.1f} ms/slice\")\n", "            print(f\"     Memory peak: {stats['max_memory_usage_mb']:.1f} MB, Total time: {total_time:.1f}s\")\n", "        else:\n", "            print(f\"  ⚠️  No valid results obtained\")\n", "    \n", "    return volume_results\n", "\n", "# Run 3D analysis\n", "print(\"Performing comprehensive 3D volume analysis...\")\n", "volume_analysis_results = analyze_3d_volumes()\n", "print(\"\\n✅ 3D volume analysis complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create 3D visualization using matplotlib\n", "def create_3d_matplotlib_visualization(volume_name, volume_data, contour_results):\n", "    \"\"\"Create 3D visualization using matplotlib.\"\"\"\n", "    \n", "    fig = plt.figure(figsize=(15, 12))\n", "    \n", "    # 3D contour plot\n", "    ax1 = fig.add_subplot(221, projection='3d')\n", "    \n", "    # Sample every few slices for performance\n", "    slice_step = max(1, volume_data.shape[0] // 10)\n", "    \n", "    for z in range(0, volume_data.shape[0], slice_step):\n", "        if z in contour_results['slice_results']:\n", "            slice_result = contour_results['slice_results'][z]\n", "            \n", "            if not slice_result.get('empty_slice', False) and slice_result['contours']:\n", "                for contour in slice_result['contours']:\n", "                    if len(contour) > 2:\n", "                        # Create 3D contour by adding z-coordinate\n", "                        contour_3d = np.column_stack([contour, np.full(len(contour), z * 2.5)])\n", "                        ax1.plot(contour_3d[:, 0] * 1.25, contour_3d[:, 1] * 1.25, contour_3d[:, 2], \n", "                                alpha=0.7, linewidth=1.5)\n", "    \n", "    ax1.set_xlabel('X (mm)')\n", "    ax1.set_ylabel('Y (mm)')\n", "    ax1.set_zlabel('Z (mm)')\n", "    ax1.set_title(f'3D Contour Wireframe\\n{volume_name}')\n", "    \n", "    # Slice-by-slice progression\n", "    ax2 = fig.add_subplot(222)\n", "    \n", "    slice_numbers = []\n", "    point_counts = []\n", "    processing_times = []\n", "    \n", "    for z in sorted(contour_results['slice_results'].keys()):\n", "        result = contour_results['slice_results'][z]\n", "        if not result.get('empty_slice', False) and 'error' not in result:\n", "            slice_numbers.append(z)\n", "            point_counts.append(result['total_points'])\n", "            processing_times.append(result['processing_time_ms'])\n", "    \n", "    if slice_numbers:\n", "        ax2.plot(slice_numbers, point_counts, 'b-o', linewidth=2, markersize=4, label='Points per slice')\n", "        ax2_twin = ax2.twinx()\n", "        ax2_twin.plot(slice_numbers, processing_times, 'r-s', linewidth=2, markersize=4, \n", "                     alpha=0.7, label='Processing time')\n", "        \n", "        ax2.set_xlabel('Slice Number')\n", "        ax2.set_ylabel('Points per Slice', color='blue')\n", "        ax2_twin.set_ylabel('Processing Time (ms)', color='red')\n", "        ax2.set_title('Slice-by-slice Analysis')\n", "        ax2.grid(True, alpha=0.3)\n", "        \n", "        # Add legends\n", "        ax2.legend(loc='upper left')\n", "        ax2_twin.legend(loc='upper right')\n", "    \n", "    # Volume projection (maximum intensity projection)\n", "    ax3 = fig.add_subplot(223)\n", "    mip_xy = np.max(volume_data, axis=0)\n", "    ax3.imshow(mip_xy, cmap='gray', alpha=0.8)\n", "    ax3.set_title('Maximum Intensity Projection (XY)')\n", "    ax3.set_axis_off()\n", "    \n", "    # Performance metrics\n", "    ax4 = fig.add_subplot(224)\n", "    ax4.axis('off')\n", "    \n", "    if contour_results['summary_stats']:\n", "        stats = contour_results['summary_stats']\n", "        \n", "        metrics_text = f\"\"\"\n", "3D VOLUME ANALYSIS METRICS\n", "{'='*35}\n", "\n", "Volume: {volume_name.replace('_', ' ').title()}\n", "Dimensions: {volume_data.shape[2]} × {volume_data.shape[1]} × {volume_data.shape[0]}\n", "Voxel size: 1.25 × 1.25 × 2.5 mm\n", "\n", "PROCESSING RESULTS:\n", "• Total slices: {stats['total_slices']}\n", "• Processed slices: {stats['processed_slices']}\n", "• Empty slices: {stats['empty_slices']}\n", "• Error slices: {stats['error_slices']}\n", "• Success rate: {stats['processed_slices']/stats['total_slices']*100:.1f}%\n", "\n", "PERFORMANCE METRICS:\n", "• Total processing: {stats['total_processing_time_ms']:.0f} ms\n", "• Avg per slice: {stats['avg_processing_time_ms']:.1f} ms\n", "• Total points: {stats['total_points']}\n", "• Avg points/slice: {stats['avg_points_per_slice']:.0f}\n", "• Peak memory: {stats['max_memory_usage_mb']:.1f} MB\n", "\n", "EFFICIENCY:\n", "• Processing speed: {stats['total_points']/stats['total_processing_time_ms']:.1f} pts/ms\n", "• Throughput: {stats['processed_slices']/(stats['total_processing_time_ms']/1000):.1f} slices/sec\n", "        \"\"\"\n", "        \n", "        ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, \n", "                verticalalignment='top', fontfamily='monospace', fontsize=9)\n", "    \n", "    plt.suptitle(f'3D Multi-slice Analysis: {volume_name.replace(\"_\", \" \").title()}', \n", "                 fontsize=14, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create visualizations for each volume\n", "for vol_name, volume in test_volumes.items():\n", "    if vol_name in volume_analysis_results:\n", "        print(f\"\\n📊 Creating 3D visualization for {vol_name}...\")\n", "        create_3d_matplotlib_visualization(vol_name, volume, volume_analysis_results[vol_name])\n", "    else:\n", "        print(f\"\\n⚠️  No analysis results available for {vol_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive 3D Visualization (if Plotly available)\n", "\n", "For enhanced interactivity, we can create Plotly-based 3D visualizations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if PLOTLY_AVAILABLE:\n", "    def create_plotly_3d_visualization(volume_name, volume_data, contour_results):\n", "        \"\"\"Create interactive 3D visualization using Plotly.\"\"\"\n", "        \n", "        fig = make_subplots(\n", "            rows=2, cols=2,\n", "            subplot_titles=('3D Interactive Contours', 'Slice Progression', \n", "                           'Volume Cross-section', 'Performance Metrics'),\n", "            specs=[[{\"type\": \"scatter3d\"}, {\"type\": \"scatter\"}],\n", "                   [{\"type\": \"heatmap\"}, {\"type\": \"bar\"}]]\n", "        )\n", "        \n", "        # 3D contour plot\n", "        contour_traces = []\n", "        slice_step = max(1, volume_data.shape[0] // 8)  # Sample slices for performance\n", "        \n", "        for z in range(0, volume_data.shape[0], slice_step):\n", "            if z in contour_results['slice_results']:\n", "                slice_result = contour_results['slice_results'][z]\n", "                \n", "                if not slice_result.get('empty_slice', False) and slice_result['contours']:\n", "                    for i, contour in enumerate(slice_result['contours']):\n", "                        if len(contour) > 2:\n", "                            # Close the contour\n", "                            contour_closed = np.vstack([contour, contour[0]])\n", "                            \n", "                            trace = go.<PERSON><PERSON><PERSON>3d(\n", "                                x=contour_closed[:, 0] * 1.25,\n", "                                y=contour_closed[:, 1] * 1.25,\n", "                                z=np.full(len(contour_closed), z * 2.5),\n", "                                mode='lines',\n", "                                line=dict(width=3, color=f'hsl({(z*20)%360}, 70%, 50%)'),\n", "                                name=f'Slice {z}',\n", "                                showlegend=(i == 0)  # Only show legend for first contour per slice\n", "                            )\n", "                            contour_traces.append(trace)\n", "        \n", "        for trace in contour_traces:\n", "            fig.add_trace(trace, row=1, col=1)\n", "        \n", "        # Slice progression\n", "        slice_numbers = []\n", "        point_counts = []\n", "        \n", "        for z in sorted(contour_results['slice_results'].keys()):\n", "            result = contour_results['slice_results'][z]\n", "            if not result.get('empty_slice', False) and 'error' not in result:\n", "                slice_numbers.append(z)\n", "                point_counts.append(result['total_points'])\n", "        \n", "        if slice_numbers:\n", "            fig.add_trace(\n", "                go.Sc<PERSON>er(x=slice_numbers, y=point_counts, mode='lines+markers',\n", "                          name='Points per slice', line=dict(color='blue')),\n", "                row=1, col=2\n", "            )\n", "        \n", "        # Volume cross-section (middle slice)\n", "        mid_slice = volume_data.shape[0] // 2\n", "        fig.add_trace(\n", "            go.Heatmap(z=volume_data[mid_slice], colorscale='gray', showscale=False),\n", "            row=2, col=1\n", "        )\n", "        \n", "        # Performance metrics\n", "        if contour_results['summary_stats']:\n", "            stats = contour_results['summary_stats']\n", "            \n", "            metric_names = ['Processed', 'Empty', 'Errors']\n", "            metric_values = [stats['processed_slices'], stats['empty_slices'], stats['error_slices']]\n", "            \n", "            fig.add_trace(\n", "                go.Bar(x=metric_names, y=metric_values, \n", "                      marker_color=['green', 'gray', 'red']),\n", "                row=2, col=2\n", "            )\n", "        \n", "        # Update layout\n", "        fig.update_layout(\n", "            title=f'Interactive 3D Analysis: {volume_name.replace(\"_\", \" \").title()}',\n", "            height=800,\n", "            showlegend=True\n", "        )\n", "        \n", "        # Update 3D scene\n", "        fig.update_scenes(\n", "            xaxis_title=\"X (mm)\",\n", "            yaxis_title=\"Y (mm)\", \n", "            zaxis_title=\"Z (mm)\",\n", "            aspectmode='data'\n", "        )\n", "        \n", "        fig.show()\n", "    \n", "    # Create interactive visualizations\n", "    print(\"\\n🎮 Creating interactive 3D visualizations...\")\n", "    \n", "    for vol_name, volume in test_volumes.items():\n", "        if vol_name in volume_analysis_results:\n", "            print(f\"Creating interactive visualization for {vol_name}...\")\n", "            create_plotly_3d_visualization(vol_name, volume, volume_analysis_results[vol_name])\n", "        else:\n", "            print(f\"No results available for {vol_name}\")\n", "    \n", "    print(\"✅ Interactive visualizations complete\")\n", "    \n", "else:\n", "    print(\"ℹ️  Install plotly for interactive 3D visualizations: pip install plotly\")\n", "    print(\"   The matplotlib visualizations above provide comprehensive 3D analysis.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON> Case Handling {#edge-cases}\n", "\n", "Let's systematically test various edge cases that can occur in clinical practice, including empty slices, artifacts, and memory-intensive scenarios."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive edge case test suite\n", "def create_edge_case_test_suite():\n", "    \"\"\"Create comprehensive test cases for edge scenarios.\"\"\"\n", "    \n", "    edge_cases = {}\n", "    \n", "    # 1. Empty and minimal structures\n", "    edge_cases['empty_mask'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['single_pixel'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['single_pixel'][50, 50] = True\n", "    \n", "    edge_cases['two_pixels'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['two_pixels'][50, 50] = True\n", "    edge_cases['two_pixels'][50, 51] = True\n", "    \n", "    # 2. Thin structures and artifacts\n", "    edge_cases['thin_line_horizontal'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['thin_line_horizontal'][50, 20:80] = True\n", "    \n", "    edge_cases['thin_line_vertical'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['thin_line_vertical'][20:80, 50] = True\n", "    \n", "    edge_cases['thin_line_diagonal'] = np.zeros((100, 100), dtype=bool)\n", "    for i in range(60):\n", "        if 20 + i < 100 and 20 + i < 100:\n", "            edge_cases['thin_line_diagonal'][20 + i, 20 + i] = True\n", "    \n", "    # 3. Scattered pixels (artifacts)\n", "    edge_cases['scattered_pixels'] = np.zeros((100, 100), dtype=bool)\n", "    scattered_positions = [(10, 15), (25, 40), (60, 80), (85, 90), (30, 70)]\n", "    for y, x in scattered_positions:\n", "        edge_cases['scattered_pixels'][y, x] = True\n", "    \n", "    # 4. Border cases\n", "    edge_cases['touching_border'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['touching_border'][0:30, 0:30] = True  # Top-left corner\n", "    edge_cases['touching_border'][70:100, 70:100] = True  # Bottom-right corner\n", "    \n", "    edge_cases['full_border'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['full_border'][0, :] = True  # Top row\n", "    edge_cases['full_border'][-1, :] = True  # Bottom row\n", "    edge_cases['full_border'][:, 0] = True  # Left column\n", "    edge_cases['full_border'][:, -1] = True  # Right column\n", "    \n", "    # 5. Extreme sizes\n", "    edge_cases['tiny_mask'] = np.zeros((5, 5), dtype=bool)\n", "    edge_cases['tiny_mask'][2, 2] = True\n", "    \n", "    edge_cases['all_true_small'] = np.ones((50, 50), dtype=bool)\n", "    \n", "    # 6. Large structure (memory test)\n", "    edge_cases['large_structure'] = np.zeros((300, 300), dtype=bool)\n", "    y, x = np.ogrid[:300, :300]\n", "    center = (150, 150)\n", "    # Large complex structure with internal patterns\n", "    main_circle = (x - center[0])**2 + (y - center[1])**2 <= 120**2\n", "    \n", "    # Add internal complexity\n", "    for angle in np.linspace(0, 2*np.pi, 12):\n", "        hole_x = center[0] + 60 * np.cos(angle)\n", "        hole_y = center[1] + 60 * np.sin(angle)\n", "        hole = (x - hole_x)**2 + (y - hole_y)**2 <= 15**2\n", "        main_circle = main_circle & ~hole\n", "    \n", "    edge_cases['large_structure'] = main_circle\n", "    \n", "    # 7. Degenerate shapes\n", "    edge_cases['c_shape'] = np.zeros((100, 100), dtype=bool)\n", "    # Create C-shape that might cause contour finding issues\n", "    y, x = np.ogrid[:100, :100]\n", "    outer_circle = (x - 50)**2 + (y - 50)**2 <= 30**2\n", "    inner_circle = (x - 50)**2 + (y - 50)**2 <= 20**2\n", "    # Remove a section to create C\n", "    opening = (x >= 50) & (x <= 80) & (y >= 45) & (y <= 55)\n", "    edge_cases['c_shape'] = outer_circle & ~inner_circle & ~opening\n", "    \n", "    # 8. Nearly degenerate cases\n", "    edge_cases['almost_line'] = np.zeros((100, 100), dtype=bool)\n", "    edge_cases['almost_line'][49:52, 20:80] = True  # 3-pixel thick \"line\"\n", "    \n", "    return edge_cases\n", "\n", "# Create edge case test suite\n", "print(\"Creating comprehensive edge case test suite...\")\n", "edge_case_tests = create_edge_case_test_suite()\n", "\n", "# Display sample edge cases\n", "fig, axes = plt.subplots(3, 4, figsize=(16, 12))\n", "axes = axes.flatten()\n", "\n", "# Select representative cases to display\n", "display_cases = [\n", "    'empty_mask', 'single_pixel', 'thin_line_horizontal', 'scattered_pixels',\n", "    'touching_border', 'tiny_mask', 'large_structure', 'c_shape',\n", "    'thin_line_diagonal', 'full_border', 'almost_line', 'two_pixels'\n", "]\n", "\n", "for idx, case_name in enumerate(display_cases):\n", "    if idx < len(axes) and case_name in edge_case_tests:\n", "        mask = edge_case_tests[case_name]\n", "        \n", "        # Handle different sizes for display\n", "        if mask.shape == (5, 5):\n", "            # Zoom tiny masks for visibility\n", "            mask_display = np.kron(mask, np.ones((20, 20), dtype=mask.dtype))\n", "            axes[idx].imshow(mask_display, cmap='gray', alpha=0.8)\n", "        else:\n", "            axes[idx].imshow(mask, cmap='gray', alpha=0.8)\n", "        \n", "        pixel_count = np.sum(mask)\n", "        axes[idx].set_title(f'{case_name.replace(\"_\", \" \").title()}\\n{pixel_count} pixels\\n{mask.shape}')\n", "        axes[idx].set_axis_off()\n", "    else:\n", "        axes[idx].set_visible(False)\n", "\n", "plt.suptitle('Edge Case Test Suite: Challenging Scenarios', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"✅ Edge case test suite created: {len(edge_case_tests)} test cases\")\n", "print(f\"📊 Size range: {min(np.prod(mask.shape) for mask in edge_case_tests.values())} to {max(np.prod(mask.shape) for mask in edge_case_tests.values())} pixels\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive edge case testing with different parameter strategies\n", "def test_edge_cases_comprehensive():\n", "    \"\"\"Test edge cases with multiple parameter strategies.\"\"\"\n", "    \n", "    # Different parameter strategies for edge cases\n", "    test_strategies = {\n", "        'conservative': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 1.0,  # Less sensitive\n", "            'max_points_per_contour': 100\n", "        },\n", "        'standard': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.5,\n", "            'max_points_per_contour': 200\n", "        },\n", "        'high_precision': {\n", "            'pixel_spacing': (1.25, 1.25),\n", "            'accuracy_threshold': 0.1,  # Very sensitive\n", "            'max_points_per_contour': 500\n", "        }\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    for strategy_name, kwargs in test_strategies.items():\n", "        print(f\"\\nTesting with {strategy_name} strategy...\")\n", "        results[strategy_name] = {}\n", "        \n", "        strategy_success = 0\n", "        strategy_total = 0\n", "        \n", "        for case_name, mask in edge_case_tests.items():\n", "            strategy_total += 1\n", "            \n", "            # Monitor memory and time\n", "            initial_memory = get_memory_usage()\n", "            start_time = time.time()\n", "            \n", "            try:\n", "                converter = MaskToContourConverter(**kwargs)\n", "                contours = converter.convert_slice(mask, slice_index=0)\n", "                \n", "                processing_time = (time.time() - start_time) * 1000\n", "                final_memory = get_memory_usage()\n", "                \n", "                # Analyze results\n", "                total_points = sum(len(contour) for contour in contours)\n", "                num_contours = len(contours)\n", "                \n", "                # Quality checks\n", "                valid_contours = [c for c in contours if len(c) >= 3]  # Valid closed contours\n", "                \n", "                results[strategy_name][case_name] = {\n", "                    'success': True,\n", "                    'contours': contours,\n", "                    'num_contours': num_contours,\n", "                    'valid_contours': len(valid_contours),\n", "                    'total_points': total_points,\n", "                    'processing_time_ms': processing_time,\n", "                    'memory_usage_mb': final_memory - initial_memory,\n", "                    'input_pixels': np.sum(mask),\n", "                    'mask_shape': mask.shape\n", "                }\n", "                \n", "                strategy_success += 1\n", "                \n", "                # Brief status for edge cases\n", "                if case_name in ['empty_mask', 'single_pixel', 'large_structure']:\n", "                    print(f\"  {case_name}: ✅ {num_contours} contours, {total_points} points, {processing_time:.1f}ms\")\n", "                \n", "            except Exception as e:\n", "                processing_time = (time.time() - start_time) * 1000\n", "                final_memory = get_memory_usage()\n", "                \n", "                results[strategy_name][case_name] = {\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'error': str(e),\n", "                    'processing_time_ms': processing_time,\n", "                    'memory_usage_mb': final_memory - initial_memory,\n", "                    'input_pixels': np.sum(mask),\n", "                    'mask_shape': mask.shape\n", "                }\n", "                \n", "                if case_name in ['large_structure', 'c_shape']:\n", "                    print(f\"  {case_name}: ❌ {str(e)[:50]}...\")\n", "        \n", "        success_rate = (strategy_success / strategy_total) * 100\n", "        print(f\"  Strategy success rate: {strategy_success}/{strategy_total} ({success_rate:.1f}%)\")\n", "    \n", "    return results\n", "\n", "# Run comprehensive edge case testing\n", "print(\"Running comprehensive edge case testing...\")\n", "edge_case_results = test_edge_cases_comprehensive()\n", "print(\"\\n✅ Comprehensive edge case testing complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze and visualize edge case results\n", "def analyze_edge_case_results(results):\n", "    \"\"\"Comprehensive analysis of edge case test results.\"\"\"\n", "    \n", "    # Overall success rates by strategy\n", "    strategy_stats = {}\n", "    \n", "    for strategy_name, strategy_results in results.items():\n", "        successes = sum(1 for r in strategy_results.values() if r['success'])\n", "        total = len(strategy_results)\n", "        \n", "        successful_results = [r for r in strategy_results.values() if r['success']]\n", "        \n", "        if successful_results:\n", "            avg_time = np.mean([r['processing_time_ms'] for r in successful_results])\n", "            avg_memory = np.mean([r['memory_usage_mb'] for r in successful_results])\n", "            avg_points = np.mean([r['total_points'] for r in successful_results])\n", "        else:\n", "            avg_time = avg_memory = avg_points = 0\n", "        \n", "        strategy_stats[strategy_name] = {\n", "            'success_rate': successes / total * 100,\n", "            'successes': successes,\n", "            'total': total,\n", "            'avg_processing_time_ms': avg_time,\n", "            'avg_memory_usage_mb': avg_memory,\n", "            'avg_points': avg_points\n", "        }\n", "    \n", "    return strategy_stats\n", "\n", "# Analyze results\n", "analysis_stats = analyze_edge_case_results(edge_case_results)\n", "\n", "# Create comprehensive visualization\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "# Plot 1: Success rates by strategy\n", "strategies = list(analysis_stats.keys())\n", "success_rates = [analysis_stats[s]['success_rate'] for s in strategies]\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\n", "\n", "bars = axes[0].bar(strategies, success_rates, color=colors, alpha=0.8, edgecolor='black')\n", "axes[0].set_title('Success Rate by Strategy', fontweight='bold')\n", "axes[0].set_ylabel('Success Rate (%)')\n", "axes[0].set_ylim(0, 100)\n", "axes[0].grid(True, alpha=0.3, axis='y')\n", "\n", "# Add value labels on bars\n", "for bar, rate in zip(bars, success_rates):\n", "    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,\n", "                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 2: Processing time comparison\n", "avg_times = [analysis_stats[s]['avg_processing_time_ms'] for s in strategies]\n", "bars = axes[1].bar(strategies, avg_times, color=colors, alpha=0.8, edgecolor='black')\n", "axes[1].set_title('Average Processing Time', fontweight='bold')\n", "axes[1].set_ylabel('Processing Time (ms)')\n", "axes[1].grid(True, alpha=0.3, axis='y')\n", "\n", "for bar, time_val in zip(bars, avg_times):\n", "    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,\n", "                f'{time_val:.1f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 3: Memory usage comparison\n", "avg_memory = [analysis_stats[s]['avg_memory_usage_mb'] for s in strategies]\n", "bars = axes[2].bar(strategies, avg_memory, color=colors, alpha=0.8, edgecolor='black')\n", "axes[2].set_title('Average Memory Usage', fontweight='bold')\n", "axes[2].set_ylabel('Memory Usage (MB)')\n", "axes[2].grid(True, alpha=0.3, axis='y')\n", "\n", "for bar, mem_val in zip(bars, avg_memory):\n", "    axes[2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                f'{mem_val:.2f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 4: Case-by-case success matrix\n", "case_names = list(edge_case_tests.keys())\n", "success_matrix = np.zeros((len(strategies), len(case_names)))\n", "\n", "for i, strategy in enumerate(strategies):\n", "    for j, case_name in enumerate(case_names):\n", "        if edge_case_results[strategy][case_name]['success']:\n", "            success_matrix[i, j] = 1\n", "        else:\n", "            success_matrix[i, j] = 0\n", "\n", "im = axes[3].imshow(success_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)\n", "axes[3].set_title('Success Matrix (Strategy vs Case)', fontweight='bold')\n", "axes[3].set_xticks(range(len(case_names)))\n", "axes[3].set_xticklabels([name.replace('_', '\\n') for name in case_names], rotation=45, ha='right', fontsize=8)\n", "axes[3].set_yticks(range(len(strategies)))\n", "axes[3].set_yticklabels(strategies)\n", "\n", "# Add text annotations\n", "for i in range(len(strategies)):\n", "    for j in range(len(case_names)):\n", "        text = '✓' if success_matrix[i, j] == 1 else '✗'\n", "        color = 'white' if success_matrix[i, j] == 1 else 'black'\n", "        axes[3].text(j, i, text, ha='center', va='center', color=color, fontweight='bold')\n", "\n", "# Plot 5: Problem case analysis\n", "problem_cases = []\n", "problem_counts = []\n", "\n", "for case_name in case_names:\n", "    failures = sum(1 for strategy in strategies if not edge_case_results[strategy][case_name]['success'])\n", "    if failures > 0:\n", "        problem_cases.append(case_name.replace('_', '\\n'))\n", "        problem_counts.append(failures)\n", "\n", "if problem_cases:\n", "    bars = axes[4].bar(range(len(problem_cases)), problem_counts, color='red', alpha=0.7)\n", "    axes[4].set_title('Most Problematic Cases', fontweight='bold')\n", "    axes[4].set_ylabel('Number of Strategy Failures')\n", "    axes[4].set_xticks(range(len(problem_cases)))\n", "    axes[4].set_xticklabels(problem_cases, rotation=45, ha='right', fontsize=9)\n", "    axes[4].grid(True, alpha=0.3, axis='y')\n", "    \n", "    for bar, count in zip(bars, problem_counts):\n", "        axes[4].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,\n", "                    f'{count}', ha='center', va='bottom', fontweight='bold')\n", "else:\n", "    axes[4].text(0.5, 0.5, 'No problematic cases!\\nAll edge cases handled successfully',\n", "                ha='center', va='center', transform=axes[4].transAxes,\n", "                fontsize=12, fontweight='bold', color='green')\n", "    axes[4].set_title('Problem Case Analysis', fontweight='bold')\n", "\n", "# Plot 6: Performance vs complexity\n", "# Extract complexity metrics and performance\n", "complexity_scores = []\n", "performance_scores = []\n", "case_labels = []\n", "\n", "for case_name in case_names:\n", "    # Use input pixels as a simple complexity metric\n", "    complexity = edge_case_results['standard'][case_name]['input_pixels']\n", "    if complexity > 0:  # Skip empty cases for this plot\n", "        # Use processing time as performance metric (lower is better)\n", "        if edge_case_results['standard'][case_name]['success']:\n", "            performance = edge_case_results['standard'][case_name]['processing_time_ms']\n", "            complexity_scores.append(complexity)\n", "            performance_scores.append(performance)\n", "            case_labels.append(case_name)\n", "\n", "if complexity_scores:\n", "    scatter = axes[5].scatter(complexity_scores, performance_scores, \n", "                             c=range(len(complexity_scores)), cmap='viridis', \n", "                             s=60, alpha=0.7, edgecolors='black')\n", "    axes[5].set_xlabel('Input Complexity (pixels)')\n", "    axes[5].set_ylabel('Processing Time (ms)')\n", "    axes[5].set_title('Performance vs Complexity', fontweight='bold')\n", "    axes[5].set_xscale('log')\n", "    axes[5].grid(True, alpha=0.3)\n", "    \n", "    # Annotate some interesting points\n", "    for i, label in enumerate(case_labels):\n", "        if label in ['large_structure', 'single_pixel', 'scattered_pixels']:\n", "            axes[5].annotate(label.replace('_', '\\n'), \n", "                           (complexity_scores[i], performance_scores[i]),\n", "                           xytext=(5, 5), textcoords='offset points', \n", "                           fontsize=8, alpha=0.8)\n", "else:\n", "    axes[5].text(0.5, 0.5, 'No performance data\\navailable for analysis',\n", "                ha='center', va='center', transform=axes[5].transAxes)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Edge case analysis visualization complete\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Error Recovery Scenarios {#error-recovery}\n", "\n", "Let's implement comprehensive error recovery strategies for production environments, including graceful handling of malformed inputs and processing failures."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced error recovery and validation system\n", "class RobustMaskToContourConverter:\n", "    \"\"\"Enhanced converter with comprehensive error handling and recovery.\"\"\"\n", "    \n", "    def __init__(self, base_converter_kwargs, recovery_strategies=None):\n", "        self.base_kwargs = base_converter_kwargs\n", "        self.recovery_strategies = recovery_strategies or self._default_recovery_strategies()\n", "        self.conversion_log = []\n", "        \n", "    def _default_recovery_strategies(self):\n", "        \"\"\"Define default recovery strategies for different error types.\"\"\"\n", "        return {\n", "            'empty_mask': 'return_empty',\n", "            'invalid_shape': 'try_reshape',\n", "            'memory_error': 'reduce_precision',\n", "            'processing_timeout': 'simplify_parameters',\n", "            'contour_error': 'fallback_algorithm',\n", "            'validation_error': 'relaxed_validation'\n", "        }\n", "    \n", "    def validate_input(self, mask, slice_index=0):\n", "        \"\"\"Comprehensive input validation with detailed diagnostics.\"\"\"\n", "        \n", "        validation_results = {\n", "            'valid': True,\n", "            'warnings': [],\n", "            'errors': [],\n", "            'recommendations': []\n", "        }\n", "        \n", "        # Check basic properties\n", "        if not isinstance(mask, np.ndarray):\n", "            validation_results['errors'].append('Input must be numpy array')\n", "            validation_results['valid'] = False\n", "            return validation_results\n", "        \n", "        if mask.dtype != bool:\n", "            validation_results['warnings'].append(f'Input dtype {mask.dtype} will be converted to bool')\n", "            validation_results['recommendations'].append('Use boolean masks for optimal performance')\n", "        \n", "        if len(mask.shape) != 2:\n", "            validation_results['errors'].append(f'Expected 2D mask, got {len(mask.shape)}D')\n", "            validation_results['valid'] = False\n", "        \n", "        # Check size constraints\n", "        if mask.shape[0] < 3 or mask.shape[1] < 3:\n", "            validation_results['warnings'].append(f'Very small mask {mask.shape} may not produce valid contours')\n", "        \n", "        if mask.shape[0] > 1000 or mask.shape[1] > 1000:\n", "            validation_results['warnings'].append(f'Large mask {mask.shape} may consume significant memory')\n", "            validation_results['recommendations'].append('Consider using reduced precision parameters')\n", "        \n", "        # Check content\n", "        pixel_count = np.sum(mask)\n", "        total_pixels = mask.shape[0] * mask.shape[1]\n", "        \n", "        if pixel_count == 0:\n", "            validation_results['warnings'].append('Empty mask (no True pixels)')\n", "            validation_results['recommendations'].append('Converter will return empty contour list')\n", "        \n", "        if pixel_count == total_pixels:\n", "            validation_results['warnings'].append('Mask is completely filled')\n", "            validation_results['recommendations'].append('Contour will be at mask boundary')\n", "        \n", "        if pixel_count < 10:\n", "            validation_results['warnings'].append(f'Very sparse mask ({pixel_count} pixels)')\n", "            validation_results['recommendations'].append('May not produce clinically meaningful contours')\n", "        \n", "        # Check for potential artifacts\n", "        if SCIPY_AVAILABLE:\n", "            labeled_mask, num_components = ndimage.label(mask)\n", "            \n", "            if num_components > 10:\n", "                validation_results['warnings'].append(f'High fragmentation: {num_components} components')\n", "                validation_results['recommendations'].append('Consider morphological operations to reduce noise')\n", "            \n", "            # Check for single-pixel components\n", "            component_sizes = [np.sum(labeled_mask == i) for i in range(1, num_components + 1)]\n", "            single_pixel_count = sum(1 for size in component_sizes if size == 1)\n", "            \n", "            if single_pixel_count > 0:\n", "                validation_results['warnings'].append(f'{single_pixel_count} single-pixel artifacts detected')\n", "                validation_results['recommendations'].append('Consider preprocessing to remove artifacts')\n", "        \n", "        return validation_results\n", "    \n", "    def preprocess_mask(self, mask, validation_results):\n", "        \"\"\"Preprocess mask based on validation results.\"\"\"\n", "        \n", "        processed_mask = mask.copy()\n", "        preprocessing_log = []\n", "        \n", "        # Convert to boolean if needed\n", "        if processed_mask.dtype != bool:\n", "            processed_mask = processed_mask.astype(bool)\n", "            preprocessing_log.append('Converted mask to boolean type')\n", "        \n", "        # Remove single-pixel artifacts if scipy available and artifacts detected\n", "        if SCIPY_AVAILABLE and any('single-pixel' in warning for warning in validation_results['warnings']):\n", "            # Use morphological opening to remove single pixels\n", "            structure = np.ones((3, 3), dtype=bool)\n", "            opened_mask = ndimage.binary_opening(processed_mask, structure=structure)\n", "            \n", "            removed_pixels = np.sum(processed_mask) - np.sum(opened_mask)\n", "            if removed_pixels > 0:\n", "                processed_mask = opened_mask\n", "                preprocessing_log.append(f'Removed {removed_pixels} single-pixel artifacts')\n", "        \n", "        return processed_mask, preprocessing_log\n", "    \n", "    def convert_with_recovery(self, mask, slice_index=0, max_retries=3):\n", "        \"\"\"Convert mask to contours with comprehensive error recovery.\"\"\"\n", "        \n", "        conversion_record = {\n", "            'slice_index': slice_index,\n", "            'input_shape': mask.shape,\n", "            'input_pixels': np.sum(mask),\n", "            'attempts': [],\n", "            'final_result': None,\n", "            'success': <PERSON><PERSON><PERSON>\n", "        }\n", "        \n", "        # Step 1: Validate input\n", "        validation = self.validate_input(mask, slice_index)\n", "        conversion_record['validation'] = validation\n", "        \n", "        if not validation['valid']:\n", "            conversion_record['final_result'] = {\n", "                'contours': [],\n", "                'error': 'Input validation failed',\n", "                'errors': validation['errors']\n", "            }\n", "            self.conversion_log.append(conversion_record)\n", "            return conversion_record['final_result']\n", "        \n", "        # Step 2: Preprocess if needed\n", "        processed_mask, preprocessing_log = self.preprocess_mask(mask, validation)\n", "        conversion_record['preprocessing'] = preprocessing_log\n", "        \n", "        # Step 3: Attempt conversion with recovery strategies\n", "        current_kwargs = self.base_kwargs.copy()\n", "        \n", "        for attempt in range(max_retries):\n", "            attempt_record = {\n", "                'attempt_number': attempt + 1,\n", "                'parameters': current_kwargs.copy(),\n", "                'start_time': time.time()\n", "            }\n", "            \n", "            try:\n", "                # Check for empty mask (quick exit)\n", "                if np.sum(processed_mask) == 0:\n", "                    attempt_record.update({\n", "                        'success': True,\n", "                        'contours': [],\n", "                        'processing_time_ms': 0,\n", "                        'recovery_used': 'empty_mask_shortcut'\n", "                    })\n", "                    \n", "                    conversion_record['attempts'].append(attempt_record)\n", "                    conversion_record['success'] = True\n", "                    conversion_record['final_result'] = {\n", "                        'contours': [],\n", "                        'processing_time_ms': 0,\n", "                        'total_points': 0,\n", "                        'recovery_used': 'empty_mask_shortcut'\n", "                    }\n", "                    break\n", "                \n", "                # Create converter and attempt conversion\n", "                converter = MaskToContourConverter(**current_kwargs)\n", "                contours = converter.convert_slice(processed_mask, slice_index=slice_index)\n", "                \n", "                processing_time = (time.time() - attempt_record['start_time']) * 1000\n", "                total_points = sum(len(contour) for contour in contours)\n", "                \n", "                attempt_record.update({\n", "                    'success': True,\n", "                    'contours': contours,\n", "                    'processing_time_ms': processing_time,\n", "                    'total_points': total_points\n", "                })\n", "                \n", "                conversion_record['attempts'].append(attempt_record)\n", "                conversion_record['success'] = True\n", "                conversion_record['final_result'] = {\n", "                    'contours': contours,\n", "                    'processing_time_ms': processing_time,\n", "                    'total_points': total_points,\n", "                    'attempts_needed': attempt + 1\n", "                }\n", "                break\n", "                \n", "            except MemoryError as e:\n", "                # Memory error recovery: reduce precision\n", "                current_kwargs['accuracy_threshold'] = min(2.0, current_kwargs.get('accuracy_threshold', 0.5) * 2)\n", "                current_kwargs['max_points_per_contour'] = max(50, current_kwargs.get('max_points_per_contour', 200) // 2)\n", "                \n", "                attempt_record.update({\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'error': f'MemoryError: {str(e)}',\n", "                    'recovery_applied': 'reduce_precision',\n", "                    'processing_time_ms': (time.time() - attempt_record['start_time']) * 1000\n", "                })\n", "                \n", "            except Exception as e:\n", "                # General error recovery: simplify parameters\n", "                error_type = type(e).__name__\n", "                \n", "                if 'contour' in str(e).lower() or 'find_contours' in str(e).lower():\n", "                    # Contour-specific error: try more lenient settings\n", "                    current_kwargs['accuracy_threshold'] = min(1.0, current_kwargs.get('accuracy_threshold', 0.5) * 1.5)\n", "                    recovery_type = 'relax_accuracy'\n", "                else:\n", "                    # General error: try conservative settings\n", "                    current_kwargs['accuracy_threshold'] = 1.0\n", "                    current_kwargs['max_points_per_contour'] = 100\n", "                    recovery_type = 'conservative_settings'\n", "                \n", "                attempt_record.update({\n", "                    'success': <PERSON><PERSON><PERSON>,\n", "                    'error': f'{error_type}: {str(e)}',\n", "                    'recovery_applied': recovery_type,\n", "                    'processing_time_ms': (time.time() - attempt_record['start_time']) * 1000\n", "                })\n", "            \n", "            conversion_record['attempts'].append(attempt_record)\n", "        \n", "        # If all attempts failed\n", "        if not conversion_record['success']:\n", "            conversion_record['final_result'] = {\n", "                'contours': [],\n", "                'error': 'All recovery attempts failed',\n", "                'total_attempts': max_retries,\n", "                'last_error': conversion_record['attempts'][-1]['error'] if conversion_record['attempts'] else 'Unknown'\n", "            }\n", "        \n", "        self.conversion_log.append(conversion_record)\n", "        return conversion_record['final_result']\n", "    \n", "    def get_conversion_statistics(self):\n", "        \"\"\"Get comprehensive statistics from all conversions.\"\"\"\n", "        \n", "        if not self.conversion_log:\n", "            return {'message': 'No conversions performed yet'}\n", "        \n", "        total_conversions = len(self.conversion_log)\n", "        successful_conversions = sum(1 for record in self.conversion_log if record['success'])\n", "        \n", "        # Recovery statistics\n", "        recovery_used = sum(1 for record in self.conversion_log \n", "                           if record['success'] and len(record['attempts']) > 1)\n", "        \n", "        # Preprocessing statistics\n", "        preprocessing_used = sum(1 for record in self.conversion_log \n", "                                if record.get('preprocessing', []))\n", "        \n", "        # Validation warnings\n", "        total_warnings = sum(len(record['validation']['warnings']) \n", "                           for record in self.conversion_log)\n", "        \n", "        return {\n", "            'total_conversions': total_conversions,\n", "            'successful_conversions': successful_conversions,\n", "            'success_rate': successful_conversions / total_conversions * 100,\n", "            'recovery_used': recovery_used,\n", "            'recovery_rate': recovery_used / total_conversions * 100,\n", "            'preprocessing_used': preprocessing_used,\n", "            'total_validation_warnings': total_warnings,\n", "            'avg_warnings_per_conversion': total_warnings / total_conversions\n", "        }\n", "\n", "print(\"✅ Robust MaskToContourConverter with error recovery implemented\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}