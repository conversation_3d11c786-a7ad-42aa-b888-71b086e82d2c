# Copyright (C) 2024 Pirate DICOM Contributors

"""
Tests for clinical validation framework.

Comprehensive test suite for clinical validation of RT DICOM objects,
including structure volume validation, naming conventions, dose parameter
safety checks, and geometric consistency validation.
"""

import pytest
import numpy as np
from typing import List, Dict, Any

from pyrt_dicom.validation.clinical import (
    ClinicalValidator,
    ValidationResult,
    ValidationLevel,
    validate_clinical_parameters,
    format_validation_report,
)
from pyrt_dicom.utils.exceptions import ValidationError


class TestClinicalValidator:
    """Test suite for ClinicalValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ClinicalValidator()
        self.strict_validator = ClinicalValidator(strict_mode=True)
        self.lenient_validator = ClinicalValidator(strict_mode=False)
    
    def test_validator_initialization(self):
        """Test validator initialization with different configurations."""
        # Default initialization
        validator = ClinicalValidator()
        assert validator.strict_mode is True
        assert validator.tps_compatibility is True
        
        # Custom initialization
        custom_limits = {
            'dose_limits': {'max_clinical_dose': 25.0},
            'volume_limits': {'PTV': {'min': 1.0, 'max': 2000.0}}
        }
        validator = ClinicalValidator(
            strict_mode=False,
            custom_limits=custom_limits,
            tps_compatibility=False
        )
        assert validator.strict_mode is False
        assert validator.tps_compatibility is False
        assert validator.dose_limits['max_clinical_dose'] == 25.0
    
    def test_structure_volume_validation_ptv(self):
        """Test PTV volume validation."""
        # Valid PTV volume
        results = self.validator.validate_structure_volume(
            volume_cc=150.0,
            structure_name="PTV_7000",
            structure_type="PTV"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # PTV volume too small
        results = self.validator.validate_structure_volume(
            volume_cc=0.1,
            structure_name="PTV_Small",
            structure_type="PTV"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        
        # PTV volume too large
        results = self.validator.validate_structure_volume(
            volume_cc=5000.0,
            structure_name="PTV_Large",
            structure_type="PTV"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
    
    def test_structure_volume_validation_organs(self):
        """Test organ volume validation."""
        # Valid heart volume
        results = self.validator.validate_structure_volume(
            volume_cc=600.0,
            structure_name="Heart",
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # Unusually large heart volume (warning expected)
        results = self.validator.validate_structure_volume(
            volume_cc=2000.0,
            structure_name="Heart",
            structure_type="ORGAN"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
        
        # Valid lung volume
        results = self.validator.validate_structure_volume(
            volume_cc=3000.0,
            structure_name="Lung_L",
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
    
    def test_structure_volume_negative(self):
        """Test negative volume validation."""
        results = self.validator.validate_structure_volume(
            volume_cc=-10.0,
            structure_name="Invalid",
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        assert any("negative" in r.message.lower() for r in error_results)
    
    def test_structure_naming_validation(self):
        """Test structure naming validation."""
        # Valid TG-263 name
        results = self.validator.validate_structure_naming(
            structure_name="Heart",
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # Empty name
        results = self.validator.validate_structure_naming(
            structure_name="",
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        
        # Name too long
        long_name = "A" * 70  # Exceeds DICOM limit
        results = self.validator.validate_structure_naming(
            structure_name=long_name,
            structure_type="ORGAN"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
    
    def test_structure_naming_special_characters(self):
        """Test structure naming with special characters."""
        results = self.validator.validate_structure_naming(
            structure_name="Heart@#$",
            structure_type="ORGAN"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
        assert any("special characters" in r.message.lower() for r in warning_results)
    
    def test_structure_naming_tg263_compliance(self):
        """Test TG-263 naming compliance."""
        # Standard TG-263 name
        results = self.validator.validate_structure_naming(
            structure_name="Brainstem",
            structure_type="ORGAN"
        )
        # Should not have warnings about non-standard naming
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING and "standard" in r.message.lower()]
        assert len(warning_results) == 0
        
        # Non-standard name
        results = self.validator.validate_structure_naming(
            structure_name="MyCustomStructure",
            structure_type="ORGAN"
        )
        # Should have info/warning about non-standard naming
        info_warning_results = [r for r in results if r.level in [ValidationLevel.INFO, ValidationLevel.WARNING]]
        assert len(info_warning_results) > 0
    
    def test_dose_parameters_validation(self):
        """Test dose parameter validation."""
        # Valid dose array
        dose_array = np.ones((10, 10, 10)) * 2.0  # 2 Gy dose
        results = self.validator.validate_dose_parameters(
            dose_array=dose_array,
            dose_units="Gy",
            prescription_dose=70.0,
            fractions=35
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # Dose too high
        high_dose_array = np.ones((10, 10, 10)) * 150.0  # 150 Gy dose (exceeds 100 Gy limit)
        results = self.validator.validate_dose_parameters(
            dose_array=high_dose_array,
            dose_units="Gy"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        
        # Negative dose
        negative_dose_array = np.ones((10, 10, 10)) * -1.0
        results = self.validator.validate_dose_parameters(
            dose_array=negative_dose_array,
            dose_units="Gy"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
    
    def test_dose_fractionation_validation(self):
        """Test dose fractionation validation."""
        # Valid fractionation
        results = self.validator.validate_dose_parameters(
            prescription_dose=70.0,
            fractions=35,
            dose_units="Gy"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # Too many fractions
        results = self.validator.validate_dose_parameters(
            prescription_dose=70.0,
            fractions=60,
            dose_units="Gy"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
        
        # Dose per fraction too high (exceeds 8 Gy limit)
        results = self.validator.validate_dose_parameters(
            prescription_dose=50.0,
            fractions=1,
            dose_units="Gy"
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
    
    def test_dose_units_validation(self):
        """Test dose units validation."""
        # Standard units
        for units in ["Gy", "cGy", "RELATIVE"]:
            results = self.validator.validate_dose_parameters(
                prescription_dose=70.0,
                dose_units=units
            )
            # Should not have warnings about units
            unit_warnings = [r for r in results if "units" in r.message.lower()]
            assert len(unit_warnings) == 0
        
        # Non-standard units
        results = self.validator.validate_dose_parameters(
            prescription_dose=7000.0,
            dose_units="mGy"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
    
    def test_geometric_consistency_validation(self):
        """Test geometric consistency validation."""
        # Consistent objects
        objects = [
            {
                'name': 'CT_Series',
                'type': 'CT',
                'frame_of_reference_uid': '*******.5',
                'pixel_spacing': [1.0, 1.0]
            },
            {
                'name': 'Structure_Set',
                'type': 'RTSTRUCT',
                'frame_of_reference_uid': '*******.5',
                'pixel_spacing': [1.0, 1.0]
            }
        ]
        
        results = self.validator.validate_geometric_consistency(objects)
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
        
        # Inconsistent Frame of Reference UIDs
        inconsistent_objects = [
            {
                'name': 'CT_Series',
                'type': 'CT',
                'frame_of_reference_uid': '*******.5'
            },
            {
                'name': 'Structure_Set',
                'type': 'RTSTRUCT',
                'frame_of_reference_uid': '*******.6'  # Different UID
            }
        ]
        
        results = self.validator.validate_geometric_consistency(inconsistent_objects)
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        assert any("frame of reference" in r.message.lower() for r in error_results)
    
    def test_strict_vs_lenient_mode(self):
        """Test difference between strict and lenient validation modes."""
        # Test with marginally small structure volume
        small_volume = 0.4  # Below PTV minimum but close
        
        strict_results = self.strict_validator.validate_structure_volume(
            volume_cc=small_volume,
            structure_name="PTV_Small",
            structure_type="PTV"
        )
        
        lenient_results = self.lenient_validator.validate_structure_volume(
            volume_cc=small_volume,
            structure_name="PTV_Small",
            structure_type="PTV"
        )
        
        # Strict mode should have more errors
        strict_errors = [r for r in strict_results if r.level == ValidationLevel.ERROR]
        lenient_errors = [r for r in lenient_results if r.level == ValidationLevel.ERROR]
        
        assert len(strict_errors) >= len(lenient_errors)


class TestValidationResultProcessing:
    """Test validation result processing and reporting."""
    
    def test_validation_result_creation(self):
        """Test ValidationResult creation."""
        result = ValidationResult(
            level=ValidationLevel.ERROR,
            message="Test error message",
            parameter="test_param",
            value=123,
            expected_range=(0, 100),
            suggestion="Fix the test parameter"
        )
        
        assert result.level == ValidationLevel.ERROR
        assert result.message == "Test error message"
        assert result.parameter == "test_param"
        assert result.value == 123
        assert result.expected_range == (0, 100)
        assert result.suggestion == "Fix the test parameter"
    
    def test_format_validation_report_empty(self):
        """Test formatting empty validation results."""
        results = []
        report = format_validation_report(results)
        assert "All clinical validation checks passed" in report
        assert "✅" in report
    
    def test_format_validation_report_with_issues(self):
        """Test formatting validation report with issues."""
        results = [
            ValidationResult(
                level=ValidationLevel.ERROR,
                message="Critical error",
                parameter="param1",
                value="bad_value",
                suggestion="Fix this immediately"
            ),
            ValidationResult(
                level=ValidationLevel.WARNING,
                message="Warning message",
                parameter="param2",
                value="warning_value",
                suggestion="Consider fixing this"
            ),
            ValidationResult(
                level=ValidationLevel.INFO,
                message="Info message",
                parameter="param3",
                value="info_value"
            )
        ]
        
        report = format_validation_report(results)
        
        # Check report structure
        assert "Clinical Validation Report" in report
        assert "Total Issues: 3" in report
        assert "Errors: 1, Warnings: 1" in report
        assert "Critical error" in report
        assert "Warning message" in report
        assert "Info message" in report
        assert "Fix this immediately" in report
        assert "Consider fixing this" in report


class TestClinicalParametersValidation:
    """Test comprehensive clinical parameters validation."""
    
    def test_validate_clinical_parameters_structures(self):
        """Test structure validation through main function."""
        structure_data = [
            {
                'name': 'PTV_7000',
                'type': 'PTV',
                'volume_cc': 150.0
            },
            {
                'name': 'Heart',
                'type': 'ORGAN',
                'volume_cc': 600.0
            }
        ]
        
        results = validate_clinical_parameters(structures=structure_data)
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
    
    def test_validate_clinical_parameters_dose(self):
        """Test dose validation through main function."""
        dose_data = {
            'dose_array': np.ones((50, 50, 30)) * 2.0,
            'dose_units': 'Gy',
            'prescription_dose': 70.0,
            'fractions': 35
        }
        
        results = validate_clinical_parameters(dose_data=dose_data)
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
    
    def test_validate_clinical_parameters_geometric(self):
        """Test geometric validation through main function."""
        geometric_data = [
            {
                'name': 'CT_Series',
                'frame_of_reference_uid': '*******.5',
                'pixel_spacing': [1.0, 1.0]
            },
            {
                'name': 'Structure_Set',
                'frame_of_reference_uid': '*******.5',
                'pixel_spacing': [1.0, 1.0]
            }
        ]
        
        results = validate_clinical_parameters(geometric_data=geometric_data)
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
    
    def test_validate_clinical_parameters_combined(self):
        """Test combined validation of all parameter types."""
        structure_data = [{'name': 'PTV', 'type': 'PTV', 'volume_cc': 100.0}]
        dose_data = {'prescription_dose': 70.0, 'fractions': 35, 'dose_units': 'Gy'}
        geometric_data = [
            {'name': 'CT', 'frame_of_reference_uid': '1.2.3'},
            {'name': 'Struct', 'frame_of_reference_uid': '1.2.3'}
        ]
        
        results = validate_clinical_parameters(
            structures=structure_data,
            dose_data=dose_data,
            geometric_data=geometric_data
        )
        
        # Should have results from all validation types
        assert len(results) >= 0  # May have warnings but should not fail
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) == 0
    
    def test_validate_clinical_parameters_strict_mode(self):
        """Test strict vs lenient mode in main function."""
        # Marginal case that should behave differently in strict vs lenient
        structure_data = [
            {
                'name': 'SmallPTV',
                'type': 'PTV',
                'volume_cc': 0.4  # Below minimum
            }
        ]
        
        strict_results = validate_clinical_parameters(
            structures=structure_data,
            strict_mode=True
        )
        
        lenient_results = validate_clinical_parameters(
            structures=structure_data,
            strict_mode=False
        )
        
        strict_errors = [r for r in strict_results if r.level == ValidationLevel.ERROR]
        lenient_errors = [r for r in lenient_results if r.level == ValidationLevel.ERROR]
        
        # Strict mode should be more restrictive
        assert len(strict_errors) >= len(lenient_errors)


class TestSpecialCases:
    """Test special cases and edge conditions."""
    
    def test_unknown_structure_type(self):
        """Test validation with unknown structure type."""
        validator = ClinicalValidator()
        results = validator.validate_structure_volume(
            volume_cc=100.0,
            structure_name="Unknown",
            structure_type="UNKNOWN_TYPE"
        )
        
        # Should have warning about unknown type and fall back to ORGAN
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
        assert any("unknown structure type" in r.message.lower() for r in warning_results)
    
    def test_name_type_consistency_validation(self):
        """Test name-type consistency checks."""
        validator = ClinicalValidator()
        
        # PTV name but ORGAN type
        results = validator.validate_structure_naming(
            structure_name="PTV_7000",
            structure_type="ORGAN"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0
        assert any("suggests target volume" in r.message.lower() for r in warning_results)
        
        # Target type but no target in name
        results = validator.validate_structure_naming(
            structure_name="MyStructure",
            structure_type="PTV"
        )
        info_results = [r for r in results if r.level == ValidationLevel.INFO]
        assert len(info_results) > 0
    
    def test_empty_geometric_objects_list(self):
        """Test geometric validation with empty or single object list."""
        validator = ClinicalValidator()
        
        # Empty list
        results = validator.validate_geometric_consistency([])
        assert len(results) == 0
        
        # Single object
        results = validator.validate_geometric_consistency([
            {'name': 'Single', 'frame_of_reference_uid': '1.2.3'}
        ])
        assert len(results) == 0
    
    def test_dose_parameter_edge_cases(self):
        """Test dose parameter validation edge cases."""
        validator = ClinicalValidator()
        
        # Zero fractions
        results = validator.validate_dose_parameters(
            prescription_dose=70.0,
            fractions=0
        )
        error_results = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(error_results) > 0
        
        # Very small prescription dose
        results = validator.validate_dose_parameters(
            prescription_dose=0.001,
            fractions=1,
            dose_units="Gy"
        )
        warning_results = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warning_results) > 0


if __name__ == "__main__":
    pytest.main([__file__])