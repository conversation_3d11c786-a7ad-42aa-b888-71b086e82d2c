"""
Cross-platform compatibility testing for pyrt-dicom Task 2.6.3.

This module tests compatibility across different Python versions, operating systems,
and library versions to ensure consistent behavior in diverse environments.

Test Coverage:
- Python version compatibility (3.10+)
- NumPy version compatibility
- pydicom version compatibility
- File system path handling across platforms
- Byte order and endianness consistency
- Timezone and locale handling
"""

import pytest
import sys
import platform
import numpy as np
import pydicom
from pathlib import Path
import tempfile
import shutil
from packaging import version
import os
import warnings

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.core.rt_struct import RTStructureSet
from pyrt_dicom.utils.exceptions import ValidationError, DicomCreationError


@pytest.fixture
def temp_cross_platform_dir():
    """Create temporary directory for cross-platform testing."""
    temp_dir = tempfile.mkdtemp(prefix="pyrt_cross_platform_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def platform_test_data():
    """Create test data for cross-platform testing."""
    # Use deterministic data for cross-platform consistency
    np.random.seed(42)
    ct_array = np.random.randint(-1000, 3000, size=(64, 64, 10), dtype=np.int16)
    
    # Simple structure mask
    mask = np.zeros((64, 64, 10), dtype=bool)
    mask[20:44, 20:44, 3:7] = True
    
    geometric_params = {
        'pixel_spacing': [1.0, 1.0],
        'slice_thickness': 2.5,
        'image_position': [0.0, 0.0, 0.0],
        'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
        'patient_position': 'HFS'
    }
    
    return {
        'ct_array': ct_array,
        'mask': mask,
        'geometric_params': geometric_params,
        'patient_info': {
            'PatientID': 'XPLAT_001',
            'PatientName': 'CrossPlatform^Test^Patient'
        }
    }


class TestPythonVersionCompatibility:
    """Test compatibility across different Python versions."""
    
    def test_python_version_requirement(self):
        """Test that Python version meets minimum requirement (3.10+)."""
        python_version = version.parse(platform.python_version())
        minimum_version = version.parse("3.10.0")
        
        assert python_version >= minimum_version, f"Python {python_version} < required {minimum_version}"
        print(f"✅ Python version {python_version} meets requirement (≥{minimum_version})")
    
    def test_python_features_compatibility(self, platform_test_data):
        """Test that required Python features work correctly."""
        
        # Test pathlib (Python 3.4+ feature, essential for cross-platform paths)
        test_path = Path("test") / "subdir" / "file.dcm"
        assert isinstance(test_path, Path)
        assert str(test_path).replace('\\', '/') == "test/subdir/file.dcm"  # Normalize for comparison
        
        # Test type hints (Python 3.5+ feature, used throughout codebase)
        def test_type_hint_function(data: dict) -> bool:
            return isinstance(data, dict)
        
        assert test_type_hint_function(platform_test_data)
        
        # Test f-strings (Python 3.6+ feature, used in error messages)
        patient_id = platform_test_data['patient_info']['PatientID']
        formatted_string = f"Patient ID: {patient_id}"
        assert formatted_string == "Patient ID: XPLAT_001"
        
        # Test dataclasses if used (Python 3.7+ feature)
        try:
            from dataclasses import dataclass
            
            @dataclass
            class TestDataClass:
                value: int
            
            test_obj = TestDataClass(42)
            assert test_obj.value == 42
        except ImportError:
            # dataclasses not available, skip this test
            pass
        
        print("✅ Python language features compatibility verified")
    
    def test_numpy_operations_consistency(self, platform_test_data):
        """Test NumPy operations produce consistent results across platforms."""
        
        ct_array = platform_test_data['ct_array']
        
        # Test array creation and basic operations
        assert ct_array.dtype == np.int16
        assert ct_array.shape == (64, 64, 10)
        
        # Test deterministic operations that should be cross-platform consistent
        array_sum = np.sum(ct_array)
        array_mean = np.mean(ct_array)
        array_std = np.std(ct_array)
        
        # These values should be consistent due to fixed random seed
        assert isinstance(array_sum, (int, np.integer))
        assert isinstance(array_mean, (float, np.floating))
        assert isinstance(array_std, (float, np.floating))
        
        # Test array indexing and slicing
        subset = ct_array[0:5, 10:20, 10:20]
        assert subset.shape == (5, 10, 10)
        
        # Test dtype preservation
        modified_array = ct_array * 2
        assert modified_array.dtype == ct_array.dtype or modified_array.dtype == np.int32  # Overflow handling may vary
        
        print(f"✅ NumPy operations consistent: sum={array_sum}, mean={array_mean:.2f}, std={array_std:.2f}")


class TestLibraryVersionCompatibility:
    """Test compatibility with different versions of key dependencies."""
    
    def test_numpy_version_compatibility(self):
        """Test NumPy version compatibility."""
        numpy_version = version.parse(np.__version__)
        
        # NumPy should be reasonably recent for pydicom compatibility
        minimum_numpy = version.parse("1.19.0")  # Conservative minimum
        assert numpy_version >= minimum_numpy, f"NumPy {numpy_version} may be too old (recommended ≥{minimum_numpy})"
        
        # Test key NumPy features used in the codebase
        test_array = np.array([1, 2, 3], dtype=np.int16)
        assert test_array.dtype == np.int16
        assert hasattr(np, 'zeros')
        assert hasattr(np, 'ones')
        assert hasattr(np, 'random')
        
        print(f"✅ NumPy version {numpy_version} compatible")
    
    def test_pydicom_version_compatibility(self):
        """Test pydicom version compatibility."""
        pydicom_version = version.parse(pydicom.__version__)
        
        # pydicom 3.0.1+ required per project specification
        minimum_pydicom = version.parse("3.0.1")
        assert pydicom_version >= minimum_pydicom, f"pydicom {pydicom_version} < required {minimum_pydicom}"
        
        # Test key pydicom features
        from pydicom.dataset import Dataset
        from pydicom.uid import generate_uid
        
        # Test dataset creation
        ds = Dataset()
        ds.PatientID = "TEST_001"
        assert ds.PatientID == "TEST_001"
        
        # Test UID generation
        test_uid = generate_uid()
        assert isinstance(test_uid, str)
        assert len(test_uid) > 0
        
        print(f"✅ pydicom version {pydicom_version} compatible")
    
    def test_library_interaction_compatibility(self, platform_test_data):
        """Test that NumPy and pydicom interact correctly."""
        
        ct_array = platform_test_data['ct_array']
        
        # Test NumPy array to pydicom pixel data
        pixel_data = ct_array.tobytes()
        assert isinstance(pixel_data, bytes)
        assert len(pixel_data) == ct_array.nbytes
        
        # Test reconstructing array from bytes
        reconstructed = np.frombuffer(pixel_data, dtype=ct_array.dtype).reshape(ct_array.shape)
        assert np.array_equal(ct_array, reconstructed)
        
        # Test with pydicom Dataset
        from pydicom.dataset import Dataset
        
        ds = Dataset()
        ds.PixelData = pixel_data
        ds.Rows = ct_array.shape[1] 
        ds.Columns = ct_array.shape[2]
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 1  # Signed
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        
        # Should not raise exceptions
        assert hasattr(ds, 'PixelData')
        assert ds.Rows == ct_array.shape[1]
        
        print("✅ NumPy-pydicom interaction compatible")


class TestFileSystemCompatibility:
    """Test file system operations across platforms."""
    
    def test_path_handling_cross_platform(self, platform_test_data, temp_cross_platform_dir):
        """Test path handling works across different operating systems."""
        
        # Test path creation and manipulation
        base_path = temp_cross_platform_dir
        
        # Test nested directory creation
        nested_dir = base_path / "nested" / "deep" / "directory"
        nested_dir.mkdir(parents=True, exist_ok=True)
        assert nested_dir.exists()
        
        # Test file path with various characters
        test_filename = "test_file_123.dcm"
        file_path = nested_dir / test_filename
        
        # Create CT series and save to test path
        ct_series = CTSeries.from_array(
            pixel_array=platform_test_data['ct_array'],
            pixel_spacing=platform_test_data['geometric_params']['pixel_spacing'],
            slice_thickness=platform_test_data['geometric_params']['slice_thickness'],
            patient_info=platform_test_data['patient_info']
        )
        
        # Save single slice for path testing
        # Use save_series for multi-slice data
        saved_paths = ct_series.save_series(nested_dir)
        saved_path = saved_paths[0]  # Use first slice for testing
        assert saved_path.exists()
        assert saved_path.name == test_filename
        
        # Test path resolution and normalization
        resolved_path = saved_path.resolve()
        assert resolved_path.exists()
        
        print(f"✅ Cross-platform path handling verified: {saved_path}")
    
    def test_file_permissions_handling(self, platform_test_data, temp_cross_platform_dir):
        """Test file permission handling across platforms."""
        
        # Create test file
        test_file = temp_cross_platform_dir / "permissions_test.dcm"
        
        ct_series = CTSeries.from_array(
            pixel_array=platform_test_data['ct_array'],
            pixel_spacing=platform_test_data['geometric_params']['pixel_spacing'],
            slice_thickness=platform_test_data['geometric_params']['slice_thickness'],
            patient_info=platform_test_data['patient_info']
        )
        
        # Use save_series for multi-slice data
        test_dir = temp_cross_platform_dir / "permissions_dir"
        test_dir.mkdir()
        saved_paths = ct_series.save_series(test_dir)
        saved_path = saved_paths[0]  # Use first slice for testing
        assert saved_path.exists()
        
        # Test file is readable
        assert os.access(saved_path, os.R_OK)
        
        # Test file size is reasonable
        file_size = saved_path.stat().st_size
        assert file_size > 1000, f"File size {file_size} bytes seems too small"
        
        print(f"✅ File permissions handling verified: {file_size} bytes")
    
    def test_unicode_path_handling(self, platform_test_data, temp_cross_platform_dir):
        """Test handling of Unicode characters in file paths."""
        
        # Test with Unicode characters (where supported by filesystem)
        try:
            unicode_dir = temp_cross_platform_dir / "unicode_测试_тест"
            unicode_dir.mkdir(exist_ok=True)
            
            unicode_file = unicode_dir / "测试文件.dcm"
            
            ct_series = CTSeries.from_array(
                ct_array=platform_test_data['ct_array'],
                **platform_test_data['geometric_params'],
                patient_info=platform_test_data['patient_info']
            )
            
            # Use save_series for multi-slice data
            saved_paths = ct_series.save_series(unicode_dir)
            saved_path = saved_paths[0]  # Use first slice for testing
            assert saved_path.exists()
            
            print(f"✅ Unicode path handling verified: {saved_path.name}")
            
        except (OSError, UnicodeError, ValueError):
            # Some filesystems don't support Unicode, skip gracefully
            print("⚠️  Unicode paths not supported on this filesystem, skipping")


class TestByteOrderAndEndianness:
    """Test byte order and endianness consistency."""
    
    def test_numpy_array_byte_order(self, platform_test_data):
        """Test NumPy array byte order consistency."""
        
        ct_array = platform_test_data['ct_array']
        
        # Test native byte order
        native_array = ct_array.astype(ct_array.dtype.newbyteorder('='))  # Native order
        assert native_array.dtype.byteorder in ('=', '<', '>')
        
        # Test explicit little-endian (DICOM standard)
        little_endian = ct_array.astype(ct_array.dtype.newbyteorder('<'))
        assert little_endian.dtype.byteorder == '<'
        
        # Test conversion consistency
        converted_back = little_endian.astype(ct_array.dtype.newbyteorder('='))
        assert np.array_equal(ct_array, converted_back)
        
        print(f"✅ Byte order consistency verified: native={native_array.dtype}, LE={little_endian.dtype}")
    
    def test_dicom_byte_order_consistency(self, platform_test_data, temp_cross_platform_dir):
        """Test DICOM file byte order is consistent across platforms."""
        
        # Create DICOM file
        ct_series = CTSeries.from_array(
            pixel_array=platform_test_data['ct_array'],
            pixel_spacing=platform_test_data['geometric_params']['pixel_spacing'],
            slice_thickness=platform_test_data['geometric_params']['slice_thickness'],
            patient_info=platform_test_data['patient_info']
        )
        
        # Use save_series for multi-slice data
        byte_order_dir = temp_cross_platform_dir / "byte_order_dir"
        byte_order_dir.mkdir()
        saved_paths = ct_series.save_series(byte_order_dir)
        saved_path = saved_paths[0]  # Use first slice for testing
        
        # Read back and verify byte order
        reloaded_ds = pydicom.dcmread(saved_path)
        
        # DICOM files should use consistent byte order
        if hasattr(reloaded_ds, 'PixelData'):
            pixel_array = reloaded_ds.pixel_array
            assert pixel_array.dtype.byteorder in ('=', '<', '>')  # Valid byte orders
            
            # Values should match original (accounting for any scaling)
            original_shape = platform_test_data['ct_array'].shape
            if len(pixel_array.shape) == 2:  # Single slice
                assert pixel_array.shape == original_shape[1:]  # Skip first dimension
            else:
                assert pixel_array.shape == original_shape
        
        print("✅ DICOM byte order consistency verified")


class TestEnvironmentSpecificBehavior:
    """Test behavior in different environments."""
    
    def test_timezone_independence(self):
        """Test that DICOM creation is timezone-independent."""
        
        import datetime
        
        # Get current timezone info
        current_time = datetime.datetime.now()
        utc_time = datetime.datetime.utcnow()
        
        # DICOM timestamps should be consistent regardless of timezone
        # (They typically use local time without timezone info)
        dicom_date = current_time.strftime("%Y%m%d")
        dicom_time = current_time.strftime("%H%M%S")
        
        assert len(dicom_date) == 8  # YYYYMMDD
        assert len(dicom_time) == 6  # HHMMSS
        assert dicom_date.isdigit()
        assert dicom_time.isdigit()
        
        print(f"✅ Timezone independence verified: {dicom_date} {dicom_time}")
    
    def test_locale_independence(self):
        """Test that numeric operations are locale-independent."""
        
        import locale
        
        # Get current locale
        current_locale = locale.getlocale()
        
        # Test that decimal operations work regardless of locale
        test_value = 1.5
        string_repr = f"{test_value}"
        
        # Should use dot as decimal separator in DICOM context
        assert "." in string_repr, f"Decimal representation {string_repr} not using dot separator"
        
        # Test numeric parsing
        parsed_value = float(string_repr)
        assert abs(parsed_value - test_value) < 1e-10
        
        print(f"✅ Locale independence verified: {current_locale}, decimal={string_repr}")
    
    def test_memory_management_consistency(self, platform_test_data):
        """Test memory management behavior is consistent."""
        
        import gc
        
        # Test garbage collection behavior
        initial_objects = len(gc.get_objects())
        
        # Create and destroy multiple objects
        for i in range(10):
            ct_series = CTSeries.from_array(
                pixel_array=platform_test_data['ct_array'],
                pixel_spacing=platform_test_data['geometric_params']['pixel_spacing'],
                slice_thickness=platform_test_data['geometric_params']['slice_thickness'],
                patient_info={
                    'PatientID': f'MEM_TEST_{i:03d}',
                    'PatientName': f'Memory^Test^{i:03d}'
                }
            )
            del ct_series
        
        # Force garbage collection
        gc.collect()
        
        final_objects = len(gc.get_objects())
        object_increase = final_objects - initial_objects
        
        # Should not have excessive object accumulation
        assert object_increase < 1000, f"Excessive object accumulation: {object_increase} objects"
        
        print(f"✅ Memory management consistent: +{object_increase} objects after 10 iterations")


class TestIntegrationConsistency:
    """Test that complete workflows work consistently across platforms."""
    
    def test_end_to_end_cross_platform_consistency(self, platform_test_data, temp_cross_platform_dir):
        """Test complete workflow produces consistent results across platforms."""
        
        # Create complete workflow
        ct_series = CTSeries.from_array(
            pixel_array=platform_test_data['ct_array'],
            pixel_spacing=platform_test_data['geometric_params']['pixel_spacing'],
            slice_thickness=platform_test_data['geometric_params']['slice_thickness'],
            patient_info=platform_test_data['patient_info']
        )
        
        # Save CT series
        ct_dir = temp_cross_platform_dir / "e2e_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        
        # Create RT Structure
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks={'TestStructure': platform_test_data['mask']},
            patient_info=platform_test_data['patient_info']
        )
        
        struct_path = temp_cross_platform_dir / "e2e_struct.dcm"
        rt_struct.save(struct_path)
        
        # Verify files are readable and consistent
        for ct_path in ct_paths[:3]:  # Check first 3 CT files
            ct_ds = pydicom.dcmread(ct_path)
            assert ct_ds.PatientID == platform_test_data['patient_info']['PatientID']
            assert ct_ds.Modality == 'CT'
        
        struct_ds = pydicom.dcmread(struct_path)
        assert struct_ds.PatientID == platform_test_data['patient_info']['PatientID']
        assert struct_ds.Modality == 'RTSTRUCT'
        assert len(struct_ds.StructureSetROISequence) == 1
        
        # Test values that should be deterministic
        roi_name = struct_ds.StructureSetROISequence[0].ROIName
        assert roi_name == 'TestStructure'
        
        print(f"✅ End-to-end cross-platform consistency verified:")
        print(f"   - Platform: {platform.system()} {platform.release()}")
        print(f"   - Python: {platform.python_version()}")
        print(f"   - NumPy: {np.__version__}")
        print(f"   - pydicom: {pydicom.__version__}")
        print(f"   - CT files: {len(ct_paths)}")
        print(f"   - Structure: {roi_name}")


def test_platform_summary():
    """Print platform summary for test documentation."""
    
    print("\n" + "="*60)
    print("CROSS-PLATFORM COMPATIBILITY TEST SUMMARY")
    print("="*60)
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    print(f"Python Version: {platform.python_version()}")
    print(f"Python Implementation: {platform.python_implementation()}")
    print(f"NumPy Version: {np.__version__}")
    print(f"pydicom Version: {pydicom.__version__}")
    
    # Check optional dependencies
    try:
        import scipy
        print(f"SciPy Version: {scipy.__version__}")
    except ImportError:
        print("SciPy: Not available")
    
    try:
        import matplotlib
        print(f"Matplotlib Version: {matplotlib.__version__}")
    except ImportError:
        print("Matplotlib: Not available")
    
    print("="*60)


if __name__ == "__main__":
    # Run cross-platform tests
    test_platform_summary()
    pytest.main([__file__, "-v", "-s"])