"""
Tests for documentation examples in pyrt-dicom API documentation.

This module validates that all code examples in the API documentation are
syntactically correct, executable, and produce expected results. It serves
as both documentation validation and integration testing.

Requirements:
- All docstring examples must be valid Python code
- Examples should demonstrate realistic clinical workflows
- Code must be compatible with the current pyrt-dicom API
- Performance examples should include realistic benchmarks
"""

import pytest
import re
import inspect
import ast
import sys
from typing import List, Dict, Any, Optional
from unittest.mock import Mock, patch

import numpy as np

# Import all major pyrt-dicom modules for testing
from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.uid_generation.generators import (
    UIDGenerator, RandomUIDGenerator, HashBasedUIDGenerator, DefaultUIDGenerator
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.validation.patient import PatientInfoValidator
from pyrt_dicom.utils.exceptions import *


class DocumentationExampleExtractor:
    """Extract and validate code examples from docstrings."""
    
    def __init__(self):
        self.code_block_pattern = re.compile(r'```python\n(.*?)\n```', re.DOTALL)
        self.doctest_pattern = re.compile(r'>>> (.*?)(?=\n(?:>>>|\.\.\.|$))', re.DOTALL | re.MULTILINE)
        
    def extract_code_blocks(self, docstring: str) -> List[str]:
        """Extract Python code blocks from docstring."""
        if not docstring:
            return []
            
        # Extract ```python code blocks
        code_blocks = []
        for match in self.code_block_pattern.finditer(docstring):
            code_blocks.append(match.group(1).strip())
            
        # Extract doctest examples
        for match in self.doctest_pattern.finditer(docstring):
            code_line = match.group(1).strip()
            if code_line and not code_line.startswith('#'):
                code_blocks.append(code_line)
                
        return code_blocks
        
    def validate_syntax(self, code: str) -> bool:
        """Validate Python syntax of code block."""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
            
    def extract_imports(self, code: str) -> List[str]:
        """Extract import statements from code."""
        imports = []
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    imports.extend([alias.name for alias in node.names])
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    imports.extend([f"{module}.{alias.name}" for alias in node.names])
        except:
            pass
        return imports


class TestDocumentationExamples:
    """Test cases for validating documentation examples."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.extractor = DocumentationExampleExtractor()
        
        # Mock clinical data for examples
        self.mock_ct_data = Mock()
        self.mock_ct_data.StudyInstanceUID = "1.2.3.4.5"
        self.mock_ct_data.FrameOfReferenceUID = "1.2.3.4.6"
        
        # Mock arrays for testing
        self.mock_ptv_mask = np.ones((64, 64, 32), dtype=bool)
        self.mock_oar_mask = np.zeros((64, 64, 32), dtype=bool)
        self.mock_dose_array = np.random.rand(64, 64, 32) * 7000
        
    def test_base_dicom_creator_examples(self):
        """Test BaseDicomCreator documentation examples."""
        # Get docstring examples
        docstring = BaseDicomCreator.__doc__
        code_blocks = self.extractor.extract_code_blocks(docstring)
        
        # Test syntax validation
        for i, code in enumerate(code_blocks):
            assert self.extractor.validate_syntax(code), f"Code block {i+1} has syntax errors"
            
        # Test specific examples with mocked dependencies
        with patch('pydicom.dcmread') as mock_dcmread:
            mock_dcmread.return_value = self.mock_ct_data
            
            # Test basic instantiation example
            patient_info = {
                'PatientID': 'RT001', 
                'PatientName': 'Doe^John',
                'PatientBirthDate': '19800101'
            }
            
            # This should work without errors (testing the pattern, not actual creation)
            assert 'PatientID' in patient_info
            assert len(patient_info['PatientID']) <= 64  # DICOM VR compliance
            
    def test_uid_generator_examples(self):
        """Test UID generator documentation examples."""
        # Test RandomUIDGenerator examples
        random_gen = RandomUIDGenerator()
        
        # Test basic UID generation
        study_uid = random_gen.generate_study_instance_uid()
        series_uid = random_gen.generate_series_instance_uid()
        instance_uid = random_gen.generate_sop_instance_uid()
        
        # Validate UID format
        assert len(study_uid) <= 64
        assert all(c.isdigit() or c == '.' for c in study_uid)
        assert study_uid.startswith(random_gen.root_uid)
        
        # Test HashBasedUIDGenerator examples
        hash_gen = HashBasedUIDGenerator()
        
        # Test reproducible generation
        seed = "Patient_001_Study_Planning_CT_20231201"
        uid1 = hash_gen.generate_study_instance_uid(seed)
        uid2 = hash_gen.generate_study_instance_uid(seed)
        
        assert uid1 == uid2  # Should be identical for same seed
        assert len(uid1) <= 64
        
        # Test DefaultUIDGenerator factory methods
        default_gen = DefaultUIDGenerator.create_default_generator()
        assert isinstance(default_gen, RandomUIDGenerator)
        
        hash_factory = DefaultUIDGenerator.create_hash_generator()
        assert isinstance(hash_factory, HashBasedUIDGenerator)
        
    def test_coordinate_transformer_examples(self):
        """Test CoordinateTransformer documentation examples."""
        # Test basic transformer creation
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            image_position=(-250.0, -250.0, -150.0)
        )
        
        # Test coordinate transformation
        patient_coords = np.array([
            [50.0, 120.0, -45.0],
            [55.0, 125.0, -45.0],  
            [45.0, 130.0, -45.0]
        ])
        
        dicom_coords = transformer.patient_to_dicom(patient_coords)
        assert dicom_coords.shape == patient_coords.shape
        
        # Test round-trip accuracy
        round_trip = transformer.dicom_to_patient(dicom_coords)
        accuracy = np.max(np.abs(patient_coords - round_trip))
        assert accuracy < 1e-10  # Sub-millimeter accuracy
        
        # Test transformation matrix
        matrix = transformer.get_transformation_matrix()
        assert matrix.shape == (4, 4)
        
    def test_patient_validator_examples(self):
        """Test PatientInfoValidator documentation examples."""
        validator = PatientInfoValidator()
        
        # Test valid patient information
        valid_patient = {
            'PatientID': 'RT001',
            'PatientName': 'Doe^John^M',
            'PatientBirthDate': '19800101',
            'PatientSex': 'M'
        }
        
        errors = validator.validate_patient_info(valid_patient)
        assert len(errors) == 0  # Should be valid
        
        # Test invalid patient information (65 characters - exceeds 64 limit)
        invalid_patient = {
            'PatientID': 'Very_Long_Patient_ID_That_Exceeds_The_Maximum_DICOM_Length_Limits'
        }
        
        errors = validator.validate_patient_info(invalid_patient)
        assert len(errors) > 0  # Should have errors
        assert any('exceeds maximum length' in error for error in errors)
        
    def test_exception_handling_examples(self):
        """Test exception handling documentation examples."""
        # Test ValidationError creation and handling
        try:
            raise ValidationError(
                "Test validation error",
                parameter_name="dose_value",
                current_value=50.0,
                valid_range=(0.1, 30.0),
                units="Gy"
            )
        except ValidationError as e:
            assert e.clinical_context['parameter_name'] == "dose_value"
            assert e.clinical_context['current_value'] == 50.0
            assert e.clinical_context['valid_range'] == (0.1, 30.0)
            assert len(e.suggestions) > 0
            
        # Test CoordinateSystemError
        try:
            raise CoordinateSystemError(
                "Frame of Reference mismatch",
                frame_of_reference_uid="1.2.3.4.5",
                patient_position="HFS"
            )
        except CoordinateSystemError as e:
            assert e.clinical_context['frame_of_reference_uid'] == "1.2.3.4.5"
            assert e.clinical_context['patient_position'] == "HFS"
            
        # Test UIDGenerationError
        try:
            raise UIDGenerationError(
                "Invalid UID format",
                uid_value="invalid.uid.format.",
                uid_type="StudyInstanceUID"
            )
        except UIDGenerationError as e:
            assert e.clinical_context['uid_value'] == "invalid.uid.format."
            assert e.clinical_context['uid_type'] == "StudyInstanceUID"
            
    def test_performance_examples(self):
        """Test performance benchmark examples from documentation."""
        # Test UID generation performance
        random_gen = RandomUIDGenerator()
        hash_gen = HashBasedUIDGenerator()
        
        import time
        
        # Test random generation performance (should be fast)
        start_time = time.time()
        for i in range(100):  # Reduced from 1000 for test speed
            uid = random_gen.generate_sop_instance_uid()
        random_time = time.time() - start_time
        
        # Should generate 100 UIDs in reasonable time (less than 1 second)
        assert random_time < 1.0
        
        # Test hash generation performance
        start_time = time.time()
        for i in range(100):
            uid = hash_gen.generate_sop_instance_uid(f"patient_data_{i}")
        hash_time = time.time() - start_time
        
        assert hash_time < 1.0
        
        # Test coordinate transformation performance
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0
        )
        
        # Generate test dataset (smaller for unit tests)
        test_coords = np.random.rand(1000, 3) * 500
        
        start_time = time.time()
        transformed = transformer.patient_to_dicom(test_coords)
        transform_time = time.time() - start_time
        
        # Should transform 1000 points quickly
        assert transform_time < 0.1
        assert transformed.shape == test_coords.shape
        
    def test_clinical_workflow_examples(self):
        """Test complete clinical workflow examples."""
        # Test workflow pattern from documentation
        patient_info = {
            'PatientID': 'RT_001',
            'PatientName': 'Smith^John^A',
            'PatientBirthDate': '19750315',
            'PatientSex': 'M',
            'StudyDescription': 'Prostate IMRT Planning'
        }
        
        # Validate patient information
        validator = PatientInfoValidator()
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 0
        
        # Test UID generation for workflow
        generator = RandomUIDGenerator()
        study_uid = generator.generate_study_instance_uid()
        frame_ref_uid = generator.generate_frame_of_reference_uid()
        
        # Validate UID relationships
        assert study_uid != frame_ref_uid  # Should be different
        assert len(study_uid) <= 64
        assert len(frame_ref_uid) <= 64
        
        # Test coordinate system setup
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5
        )
        
        # Test basic coordinate operations
        test_point = np.array([0, 0, 0])
        dicom_point = transformer.patient_to_dicom(test_point)
        round_trip = transformer.dicom_to_patient(dicom_point)
        
        assert np.allclose(test_point, round_trip, atol=1e-10)
        
    def test_error_recovery_patterns(self):
        """Test error recovery patterns from documentation."""
        # Test retry pattern with parameter correction
        def mock_validation_with_retry():
            """Mock function that demonstrates retry pattern."""
            attempts = 0
            max_retries = 3
            
            while attempts < max_retries:
                try:
                    # Simulate validation that fails first time
                    if attempts == 0:
                        raise ValidationError(
                            "PatientID missing",
                            parameter_name="PatientID"
                        )
                    return True
                except ValidationError as e:
                    attempts += 1
                    if attempts >= max_retries:
                        raise
                    # Auto-correct the issue
                    continue
                    
        # Should succeed after retry
        result = mock_validation_with_retry()
        assert result is True
        
        # Test graceful degradation pattern
        def mock_creation_with_degradation():
            """Mock function demonstrating graceful degradation."""
            try:
                # Simulate initial failure
                raise ValidationError("Strict validation failed")
            except ValidationError:
                # Fall back to less strict validation
                return "fallback_result"
                
        result = mock_creation_with_degradation()
        assert result == "fallback_result"


class TestDocumentationCompleteness:
    """Test documentation completeness and quality."""
    
    def test_all_public_methods_documented(self):
        """Ensure all public methods have docstrings with examples."""
        classes_to_check = [
            BaseDicomCreator,
            RandomUIDGenerator,
            HashBasedUIDGenerator,
            CoordinateTransformer,
            PatientInfoValidator
        ]
        
        for cls in classes_to_check:
            # Check class docstring
            assert cls.__doc__ is not None, f"{cls.__name__} missing class docstring"
            assert len(cls.__doc__.strip()) > 50, f"{cls.__name__} docstring too brief"
            
            # Check public method docstrings
            for method_name in dir(cls):
                if not method_name.startswith('_'):  # Public methods only
                    method = getattr(cls, method_name)
                    if callable(method) and hasattr(method, '__doc__'):
                        assert method.__doc__ is not None, \
                            f"{cls.__name__}.{method_name} missing docstring"
                            
    def test_cross_references_format(self):
        """Test that cross-references use proper Sphinx format."""
        classes_to_check = [BaseDicomCreator, RandomUIDGenerator, CoordinateTransformer]
        
        for cls in classes_to_check:
            docstring = cls.__doc__ or ""
            
            # Should contain cross-references
            if "Cross-References" in docstring or "Related Classes" in docstring:
                # Check for proper Sphinx cross-reference format
                assert ":class:" in docstring or ":mod:" in docstring or ":func:" in docstring, \
                    f"{cls.__name__} cross-references not in Sphinx format"
                    
    def test_clinical_notes_present(self):
        """Test that clinical context is provided in documentation."""
        classes_to_check = [BaseDicomCreator, CoordinateTransformer, PatientInfoValidator]
        
        for cls in classes_to_check:
            docstring = cls.__doc__ or ""
            
            # Should contain clinical context
            clinical_indicators = [
                "Clinical Notes",
                "clinical",
                "medical physics",
                "RT workflow",
                "DICOM standard",
                "patient safety"
            ]
            
            has_clinical_context = any(indicator in docstring for indicator in clinical_indicators)
            assert has_clinical_context, f"{cls.__name__} missing clinical context"
            
    def test_example_code_syntax(self):
        """Test that all example code has valid Python syntax."""
        extractor = DocumentationExampleExtractor()
        
        classes_to_check = [
            BaseDicomCreator,
            RandomUIDGenerator, 
            HashBasedUIDGenerator,
            CoordinateTransformer,
            PatientInfoValidator
        ]
        
        for cls in classes_to_check:
            docstring = cls.__doc__ or ""
            code_blocks = extractor.extract_code_blocks(docstring)
            
            for i, code in enumerate(code_blocks):
                # Skip code blocks that are just comments or imports
                if code.strip().startswith('#') or code.strip().startswith('import'):
                    continue
                    
                # Skip incomplete code fragments (indicated by ...)
                if '...' in code:
                    continue
                    
                assert extractor.validate_syntax(code), \
                    f"{cls.__name__} docstring code block {i+1} has syntax errors:\n{code}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])