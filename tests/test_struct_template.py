"""
Tests for RT Structure Set template implementation.

Tests DICOM RT Structure Set IOD template creation, validation, and clinical
compliance according to DICOM Part 3 Section C.8.8.5.
"""

import pytest
import numpy as np
from pydicom.dataset import Dataset
from pydicom.uid import RTStructureSetStorage

from pyrt_dicom.templates.struct_template import RTStructureSetTemplate
from pyrt_dicom.utils.exceptions import TemplateError


class TestRTStructureSetTemplate:
    """Test RT Structure Set template functionality."""

    def test_sop_class_uid(self):
        """Test that template uses correct SOP Class UID."""
        assert RTStructureSetTemplate.SOP_CLASS_UID == RTStructureSetStorage
        assert RTStructureSetTemplate.SOP_CLASS_UID == "1.2.840.10008.*******.1.481.3"

    def test_required_modules(self):
        """Test that all required DICOM modules are specified."""
        expected_modules = [
            "Patient",
            "General Study", 
            "RT Series",
            "Frame of Reference",
            "General Equipment",
            "RT Structure Set",
            "Structure Set",
            "ROI Contour",
            "RT ROI Observations",
            "SOP Common",
        ]
        
        assert RTStructureSetTemplate.REQUIRED_MODULES == expected_modules
        assert len(RTStructureSetTemplate.REQUIRED_MODULES) == 10

    def test_clinical_colors_defined(self):
        """Test that clinical color defaults are properly defined."""
        colors = RTStructureSetTemplate.CLINICAL_COLORS
        
        # Test key structure types have colors defined
        assert 'PTV' in colors
        assert 'CTV' in colors
        assert 'GTV' in colors
        assert 'Bladder' in colors
        assert 'Rectum' in colors
        assert 'SpinalCord' in colors
        
        # Test colors are valid RGB tuples
        for name, color in colors.items():
            assert isinstance(color, tuple), f"Color for {name} must be tuple"
            assert len(color) == 3, f"Color for {name} must have 3 components"
            for component in color:
                assert isinstance(component, int), f"Color component must be integer"
                assert 0 <= component <= 255, f"Color component must be 0-255"

    def test_get_clinical_color_exact_match(self):
        """Test clinical color lookup with exact matches."""
        # Test exact matches
        assert RTStructureSetTemplate.get_clinical_color('PTV') == (255, 0, 0)
        assert RTStructureSetTemplate.get_clinical_color('Bladder') == (255, 255, 0)  # Yellow for bladder
        assert RTStructureSetTemplate.get_clinical_color('Rectum') == (165, 42, 42)   # Brown for rectum

    def test_get_clinical_color_partial_match(self):
        """Test clinical color lookup with partial matches."""
        # Test partial matches
        assert RTStructureSetTemplate.get_clinical_color('PTV_7000') == (255, 0, 0)
        assert RTStructureSetTemplate.get_clinical_color('Left_Kidney') == (255, 0, 0)  # Default red (no exact match)
        assert RTStructureSetTemplate.get_clinical_color('SpinalCord') == (0, 128, 0)    # Green (matches SpinalCord logic)

    def test_get_clinical_color_prefix_match(self):
        """Test clinical color lookup with prefix-based matching."""
        # Test prefix-based matching
        assert RTStructureSetTemplate.get_clinical_color('CTV_High') == (255, 128, 0)
        assert RTStructureSetTemplate.get_clinical_color('GTV_Primary') == (255, 255, 0)

    def test_get_clinical_color_default(self):
        """Test clinical color lookup returns default for unknown structures."""
        # Test default red color for unknown structures
        unknown_color = RTStructureSetTemplate.get_clinical_color('Unknown_Structure')
        assert unknown_color == (255, 0, 0)  # Default red

    def test_create_dataset_basic(self):
        """Test basic dataset creation with minimal structures."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            }
        ]
        
        frame_uid = "*******.5.6.7"
        
        dataset = RTStructureSetTemplate.create_dataset(
            structures=structures,
            reference_frame_uid=frame_uid
        )
        
        # Test basic DICOM elements
        assert dataset.SOPClassUID == RTStructureSetStorage
        assert hasattr(dataset, 'SOPInstanceUID')
        assert dataset.Modality == "RTSTRUCT"
        assert dataset.FrameOfReferenceUID == frame_uid
        
        # Test structure sequences exist
        assert hasattr(dataset, 'StructureSetROISequence')
        assert hasattr(dataset, 'ROIContourSequence')
        assert hasattr(dataset, 'RTROIObservationsSequence')
        
        # Test sequence lengths match
        assert len(dataset.StructureSetROISequence) == 1
        assert len(dataset.ROIContourSequence) == 1
        assert len(dataset.RTROIObservationsSequence) == 1

    def test_create_dataset_multiple_structures(self):
        """Test dataset creation with multiple structures."""
        structures = [
            {
                'name': 'PTV_7000',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            },
            {
                'name': 'Bladder',
                'number': 2,
                'color': (0, 0, 255),
                'type': 'ORGAN',
                'algorithm': 'AUTOMATIC'
            },
            {
                'name': 'Rectum',
                'number': 3,
                'color': (0, 255, 0),
                'type': 'ORGAN',
                'algorithm': 'MANUAL'
            }
        ]
        
        frame_uid = "*******.5.6.7"
        ct_uids = ["*******.5.6.8", "*******.5.6.9"]
        
        dataset = RTStructureSetTemplate.create_dataset(
            structures=structures,
            reference_frame_uid=frame_uid,
            referenced_ct_sop_instance_uids=ct_uids
        )
        
        # Test all sequences have correct length
        assert len(dataset.StructureSetROISequence) == 3
        assert len(dataset.ROIContourSequence) == 3
        assert len(dataset.RTROIObservationsSequence) == 3
        
        # Test structure data is correctly stored
        for i, expected_struct in enumerate(structures):
            roi_item = dataset.StructureSetROISequence[i]
            contour_item = dataset.ROIContourSequence[i]
            obs_item = dataset.RTROIObservationsSequence[i]
            
            assert roi_item.ROINumber == expected_struct['number']
            assert roi_item.ROIName == expected_struct['name']
            assert roi_item.ROIGenerationAlgorithm == expected_struct['algorithm']
            
            assert contour_item.ReferencedROINumber == expected_struct['number']
            assert contour_item.ROIDisplayColor == list(expected_struct['color'])
            
            assert obs_item.ReferencedROINumber == expected_struct['number']
            assert obs_item.RTROIInterpretedType == expected_struct['type']

    def test_create_dataset_with_referenced_ct(self):
        """Test dataset creation with referenced CT SOP Instance UIDs."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            }
        ]
        
        frame_uid = "*******.5.6.7"
        ct_uids = ["*******.5.6.8", "*******.5.6.9", "*******.5.6.10"]
        
        dataset = RTStructureSetTemplate.create_dataset(
            structures=structures,
            reference_frame_uid=frame_uid,
            referenced_ct_sop_instance_uids=ct_uids
        )
        
        # Test referenced frame of reference sequence
        assert hasattr(dataset, 'ReferencedFrameOfReferenceSequence')
        ref_for_seq = dataset.ReferencedFrameOfReferenceSequence
        assert len(ref_for_seq) == 1
        
        ref_for_item = ref_for_seq[0]
        assert ref_for_item.FrameOfReferenceUID == frame_uid
        
        # Test contour image sequence is created
        rt_study_seq = ref_for_item.RTReferencedStudySequence
        assert len(rt_study_seq) == 1
        
        rt_series_seq = rt_study_seq[0].RTReferencedSeriesSequence
        assert len(rt_series_seq) == 1
        
        contour_img_seq = rt_series_seq[0].ContourImageSequence
        assert len(contour_img_seq) == len(ct_uids)
        
        # Test all CT UIDs are referenced
        referenced_uids = [img.ReferencedSOPInstanceUID for img in contour_img_seq]
        assert set(referenced_uids) == set(ct_uids)

    def test_create_dataset_with_kwargs(self):
        """Test dataset creation with additional parameters."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            }
        ]
        
        dataset = RTStructureSetTemplate.create_dataset(
            structures=structures,
            reference_frame_uid="*******.5.6.7",
            StructureSetLabel="PROSTATE_PLAN",
            StructureSetName="Prostate IMRT Structure Set",
            StructureSetDescription="Planning structures for prostate IMRT"
        )
        
        # Test custom parameters are set
        assert dataset.StructureSetLabel == "PROSTATE_PLAN"
        assert dataset.StructureSetName == "Prostate IMRT Structure Set"
        assert dataset.StructureSetDescription == "Planning structures for prostate IMRT"

    def test_create_dataset_empty_structures_error(self):
        """Test that empty structure list raises appropriate error."""
        with pytest.raises(TemplateError) as exc_info:
            RTStructureSetTemplate.create_dataset(
                structures=[],
                reference_frame_uid="*******.5.6.7"
            )
        
        error = exc_info.value
        assert "At least one structure must be provided" in str(error)
        assert error.clinical_context['modality'] == "RTSTRUCT"
        assert "minimum_required" in error.clinical_context

    def test_create_dataset_missing_name_error(self):
        """Test error handling for structures missing name field."""
        structures = [
            {
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
                # Missing 'name'
            }
        ]
        
        with pytest.raises(TemplateError) as exc_info:
            RTStructureSetTemplate.create_dataset(
                structures=structures,
                reference_frame_uid="*******.5.6.7"
            )
        
        error = exc_info.value
        assert "missing required 'name' field" in str(error)

    def test_create_dataset_missing_number_error(self):
        """Test error handling for structures missing number field."""
        structures = [
            {
                'name': 'PTV',
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
                # Missing 'number'
            }
        ]
        
        with pytest.raises(TemplateError) as exc_info:
            RTStructureSetTemplate.create_dataset(
                structures=structures,
                reference_frame_uid="*******.5.6.7"
            )
        
        error = exc_info.value
        assert "missing required 'number' field" in str(error)

    def test_create_dataset_duplicate_roi_number_error(self):
        """Test error handling for duplicate ROI numbers."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            },
            {
                'name': 'Bladder',
                'number': 1,  # Duplicate number
                'color': (0, 0, 255),
                'type': 'ORGAN',
                'algorithm': 'MANUAL'
            }
        ]
        
        with pytest.raises(TemplateError) as exc_info:
            RTStructureSetTemplate.create_dataset(
                structures=structures,
                reference_frame_uid="*******.5.6.7"
            )
        
        error = exc_info.value
        assert "Duplicate ROI number: 1" in str(error)
        assert error.clinical_context['duplicate_number'] == 1

    def test_create_dataset_missing_color_error(self):
        """Test error handling for structures missing color field."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'type': 'PTV',
                'algorithm': 'MANUAL'
                # Missing 'color'
            }
        ]
        
        with pytest.raises(TemplateError) as exc_info:
            RTStructureSetTemplate.create_dataset(
                structures=structures,
                reference_frame_uid="*******.5.6.7"
            )
        
        error = exc_info.value
        assert "missing required 'color' field" in str(error)

    def test_validate_compliance_valid_dataset(self):
        """Test compliance validation for valid RT Structure Set dataset."""
        structures = [
            {
                'name': 'PTV',
                'number': 1,
                'color': (255, 0, 0),
                'type': 'PTV',
                'algorithm': 'MANUAL'
            }
        ]
        
        dataset = RTStructureSetTemplate.create_dataset(
            structures=structures,
            reference_frame_uid="*******.5.6.7"
        )
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert errors == [], f"Valid dataset should have no errors: {errors}"

    def test_validate_compliance_missing_sop_class_uid(self):
        """Test compliance validation detects missing SOPClassUID."""
        dataset = Dataset()
        dataset.Modality = "RTSTRUCT"
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert any("Missing required SOPClassUID" in error for error in errors)

    def test_validate_compliance_wrong_sop_class_uid(self):
        """Test compliance validation detects incorrect SOPClassUID."""
        dataset = Dataset()
        dataset.SOPClassUID = "*******.5"  # Wrong UID
        dataset.Modality = "RTSTRUCT"
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert any("Invalid SOPClassUID" in error for error in errors)

    def test_validate_compliance_wrong_modality(self):
        """Test compliance validation detects incorrect modality."""
        dataset = Dataset()
        dataset.SOPClassUID = RTStructureSetStorage
        dataset.Modality = "CT"  # Wrong modality
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert any("Invalid modality for RT Structure Set: CT" in error for error in errors)

    def test_validate_compliance_missing_required_elements(self):
        """Test compliance validation detects missing required elements."""
        dataset = Dataset()
        dataset.SOPClassUID = RTStructureSetStorage
        dataset.Modality = "RTSTRUCT"
        # Missing other required elements
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        
        required_elements = [
            "StructureSetLabel",
            "StructureSetROISequence",
            "ROIContourSequence", 
            "RTROIObservationsSequence",
            "ReferencedFrameOfReferenceSequence",
        ]
        
        for element in required_elements:
            assert any(f"Missing required element: {element}" in error for error in errors)

    def test_validate_compliance_sequence_length_mismatch(self):
        """Test compliance validation detects sequence length mismatches."""
        from pydicom.sequence import Sequence
        
        dataset = Dataset()
        dataset.SOPClassUID = RTStructureSetStorage
        dataset.Modality = "RTSTRUCT"
        dataset.StructureSetLabel = "TEST"
        dataset.ReferencedFrameOfReferenceSequence = Sequence()
        
        # Create sequences with different lengths
        dataset.StructureSetROISequence = Sequence([Dataset(), Dataset()])  # 2 items
        dataset.ROIContourSequence = Sequence([Dataset()])  # 1 item
        dataset.RTROIObservationsSequence = Sequence([Dataset()])  # 1 item
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert any("Sequence length mismatch" in error for error in errors)

    def test_validate_compliance_duplicate_roi_numbers(self):
        """Test compliance validation detects duplicate ROI numbers."""
        from pydicom.sequence import Sequence
        
        dataset = Dataset()
        dataset.SOPClassUID = RTStructureSetStorage
        dataset.Modality = "RTSTRUCT" 
        dataset.StructureSetLabel = "TEST"
        dataset.ReferencedFrameOfReferenceSequence = Sequence()
        
        # Create ROI items with duplicate numbers
        roi1 = Dataset()
        roi1.ROINumber = 1
        roi2 = Dataset()
        roi2.ROINumber = 1  # Duplicate
        
        dataset.StructureSetROISequence = Sequence([roi1, roi2])
        dataset.ROIContourSequence = Sequence([Dataset(), Dataset()])
        dataset.RTROIObservationsSequence = Sequence([Dataset(), Dataset()])
        
        errors = RTStructureSetTemplate.validate_compliance(dataset)
        assert any("Duplicate ROI number: 1" in error for error in errors)