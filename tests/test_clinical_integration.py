"""
Real clinical data integration testing for pyrt-dicom.

This module tests the pyrt-dicom library's integration with real clinical DICOM
data including:
- Anonymized clinical DICOM test fixtures
- Multi-vendor TPS compatibility test data
- Various CT geometries and patient positions
- Integration with existing clinical workflows

These tests ensure the library works correctly with real-world clinical data
and maintains compatibility across different vendor systems.
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import pydicom
from pydicom.dataset import Dataset
from pydicom.data import get_testdata_file

from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.validation.geometric import GeometricValidator
from pyrt_dicom.validation.patient import PatientInfoValidator
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    GeometricParameters,
)
from pyrt_dicom.uid_generation.generators import DefaultUIDGenerator
from pyrt_dicom.utils.exceptions import Dicom<PERSON>reation<PERSON><PERSON>r, ValidationError


class TestBaseDicomCreator(BaseDicomCreator):
    """Concrete implementation for testing with clinical data."""

    def _create_modality_specific_dataset(self):
        """Create minimal test dataset."""
        dataset = self._create_base_dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        dataset.Modality = "CT"
        return dataset

    def _validate_modality_specific(self):
        """Test-specific validation."""
        pass


@pytest.fixture
def clinical_ct_dataset():
    """Load a real clinical CT dataset from pydicom test data."""
    try:
        # Use pydicom's built-in CT test data
        ct_path = get_testdata_file("CT_small.dcm", download=False)
        return pydicom.dcmread(ct_path, force=True)
    except Exception:
        # Fallback to creating a minimal CT dataset if test data unavailable
        ds = Dataset()
        ds.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        ds.Modality = "CT"
        ds.PatientID = "TEST_PATIENT_001"
        ds.PatientName = "Test^Patient^Clinical"
        ds.StudyInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.20"
        ds.SeriesInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.21"
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.22"
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        ds.PixelSpacing = [1.0, 1.0]
        ds.SliceThickness = 2.5
        ds.Rows = 64
        ds.Columns = 64
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 1
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        # Add minimal pixel data
        ds.PixelData = np.zeros((64, 64), dtype=np.int16).tobytes()
        return ds


@pytest.fixture
def clinical_rt_struct_dataset():
    """Load a real clinical RT Structure Set dataset from pydicom test data."""
    try:
        # Use pydicom's built-in RT Structure Set test data
        rtstruct_path = get_testdata_file("rtstruct.dcm", download=False)
        return pydicom.dcmread(rtstruct_path, force=True)
    except Exception:
        # Fallback to creating a minimal RT Structure Set dataset
        ds = Dataset()
        ds.SOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"  # RT Structure Set Storage
        ds.Modality = "RTSTRUCT"
        ds.PatientID = "TEST_PATIENT_001"
        ds.PatientName = "Test^Patient^Clinical"
        ds.StudyInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.20"
        ds.SeriesInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.23"
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.24"
        ds.StructureSetLabel = "Test Structure Set"
        ds.StructureSetDate = "20240101"
        ds.StructureSetTime = "120000"
        return ds


@pytest.fixture
def clinical_rt_dose_dataset():
    """Load a real clinical RT Dose dataset from pydicom test data."""
    try:
        # Use pydicom's built-in RT Dose test data
        rtdose_path = get_testdata_file("rtdose.dcm", download=False)
        return pydicom.dcmread(rtdose_path, force=True)
    except Exception:
        # Fallback to creating a minimal RT Dose dataset
        ds = Dataset()
        ds.SOPClassUID = "1.2.840.10008.5.1.4.1.1.481.2"  # RT Dose Storage
        ds.Modality = "RTDOSE"
        ds.PatientID = "TEST_PATIENT_001"
        ds.PatientName = "Test^Patient^Clinical"
        ds.StudyInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.20"
        ds.SeriesInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.25"
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.26"
        ds.DoseUnits = "GY"
        ds.DoseType = "PHYSICAL"
        ds.DoseSummationType = "PLAN"
        return ds


@pytest.fixture
def clinical_rt_plan_dataset():
    """Load a real clinical RT Plan dataset from pydicom test data."""
    try:
        # Use pydicom's built-in RT Plan test data
        rtplan_path = get_testdata_file("rtplan.dcm", download=False)
        return pydicom.dcmread(rtplan_path, force=True)
    except Exception:
        # Fallback to creating a minimal RT Plan dataset
        ds = Dataset()
        ds.SOPClassUID = "1.2.840.10008.5.1.4.1.1.481.5"  # RT Plan Storage
        ds.Modality = "RTPLAN"
        ds.PatientID = "TEST_PATIENT_001"
        ds.PatientName = "Test^Patient^Clinical"
        ds.StudyInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.20"
        ds.SeriesInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.27"
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15.16.17.18.19.28"
        ds.RTPlanLabel = "Test Plan"
        ds.RTPlanDate = "20240101"
        ds.RTPlanTime = "120000"
        return ds


@pytest.fixture
def temp_test_dir():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


class TestClinicalDataIntegration:
    """Test integration with real clinical DICOM data."""

    def test_load_clinical_ct_data(self, clinical_ct_dataset):
        """Test loading and processing real clinical CT data."""
        # Verify we can load the dataset
        assert clinical_ct_dataset is not None
        assert hasattr(clinical_ct_dataset, "Modality")
        assert clinical_ct_dataset.Modality == "CT"

        # Test that we can extract geometric information
        if hasattr(clinical_ct_dataset, "ImagePositionPatient"):
            position = clinical_ct_dataset.ImagePositionPatient
            assert len(position) == 3
            assert all(isinstance(p, (int, float)) for p in position)

        if hasattr(clinical_ct_dataset, "ImageOrientationPatient"):
            orientation = clinical_ct_dataset.ImageOrientationPatient
            assert len(orientation) == 6
            assert all(isinstance(o, (int, float)) for o in orientation)

    def test_load_clinical_rt_struct_data(self, clinical_rt_struct_dataset):
        """Test loading and processing real clinical RT Structure Set data."""
        # Verify we can load the dataset
        assert clinical_rt_struct_dataset is not None
        assert hasattr(clinical_rt_struct_dataset, "Modality")
        assert clinical_rt_struct_dataset.Modality == "RTSTRUCT"

        # Test basic RT Structure Set attributes
        assert hasattr(clinical_rt_struct_dataset, "SOPClassUID")
        assert clinical_rt_struct_dataset.SOPClassUID == "1.2.840.10008.5.1.4.1.1.481.3"

    def test_load_clinical_rt_dose_data(self, clinical_rt_dose_dataset):
        """Test loading and processing real clinical RT Dose data."""
        # Verify we can load the dataset
        assert clinical_rt_dose_dataset is not None
        assert hasattr(clinical_rt_dose_dataset, "Modality")
        assert clinical_rt_dose_dataset.Modality == "RTDOSE"

        # Test basic RT Dose attributes
        assert hasattr(clinical_rt_dose_dataset, "SOPClassUID")
        assert clinical_rt_dose_dataset.SOPClassUID == "1.2.840.10008.5.1.4.1.1.481.2"

    def test_load_clinical_rt_plan_data(self, clinical_rt_plan_dataset):
        """Test loading and processing real clinical RT Plan data."""
        # Verify we can load the dataset
        assert clinical_rt_plan_dataset is not None
        assert hasattr(clinical_rt_plan_dataset, "Modality")
        assert clinical_rt_plan_dataset.Modality == "RTPLAN"

        # Test basic RT Plan attributes
        assert hasattr(clinical_rt_plan_dataset, "SOPClassUID")
        assert clinical_rt_plan_dataset.SOPClassUID == "1.2.840.10008.5.1.4.1.1.481.5"


class TestClinicalDataCreation:
    """Test creating DICOM objects using real clinical data as reference."""

    def test_create_with_clinical_ct_reference(self, clinical_ct_dataset):
        """Test creating DICOM objects with real clinical CT as reference."""
        # Extract patient information from clinical data
        patient_info = {}
        if hasattr(clinical_ct_dataset, "PatientID"):
            patient_info["PatientID"] = str(clinical_ct_dataset.PatientID)
        else:
            patient_info["PatientID"] = "CLINICAL_TEST_001"

        if hasattr(clinical_ct_dataset, "PatientName"):
            patient_info["PatientName"] = str(clinical_ct_dataset.PatientName)
        else:
            patient_info["PatientName"] = "Clinical^Test^Patient"

        # Create DICOM creator with clinical reference
        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )

        # Should not raise exceptions during creation
        assert creator.reference_image is not None
        assert creator.patient_info == patient_info

        # Should pass validation
        creator.validate()
        assert creator.is_validated

    def test_geometric_validation_with_clinical_data(self, clinical_ct_dataset):
        """Test geometric validation using real clinical CT geometry."""
        # Extract geometric parameters if available
        if (
            hasattr(clinical_ct_dataset, "ImagePositionPatient")
            and hasattr(clinical_ct_dataset, "ImageOrientationPatient")
            and hasattr(clinical_ct_dataset, "PixelSpacing")
        ):

            position = clinical_ct_dataset.ImagePositionPatient
            orientation = clinical_ct_dataset.ImageOrientationPatient
            pixel_spacing = clinical_ct_dataset.PixelSpacing
            slice_thickness = getattr(clinical_ct_dataset, "SliceThickness", 2.5)

            # Create geometric parameters from clinical data
            try:
                params = GeometricParameters(
                    image_position=tuple(position),
                    pixel_spacing=tuple(pixel_spacing),
                    slice_thickness=float(slice_thickness),
                    image_orientation=tuple(orientation),
                    patient_position=getattr(
                        clinical_ct_dataset, "PatientPosition", "HFS"
                    ),
                )

                # Should not raise validation errors for real clinical data
                assert params is not None

            except Exception as e:
                # If clinical data has unusual but valid parameters,
                # we should handle gracefully
                pytest.skip(f"Clinical data has unusual geometry: {e}")

    def test_patient_validation_with_clinical_data(self, clinical_ct_dataset):
        """Test patient information validation using real clinical data."""
        # Extract patient information from clinical data
        patient_info = {}

        # Get available patient fields
        patient_fields = [
            "PatientID",
            "PatientName",
            "PatientBirthDate",
            "PatientSex",
            "PatientAge",
        ]

        for field in patient_fields:
            if hasattr(clinical_ct_dataset, field):
                value = getattr(clinical_ct_dataset, field)
                if value:  # Only include non-empty values
                    patient_info[field] = str(value)

        # Ensure we have at least PatientID for validation
        if "PatientID" not in patient_info:
            patient_info["PatientID"] = "CLINICAL_TEST_001"

        # Validate patient information
        validator = PatientInfoValidator()
        errors = validator.validate_patient_info(patient_info)

        # Real clinical data should generally pass validation
        # If there are errors, they should be documented
        if errors:
            print(f"Clinical data validation issues: {errors}")
            # For now, we'll allow this but log the issues
            assert isinstance(errors, list)


class TestMultiVendorCompatibility:
    """Test compatibility with different vendor DICOM implementations."""

    def test_vendor_specific_tags_handling(self, clinical_ct_dataset):
        """Test handling of vendor-specific private tags."""
        # Check if the clinical dataset has any private tags
        private_tags = []
        for tag in clinical_ct_dataset.keys():
            if tag.is_private:
                private_tags.append(tag)

        # Create DICOM creator with clinical reference that may have private tags
        patient_info = {"PatientID": "VENDOR_TEST_001"}
        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )

        # Should handle private tags gracefully
        creator.validate()
        assert creator.is_validated

        # Create dataset should not fail due to private tags
        dataset = creator._create_modality_specific_dataset()
        assert dataset is not None

    def test_different_transfer_syntaxes(self, clinical_ct_dataset):
        """Test handling of different transfer syntaxes."""
        # Check the transfer syntax of the clinical data
        transfer_syntax = getattr(clinical_ct_dataset, "file_meta", None)
        if transfer_syntax and hasattr(transfer_syntax, "TransferSyntaxUID"):
            print(f"Clinical data transfer syntax: {transfer_syntax.TransferSyntaxUID}")

        # Create DICOM creator and ensure it can handle the transfer syntax
        patient_info = {"PatientID": "TRANSFER_TEST_001"}
        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )

        # Should handle different transfer syntaxes
        creator.validate()
        assert creator.is_validated

    def test_character_set_handling(self, clinical_ct_dataset):
        """Test handling of different character sets."""
        # Check if the clinical dataset specifies a character set
        charset = getattr(clinical_ct_dataset, "SpecificCharacterSet", None)
        if charset:
            print(f"Clinical data character set: {charset}")

        # Create DICOM creator with clinical reference
        patient_info = {"PatientID": "CHARSET_TEST_001"}
        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )

        # Should handle character sets gracefully
        creator.validate()
        assert creator.is_validated


class TestClinicalWorkflowIntegration:
    """Test integration with typical clinical workflows."""

    def test_save_and_reload_clinical_data(self, clinical_ct_dataset, temp_test_dir):
        """Test saving DICOM created from clinical data and reloading it."""
        # Extract patient info from clinical data
        patient_info = {}
        if hasattr(clinical_ct_dataset, "PatientID"):
            patient_info["PatientID"] = str(clinical_ct_dataset.PatientID)
        else:
            patient_info["PatientID"] = "WORKFLOW_TEST_001"

        # Create DICOM creator
        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )

        # Save the created DICOM
        output_path = temp_test_dir / "clinical_test.dcm"
        saved_path = creator.save(output_path)

        assert saved_path.exists()
        assert saved_path.stat().st_size > 0

        # Reload and verify
        reloaded_dataset = pydicom.dcmread(saved_path)
        assert reloaded_dataset.PatientID == patient_info["PatientID"]
        assert reloaded_dataset.Modality == "CT"

    def test_clinical_data_anonymization_workflow(
        self, clinical_ct_dataset, temp_test_dir
    ):
        """Test anonymization workflow with clinical data."""
        # Create DICOM creator with clinical reference
        original_patient_info = {}
        if hasattr(clinical_ct_dataset, "PatientID"):
            original_patient_info["PatientID"] = str(clinical_ct_dataset.PatientID)
        else:
            original_patient_info["PatientID"] = "ANON_TEST_001"

        creator = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=original_patient_info
        )

        # Save with anonymization
        output_path = temp_test_dir / "anonymized_clinical.dcm"
        saved_path = creator.save(output_path, anonymize=True)

        assert saved_path.exists()

        # Verify anonymization worked
        anonymized_dataset = pydicom.dcmread(saved_path)
        # Patient ID should be anonymized (different from original)
        assert anonymized_dataset.PatientID != original_patient_info["PatientID"]
        # But modality should be preserved
        assert anonymized_dataset.Modality == "CT"

    def test_uid_consistency_across_clinical_workflow(self, clinical_ct_dataset):
        """Test UID consistency in clinical workflow."""
        # Create multiple DICOM objects that should reference each other
        patient_info = {"PatientID": "UID_TEST_001"}

        # Create first object
        creator1 = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )
        dataset1 = creator1._create_modality_specific_dataset()

        # Create second object with same patient
        creator2 = TestBaseDicomCreator(
            reference_image=clinical_ct_dataset, patient_info=patient_info
        )
        dataset2 = creator2._create_modality_specific_dataset()

        # Should have same Study UID if from same study
        # (This depends on implementation - for now just verify UIDs are valid)
        assert hasattr(dataset1, "StudyInstanceUID")
        assert hasattr(dataset2, "StudyInstanceUID")
        assert len(dataset1.StudyInstanceUID) > 0
        assert len(dataset2.StudyInstanceUID) > 0

        # SOPInstanceUIDs should be different
        assert dataset1.SOPInstanceUID != dataset2.SOPInstanceUID
