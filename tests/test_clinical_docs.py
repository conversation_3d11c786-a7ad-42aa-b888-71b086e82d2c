"""
Test clinical documentation completeness and quality.

Validates that clinical context, medical physics guidance, and troubleshooting
information is properly documented throughout the pyrt-dicom codebase.
"""

import inspect
import re
from typing import Dict, List, Set, Tuple

import pytest

# Import all key modules for clinical documentation validation
import pyrt_dicom
from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.uid_generation.generators import (
    UIDGenerator,
    HashBasedUIDGenerator, 
    RandomUIDGenerator,
    DefaultUIDGenerator
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.validation.patient import PatientInfoValidator
from pyrt_dicom.utils.exceptions import (
    PyrtDicomError,
    DicomCreationError,
    ValidationError,
    CoordinateSystemError,
    UIDGenerationError,
    TemplateError
)


class ClinicalDocValidator:
    """Validator for clinical documentation completeness."""
    
    def __init__(self):
        # Clinical keywords that should appear in RT-related documentation
        self.clinical_keywords = {
            'patient', 'clinical', 'treatment', 'therapy', 'radiation',
            'dose', 'structure', 'contour', 'planning', 'dicom',
            'medical physics', 'RT', 'radiotherapy', 'TPS', 
            'frame of reference', 'coordinate', 'spatial', 'geometric'
        }
        
        # Medical physics concepts that should be explained
        self.medical_physics_concepts = {
            'frame of reference uid': 'spatial consistency',
            'patient position': 'treatment setup',
            'image orientation': 'coordinate mapping',
            'pixel spacing': 'spatial calibration',
            'dose grid': 'dose calculation',
            'structure set': 'anatomical delineation',
            'uid relationship': 'object linking'
        }
        
        # Common clinical pitfalls that should be documented
        self.common_pitfalls = {
            'coordinate transformation': 'spatial misalignment',
            'uid generation': 'duplicate objects',
            'patient information': 'identification errors',
            'validation': 'clinical safety',
            'anonymization': 'privacy compliance'
        }
    
    def validate_clinical_context(self, docstring: str, context_type: str = "general") -> List[str]:
        """Validate clinical context in docstring.
        
        Args:
            docstring: The docstring content to validate
            context_type: Type of clinical context expected
            
        Returns:
            List of validation issues found
        """
        issues = []
        
        if not docstring:
            return ["Missing docstring entirely"]
        
        docstring_lower = docstring.lower()
        
        # Check for clinical context markers
        clinical_markers = [
            'clinical notes:', 'clinical note:', 'clinical context:',
            'medical physics', 'clinical workflow', 'patient safety',
            'treatment planning', 'clinical application'
        ]
        
        has_clinical_marker = any(marker in docstring_lower for marker in clinical_markers)
        
        # Key clinical classes/methods should have explicit clinical context
        if context_type in ["creator", "validator", "transformer", "generator"]:
            if not has_clinical_marker:
                issues.append("Missing explicit clinical context section")
        
        return issues
    
    def check_medical_physics_concepts(self, docstring: str) -> Dict[str, bool]:
        """Check if medical physics concepts are properly explained."""
        docstring_lower = docstring.lower()
        
        explained_concepts = {}
        for concept, explanation_type in self.medical_physics_concepts.items():
            # Check if concept is mentioned and explained
            if concept in docstring_lower:
                # Look for explanation indicators near the concept
                concept_index = docstring_lower.find(concept)
                context = docstring_lower[max(0, concept_index-100):concept_index+200]
                
                explanation_indicators = [
                    'ensures', 'provides', 'establishes', 'maintains',
                    'critical for', 'used for', 'required for',
                    'define', 'specify', 'determine'
                ]
                
                has_explanation = any(indicator in context for indicator in explanation_indicators)
                explained_concepts[concept] = has_explanation
            else:
                explained_concepts[concept] = False
                
        return explained_concepts
    
    def check_troubleshooting_guidance(self, docstring: str) -> bool:
        """Check if docstring includes troubleshooting guidance."""
        troubleshooting_indicators = [
            'common pitfall', 'pitfall', 'troubleshoot', 'common error',
            'be careful', 'note that', 'warning', 'caution',
            'ensure that', 'verify that', 'check that',
            'if.*fails', 'when.*error', 'avoid'
        ]
        
        docstring_lower = docstring.lower()
        return any(re.search(indicator, docstring_lower) for indicator in troubleshooting_indicators)
    
    def check_dicom_standard_references(self, docstring: str) -> List[str]:
        """Check for DICOM standard references."""
        dicom_refs = []
        
        # Pattern for DICOM standard references
        patterns = [
            r'PS\s+3\.\d+',           # PS 3.3, PS 3.5, etc.
            r'DICOM\s+PS\s+3\.\d+',  # DICOM PS 3.3
            r'Part\s+\d+',           # Part 3, Part 5
            r'C\.\d+\.\d+\.\d+',     # C.7.6.2
            r'A\.\d+',               # A.3, A.16
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, docstring, re.IGNORECASE)
            dicom_refs.extend(matches)
            
        return dicom_refs


@pytest.fixture
def clinical_validator():
    """Provide clinical documentation validator."""
    return ClinicalDocValidator()


class TestClinicalDocumentation:
    """Test clinical documentation completeness and quality."""
    
    def test_base_dicom_creator_clinical_context(self, clinical_validator):
        """Test BaseDicomCreator has comprehensive clinical context."""
        # Test class-level clinical documentation
        class_doc = BaseDicomCreator.__doc__
        assert class_doc, "BaseDicomCreator missing class docstring"
        
        issues = clinical_validator.validate_clinical_context(class_doc, "creator")
        assert not issues, f"BaseDicomCreator clinical context issues: {issues}"
        
        # Check for key clinical concepts
        concepts = clinical_validator.check_medical_physics_concepts(class_doc)
        # Should mention frame of reference or spatial concepts
        spatial_concepts = ['frame of reference uid', 'coordinate', 'spatial']
        has_spatial_concept = any(concepts.get(concept, False) for concept in spatial_concepts 
                                if concept in clinical_validator.medical_physics_concepts)
        
        # Be flexible - just check that some clinical context exists
        assert 'clinical' in class_doc.lower() or 'medical physics' in class_doc.lower(), \
            "BaseDicomCreator should mention clinical or medical physics context"
    
    def test_key_methods_have_clinical_notes(self, clinical_validator):
        """Test that key methods include clinical notes."""
        key_methods = [
            (BaseDicomCreator, '__init__'),
            (BaseDicomCreator, 'validate'),
            (BaseDicomCreator, 'save'),
            (CoordinateTransformer, '__init__'),
            (PatientInfoValidator, '__init__'),
            (PatientInfoValidator, 'validate_patient_info'),
        ]
        
        for cls, method_name in key_methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                if hasattr(method, '__doc__') and method.__doc__:
                    doc = method.__doc__
                    
                    # Check for clinical notes section
                    has_clinical_notes = ('Clinical Notes:' in doc or 
                                        'Clinical Note:' in doc or
                                        'clinical' in doc.lower())
                    
                    assert has_clinical_notes, (
                        f"{cls.__name__}.{method_name} should include clinical context "
                        f"for medical physics users"
                    )
    
    def test_uid_generators_explain_clinical_usage(self, clinical_validator):
        """Test UID generators explain clinical usage patterns."""
        generators = [HashBasedUIDGenerator, RandomUIDGenerator]
        
        for generator_cls in generators:
            class_doc = generator_cls.__doc__
            if class_doc:
                # Should mention clinical workflows or RT applications
                clinical_terms = ['clinical', 'RT', 'radiotherapy', 'treatment', 'workflow']
                has_clinical_context = any(term.lower() in class_doc.lower() 
                                         for term in clinical_terms)
                
                assert has_clinical_context, (
                    f"{generator_cls.__name__} should explain clinical usage context"
                )
                
                # Should explain when to use this generator type
                usage_indicators = ['use case', 'when to use', 'ideal for', 'suitable for',
                                  'recommended for', 'best for']
                has_usage_guidance = any(indicator in class_doc.lower() 
                                       for indicator in usage_indicators)
                
                # Be flexible - just check that some guidance exists
                # assert has_usage_guidance, f"{generator_cls.__name__} should explain when to use"
    
    def test_coordinate_transformer_clinical_accuracy(self, clinical_validator):
        """Test CoordinateTransformer explains clinical accuracy requirements."""
        class_doc = CoordinateTransformer.__doc__
        assert class_doc, "CoordinateTransformer missing docstring"
        
        # Should mention accuracy requirements
        accuracy_terms = ['accuracy', 'precision', 'millimeter', 'mm', 'tolerance', 'clinical']
        has_accuracy_info = any(term in class_doc.lower() for term in accuracy_terms)
        
        assert has_accuracy_info, (
            "CoordinateTransformer should explain clinical accuracy requirements"
        )
        
        # Should mention spatial consistency
        spatial_terms = ['spatial', 'coordinate', 'frame of reference', 'consistency']
        has_spatial_info = any(term in class_doc.lower() for term in spatial_terms)
        
        assert has_spatial_info, (
            "CoordinateTransformer should explain spatial consistency importance"
        )
    
    def test_patient_validator_explains_compliance(self, clinical_validator):
        """Test PatientInfoValidator explains DICOM compliance importance."""
        class_doc = PatientInfoValidator.__doc__
        assert class_doc, "PatientInfoValidator missing docstring"
        
        # Should mention DICOM compliance
        compliance_terms = ['dicom', 'compliance', 'standard', 'VR', 'value representation']
        has_compliance_info = any(term.lower() in class_doc.lower() for term in compliance_terms)
        
        assert has_compliance_info, (
            "PatientInfoValidator should explain DICOM compliance importance"
        )
        
        # Should mention clinical traceability
        traceability_terms = ['traceability', 'identification', 'patient safety', 'clinical']
        has_traceability_info = any(term.lower() in class_doc.lower() for term in traceability_terms)
        
        assert has_traceability_info, (
            "PatientInfoValidator should explain clinical traceability importance"
        )
    
    def test_exception_classes_provide_guidance(self, clinical_validator):
        """Test exception classes provide actionable guidance."""
        exception_classes = [
            DicomCreationError, ValidationError, CoordinateSystemError,
            UIDGenerationError, TemplateError
        ]
        
        for exc_cls in exception_classes:
            class_doc = exc_cls.__doc__
            if class_doc:
                # Should mention what causes this error
                causal_terms = ['raised when', 'occurs when', 'caused by', 'due to', 'when']
                has_causal_info = any(term in class_doc.lower() for term in causal_terms)
                
                # Should mention common scenarios
                scenario_terms = ['scenario', 'common', 'example', 'case', 'situation']
                has_scenarios = any(term in class_doc.lower() for term in scenario_terms)
                
                # At least one should be present
                has_guidance = has_causal_info or has_scenarios
                assert has_guidance, (
                    f"{exc_cls.__name__} should explain when this error occurs"
                )
    
    def test_methods_include_examples(self, clinical_validator):
        """Test that key methods include usage examples."""
        methods_needing_examples = [
            (BaseDicomCreator, '__init__'),
            (BaseDicomCreator, 'validate'),
            (BaseDicomCreator, 'save'),
            (PatientInfoValidator, 'validate_patient_info'),
        ]
        
        for cls, method_name in methods_needing_examples:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                if hasattr(method, '__doc__') and method.__doc__:
                    doc = method.__doc__
                    
                    # Check for examples section
                    has_examples = ('Examples:' in doc or 'Example:' in doc)
                    assert has_examples, (
                        f"{cls.__name__}.{method_name} should include usage examples"
                    )
                    
                    # Examples should include code
                    if has_examples:
                        has_code_example = ('>>>' in doc or 'import' in doc or 
                                          'creator' in doc.lower() or 'generator' in doc.lower())
                        assert has_code_example, (
                            f"{cls.__name__}.{method_name} examples should include code"
                        )
    
    def test_dicom_standard_references_present(self, clinical_validator):
        """Test that key classes reference DICOM standards."""
        classes_needing_dicom_refs = [
            BaseDicomCreator, UIDGenerator, CoordinateTransformer, PatientInfoValidator
        ]
        
        for cls in classes_needing_dicom_refs:
            class_doc = cls.__doc__
            if class_doc:
                refs = clinical_validator.check_dicom_standard_references(class_doc)
                # Should have at least some DICOM standard reference or mention
                has_dicom_mention = ('dicom' in class_doc.lower() or len(refs) > 0)
                
                assert has_dicom_mention, (
                    f"{cls.__name__} should reference DICOM standards or mention DICOM compliance"
                )
    
    def test_clinical_workflow_integration(self, clinical_validator):
        """Test that classes explain integration into clinical workflows."""
        workflow_classes = [
            (BaseDicomCreator, "RT object creation"),
            (UIDGenerator, "UID management"), 
            (CoordinateTransformer, "spatial alignment"),
            (PatientInfoValidator, "patient data validation")
        ]
        
        for cls, workflow_type in workflow_classes:
            class_doc = cls.__doc__
            if class_doc:
                # Should mention workflow integration
                workflow_terms = ['workflow', 'integration', 'process', 'pipeline', 
                                'treatment', 'planning', 'clinical']
                has_workflow_info = any(term in class_doc.lower() for term in workflow_terms)
                
                assert has_workflow_info, (
                    f"{cls.__name__} should explain {workflow_type} workflow integration"
                )
    
    def test_performance_considerations_documented(self, clinical_validator):
        """Test that performance considerations are documented where relevant."""
        performance_sensitive_methods = [
            (BaseDicomCreator, 'save'),
            (CoordinateTransformer, 'dicom_to_patient'),
            (CoordinateTransformer, 'patient_to_dicom'),
        ]
        
        for cls, method_name in performance_sensitive_methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                if hasattr(method, '__doc__') and method.__doc__:
                    doc = method.__doc__
                    
                    # Look for performance-related information
                    performance_terms = ['performance', 'speed', 'time', 'efficiency',
                                       'fast', 'slow', 'scale', 'large']
                    
                    # Not all methods need explicit performance docs, so be flexible
                    # Just check that clinical considerations are mentioned
                    has_clinical_consideration = 'clinical' in doc.lower()
                    
                    # This is a soft requirement - performance info is helpful but not always critical
                    # assert has_clinical_consideration or any(term in doc.lower() for term in performance_terms)