"""
Complete workflow testing for pyrt-dicom Task 2.6.1.

This module tests complete end-to-end workflows combining CT series creation
and RT structure sets with realistic clinical data scenarios.

Tests include:
- Complete CT + Structure creation pipeline
- DICOM file compatibility validation with external tools
- Complete RT workflow scenarios (planning CT with multiple structures)
- Real clinical data integration and round-trip validation
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
import pydicom
from pydicom.dataset import Dataset

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.core.rt_struct import RTStructureSet
from pyrt_dicom.utils.contour_processing import MaskToContourConverter
from pyrt_dicom.validation.clinical import ClinicalValidator
from pyrt_dicom.validation.dicom_compliance import DicomComplianceValidator
from pyrt_dicom.utils.exceptions import ValidationError, DicomCreationError


@pytest.fixture
def temp_workflow_dir():
    """Create temporary directory for workflow testing."""
    temp_dir = tempfile.mkdtemp(prefix="pyrt_workflow_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def clinical_ct_array():
    """Create realistic clinical CT array data."""
    # Simulate a clinical head/neck CT scan - note: first dimension is number of slices
    ct_array = np.random.randint(-1000, 3000, size=(256, 256, 100), dtype=np.int16)
    
    # Add realistic HU values for different tissues
    # Air: -1000 HU
    ct_array[:50, :, :] = -1000
    
    # Soft tissue: 0-100 HU  
    ct_array[50:200, 50:200, :] = np.random.randint(0, 100, size=(150, 150, 100))
    
    # Bone: 500-3000 HU
    ct_array[100:150, 100:150, :] = np.random.randint(500, 1500, size=(50, 50, 100))
    
    return ct_array


@pytest.fixture
def clinical_structure_masks():
    """Create realistic clinical structure masks."""
    masks = {}
    
    # PTV (Planning Target Volume) - central region
    ptv_mask = np.zeros((256, 256, 100), dtype=bool)
    ptv_mask[90:170, 90:170, 30:70] = True
    masks['PTV_6000'] = ptv_mask
    
    # CTV (Clinical Target Volume) - slightly smaller than PTV
    ctv_mask = np.zeros((256, 256, 100), dtype=bool)
    ctv_mask[95:165, 95:165, 32:68] = True
    masks['CTV_6000'] = ctv_mask
    
    # GTV (Gross Target Volume) - smallest target volume
    gtv_mask = np.zeros((256, 256, 100), dtype=bool)
    gtv_mask[100:160, 100:160, 35:65] = True  
    masks['GTV_Primary'] = gtv_mask
    
    # Organs at risk
    # Spinal cord - thin vertical structure
    cord_mask = np.zeros((256, 256, 100), dtype=bool)
    cord_mask[120:140, 200:220, :] = True
    masks['SpinalCord'] = cord_mask
    
    # Heart - larger organ structure
    heart_mask = np.zeros((256, 256, 100), dtype=bool)
    heart_mask[60:120, 100:160, 60:90] = True
    masks['Heart'] = heart_mask
    
    return masks


@pytest.fixture
def clinical_geometric_params():
    """Create realistic clinical geometric parameters."""
    return {
        'pixel_spacing': [0.976562, 0.976562],  # ~1mm resolution
        'slice_thickness': 2.5,  # 2.5mm slice thickness
        'image_position': [0.0, 0.0, -125.0],  # Starting at -125mm
        'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Standard axial
        'patient_position': 'HFS'  # Head First Supine
    }


class TestCompleteWorkflowIntegration:
    """Test complete CT + Structure workflow integration."""
    
    def test_complete_ct_structure_workflow(self, clinical_ct_array, clinical_structure_masks, 
                                          clinical_geometric_params, temp_workflow_dir):
        """Test complete workflow from CT array to RT Structure Set."""
        
        # Step 1: Create CT series from array
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'WORKFLOW_001',
                'PatientName': 'Test^Workflow^Patient',
                'StudyDescription': 'CT Planning Study'
            }
        )
        
        # Validate CT series creation
        assert ct_series is not None
        ct_series.validate()
        assert ct_series.is_validated
        
        # Step 2: Save CT series
        ct_output_dir = temp_workflow_dir / "ct_series"
        ct_output_dir.mkdir()
        ct_paths = ct_series.save_series(ct_output_dir)
        
        # Verify CT files were created
        assert len(ct_paths) == clinical_ct_array.shape[2]  # One file per slice (3rd dimension)
        for ct_path in ct_paths:
            assert ct_path.exists()
            assert ct_path.stat().st_size > 0
        
        # Step 3: Create RT Structure Set from masks using saved CT as reference
        # Load one CT slice as reference for structure set
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=clinical_structure_masks,
            patient_info={
                'PatientID': 'WORKFLOW_001',  # Match CT patient ID
                'PatientName': 'Test^Workflow^Patient'
            }
        )
        
        # Validate RT Structure Set creation
        assert rt_struct is not None
        rt_struct.validate()
        assert rt_struct.is_validated
        
        # Step 4: Save RT Structure Set
        struct_path = temp_workflow_dir / "rt_struct.dcm"
        saved_struct_path = rt_struct.save(struct_path)
        
        # Verify RT Structure Set file was created
        assert saved_struct_path.exists()
        assert saved_struct_path.stat().st_size > 0
        
        # Step 5: Verify complete workflow integrity
        # Load saved structure set and verify contents
        reloaded_struct = pydicom.dcmread(saved_struct_path)
        
        # Verify DICOM compliance
        assert reloaded_struct.Modality == 'RTSTRUCT'
        assert reloaded_struct.PatientID == 'WORKFLOW_001'
        assert hasattr(reloaded_struct, 'StructureSetROISequence')
        assert len(reloaded_struct.StructureSetROISequence) == len(clinical_structure_masks)
        
        # Verify structure names are preserved
        roi_names = [roi.ROIName for roi in reloaded_struct.StructureSetROISequence]
        expected_names = list(clinical_structure_masks.keys())
        assert set(roi_names) == set(expected_names)
        
        print(f"✅ Complete workflow test passed:")
        print(f"   - Created {len(ct_paths)} CT slices")  
        print(f"   - Created RT Structure Set with {len(clinical_structure_masks)} structures")
        print(f"   - All files saved and validated successfully")
    
    def test_external_dicom_tool_compatibility(self, clinical_ct_array, clinical_geometric_params, temp_workflow_dir):
        """Test compatibility with external DICOM tools (pydicom validation)."""
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'COMPAT_001', 'PatientName': 'Compatibility^Test'}
        )
        
        # Save single CT slice for testing
        ct_output_dir = temp_workflow_dir / "compatibility_test"
        ct_output_dir.mkdir()
        ct_paths = ct_series.save_series(ct_output_dir)
        
        # Test pydicom compatibility - load and validate each file
        for i, ct_path in enumerate(ct_paths[:5]):  # Test first 5 slices
            # Test 1: Basic pydicom reading
            try:
                dataset = pydicom.dcmread(ct_path)
                assert dataset is not None
                print(f"✅ Slice {i}: pydicom read successful")
            except Exception as e:
                pytest.fail(f"pydicom failed to read CT slice {i}: {e}")
            
            # Test 2: Verify essential DICOM elements
            essential_elements = [
                'SOPClassUID', 'SOPInstanceUID', 'StudyInstanceUID', 
                'SeriesInstanceUID', 'Modality', 'PatientID'
            ]
            for element in essential_elements:
                assert hasattr(dataset, element), f"Missing essential element: {element}"
                assert getattr(dataset, element) is not None
            
            # Test 3: Verify CT-specific elements
            ct_elements = ['ImagePositionPatient', 'ImageOrientationPatient', 'PixelSpacing']
            for element in ct_elements:
                assert hasattr(dataset, element), f"Missing CT element: {element}"
            
            # Test 4: Verify pixel data integrity
            if hasattr(dataset, 'PixelData'):
                # Ensure pixel data can be accessed
                pixel_array = dataset.pixel_array
                assert pixel_array is not None
                assert pixel_array.shape == (256, 256)  # Match our test data
                
                # Verify HU values are reasonable
                assert np.min(pixel_array) >= -1024  # Standard CT minimum
                assert np.max(pixel_array) <= 3071   # Standard CT maximum
        
        print(f"✅ External tool compatibility test passed for {len(ct_paths)} CT slices")
    
    def test_multi_structure_clinical_scenario(self, clinical_ct_array, clinical_geometric_params, temp_workflow_dir):
        """Test complete RT workflow with multiple structures (typical clinical scenario)."""
        
        # Create more comprehensive structure set
        comprehensive_masks = {}
        
        # Target volumes (typical prostate case)
        for i, (name, margin) in enumerate([
            ('GTV_Prostate', 0),
            ('CTV_Prostate', 5), 
            ('CTV_SeminalVesicles', 5),
            ('PTV_7000', 10),
            ('PTV_6000', 8)
        ]):
            mask = np.zeros((256, 256, 100), dtype=bool)
            center_z, center_y, center_x = 50, 128, 128
            size = 20 + margin
            
            y_start = max(0, center_y - size//2) 
            y_end = min(256, center_y + size//2)
            x_start = max(0, center_x - size//2)
            x_end = min(256, center_x + size//2)
            z_start = max(0, center_z - size//2)
            z_end = min(100, center_z + size//2)
            
            mask[y_start:y_end, x_start:x_end, z_start:z_end] = True
            comprehensive_masks[name] = mask
        
        # Organs at risk
        oar_structures = [
            ('Rectum', (50, 100, 128)),
            ('Bladder', (50, 150, 128)), 
            ('FemoralHead_L', (50, 128, 80)),
            ('FemoralHead_R', (50, 128, 176)),
            ('Bowel', (30, 120, 120))
        ]
        
        for name, (z_center, y_center, x_center) in oar_structures:
            mask = np.zeros((256, 256, 100), dtype=bool)
            size = 15
            
            y_start = max(0, y_center - size//2)
            y_end = min(256, y_center + size//2) 
            x_start = max(0, x_center - size//2)
            x_end = min(256, x_center + size//2)
            z_start = max(0, z_center - size//2)
            z_end = min(100, z_center + size//2)
            
            mask[y_start:y_end, x_start:x_end, z_start:z_end] = True
            comprehensive_masks[name] = mask
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'CLINICAL_SCENARIO_001',
                'PatientName': 'Clinical^Scenario^Test',
                'StudyDescription': 'Prostate IMRT Planning'
            }
        )
        
        # Save CT reference
        ct_dir = temp_workflow_dir / "clinical_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create comprehensive RT Structure Set
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=comprehensive_masks,
            patient_info={
                'PatientID': 'CLINICAL_SCENARIO_001',
                'PatientName': 'Clinical^Scenario^Test'
            }
        )
        
        # Validate clinical scenario
        rt_struct.validate()
        assert rt_struct.is_validated
        
        # Save and verify
        struct_path = temp_workflow_dir / "clinical_scenario_struct.dcm"
        saved_path = rt_struct.save(struct_path)
        
        # Load and verify comprehensive structure set
        reloaded = pydicom.dcmread(saved_path)
        
        # Verify all structures were created
        assert len(reloaded.StructureSetROISequence) == len(comprehensive_masks)
        
        # Verify structure categories
        roi_names = [roi.ROIName for roi in reloaded.StructureSetROISequence]
        
        # Check for target volumes
        target_volumes = [name for name in roi_names if any(prefix in name for prefix in ['GTV', 'CTV', 'PTV'])]
        assert len(target_volumes) >= 5, f"Expected ≥5 target volumes, got {len(target_volumes)}"
        
        # Check for organs at risk  
        oars = [name for name in roi_names if name not in target_volumes]
        assert len(oars) >= 5, f"Expected ≥5 OARs, got {len(oars)}"
        
        print(f"✅ Multi-structure clinical scenario passed:")
        print(f"   - {len(target_volumes)} target volumes: {target_volumes}")
        print(f"   - {len(oars)} organs at risk: {oars}")
        print(f"   - Total structures: {len(comprehensive_masks)}")


class TestWorkflowValidation:
    """Test validation aspects of complete workflows."""
    
    def test_workflow_clinical_validation(self, clinical_ct_array, clinical_structure_masks, clinical_geometric_params):
        """Test clinical validation throughout the workflow."""
        
        # Create CT series with clinical validation
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'VALIDATION_001',
                'PatientName': 'Validation^Test^Patient'
            }
        )
        
        # Validate CT clinical parameters
        validator = ClinicalValidator()
        
        # Test pixel spacing validation
        validation_results = validator.validate_geometric_consistency([
            {
                'pixel_spacing': clinical_geometric_params['pixel_spacing'],
                'slice_thickness': clinical_geometric_params['slice_thickness']
            }
        ])
        # Check if there are any ERROR level results
        error_results = [r for r in validation_results if r.level.value in ['error', 'critical']]
        assert len(error_results) == 0, f"Geometric validation failed: {[r.message for r in error_results]}"
        
        # Test HU range validation for CT
        hu_validation = validator.validate_dose_parameters(
            dose_array=clinical_ct_array.astype(float),
            dose_units='HU'
        )
        # Check if there are any ERROR level results
        error_results = [r for r in hu_validation if r.level.value in ['error', 'critical']]
        assert len(error_results) == 0, f"HU validation failed: {[r.message for r in error_results]}"
        
        print("✅ Workflow clinical validation passed")
    
    def test_workflow_dicom_compliance(self, clinical_ct_array, clinical_structure_masks, 
                                     clinical_geometric_params, temp_workflow_dir):
        """Test DICOM compliance throughout the workflow."""
        
        # Create and save CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'COMPLIANCE_001', 'PatientName': 'Compliance^Test'}
        )
        
        ct_dir = temp_workflow_dir / "compliance_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        
        # Test CT DICOM compliance
        compliance_validator = DicomComplianceValidator()
        
        # Test first CT slice
        ct_dataset = pydicom.dcmread(ct_paths[0])
        ct_compliance = compliance_validator.validate_ct_image_iod(ct_dataset)
        # Check if there are any ERROR level results
        error_results = [r for r in ct_compliance if r.level.value in ['error', 'critical']]
        assert len(error_results) == 0, f"CT compliance failed: {[r.message for r in error_results]}"
        
        # Create and test RT Structure Set compliance
        rt_struct = RTStructureSet.from_masks(
            ct_reference=ct_dataset,
            masks=clinical_structure_masks,
            patient_info={'PatientID': 'COMPLIANCE_001', 'PatientName': 'Compliance^Test'}
        )
        
        struct_path = temp_workflow_dir / "compliance_struct.dcm"
        rt_struct.save(struct_path)
        
        struct_dataset = pydicom.dcmread(struct_path)
        struct_compliance = compliance_validator.validate_rt_structure_set_iod(struct_dataset)
        # Check if there are any ERROR level results
        error_results = [r for r in struct_compliance if r.level.value in ['error', 'critical']]
        assert len(error_results) == 0, f"RT Structure compliance failed: {[r.message for r in error_results]}"
        
        print("✅ Workflow DICOM compliance validation passed")


class TestWorkflowErrorHandling:
    """Test error handling in complete workflows."""
    
    def test_workflow_with_invalid_geometric_params(self, clinical_ct_array, clinical_structure_masks):
        """Test workflow error handling with invalid geometric parameters."""
        
        # Test with invalid pixel spacing
        invalid_params = {
            'pixel_spacing': [-1.0, 2.0],  # Negative pixel spacing
            'slice_thickness': 2.5,
            'image_position': [0.0, 0.0, 0.0],
            'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            'patient_position': 'HFS'
        }
        
        with pytest.raises((ValidationError, DicomCreationError)):
            CTSeries.from_array(
                pixel_array=clinical_ct_array,
                pixel_spacing=invalid_params['pixel_spacing'],
                slice_thickness=invalid_params['slice_thickness'],
                patient_info={'PatientID': 'ERROR_001', 'PatientName': 'Error^Test'}
            )
    
    def test_workflow_with_mismatched_references(self, clinical_ct_array, clinical_structure_masks, 
                                                clinical_geometric_params, temp_workflow_dir):
        """Test workflow error handling with mismatched CT reference."""
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'MISMATCH_001', 'PatientName': 'Mismatch^Test'}
        )
        
        ct_dir = temp_workflow_dir / "mismatch_ct"  
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        
        # Create different CT with different geometry for reference
        different_ct_array = np.random.randint(-1000, 3000, size=(50, 128, 128), dtype=np.int16)
        different_params = clinical_geometric_params.copy()
        different_params['pixel_spacing'] = [2.0, 2.0]  # Different pixel spacing
        
        different_ct = CTSeries.from_array(
            pixel_array=different_ct_array,
            pixel_spacing=different_params['pixel_spacing'],
            slice_thickness=different_params['slice_thickness'],
            patient_info={'PatientID': 'DIFFERENT_001', 'PatientName': 'Different^Test'}
        )
        
        different_ct_dir = temp_workflow_dir / "different_ct"
        different_ct_dir.mkdir() 
        different_ct_paths = different_ct.save_series(different_ct_dir)
        different_ct_ref = pydicom.dcmread(different_ct_paths[0])
        
        # Try to create structures with mismatched reference - should handle gracefully
        try:
            rt_struct = RTStructureSet.from_masks(
                ct_reference=different_ct_ref,  # Different geometry
                masks=clinical_structure_masks,  # Masks based on original CT  
                patient_info={'PatientID': 'MISMATCH_001', 'PatientName': 'Mismatch^Test'}
            )
            
            # If creation succeeds, validation should catch geometric inconsistencies
            with pytest.raises(ValidationError):
                rt_struct.validate()
                
        except (ValidationError, DicomCreationError):
            # Expected - geometry mismatch should be caught during creation or validation
            pass
        
        print("✅ Workflow error handling test passed - geometric mismatches properly detected")


if __name__ == "__main__":
    # Run specific workflow tests
    pytest.main([__file__, "-v"])