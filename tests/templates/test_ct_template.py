"""
Tests for CT Image IOD Template Implementation.

Comprehensive test suite validating DICOM CT Image IOD compliance, geometric
accuracy, and clinical parameter handling according to DICOM Part 3 standards.
"""

import pytest
import numpy as np
from pydicom.dataset import Dataset
from pydicom.uid import CTImageStorage

from pyrt_dicom.templates.ct_template import CTImageTemplate
from pyrt_dicom.utils.exceptions import TemplateError


class TestCTImageTemplate:
    """Test suite for CT Image template compliance and functionality."""
    
    def test_sop_class_uid_constant(self):
        """Test that SOP Class UID matches DICOM standard."""
        assert CTImageTemplate.SOP_CLASS_UID == CTImageStorage
        assert CTImageTemplate.SOP_CLASS_UID == "1.2.840.10008.*******.1.2"
    
    def test_required_modules_list(self):
        """Test that required modules list is complete."""
        expected_modules = [
            'Patient', 'General Study', 'General Series', 'Frame of Reference',
            'General Equipment', 'General Image', 'Image Plane', 'Image Pixel',
            'CT Image', 'SOP Common'
        ]
        assert CTImageTemplate.REQUIRED_MODULES == expected_modules
    
    def test_create_basic_dataset(self):
        """Test creation of basic CT dataset with minimal parameters."""
        # Create test data
        pixel_array = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)
        pixel_spacing = (1.0, 1.0)
        slice_thickness = 2.5
        image_position = (-256.0, -256.0, 100.0)
        
        # Create dataset
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position
        )
        
        # Verify basic structure
        assert isinstance(dataset, Dataset)
        assert dataset.SOPClassUID == CTImageTemplate.SOP_CLASS_UID
        assert dataset.Modality == "CT"
        
        # Verify image dimensions
        assert dataset.Rows == 512
        assert dataset.Columns == 512
        assert len(dataset.PixelData) == pixel_array.nbytes
        
        # Verify geometric parameters
        assert dataset.PixelSpacing == [1.0, 1.0]
        assert dataset.SliceThickness == 2.5
        assert dataset.ImagePositionPatient == [-256.0, -256.0, 100.0]
        
    def test_create_dataset_with_all_parameters(self):
        """Test dataset creation with all optional parameters."""
        pixel_array = np.random.randint(-1000, 3000, (256, 256), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(0.5, 0.5),
            slice_thickness=1.25,
            image_position=(-64.0, -64.0, 50.0),
            image_orientation=(1.0, 0.0, 0.0, 0.0, 1.0, 0.0),
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            KVP=120,
            XRayTubeCurrent=300,
            ConvolutionKernel='BONE'
        )
        
        # Verify all parameters were set
        assert dataset.PixelSpacing == [0.5, 0.5]
        assert dataset.SliceThickness == 1.25
        assert dataset.ImagePositionPatient == [-64.0, -64.0, 50.0]
        assert dataset.ImageOrientationPatient == [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        assert dataset.RescaleIntercept == -1024.0
        assert dataset.RescaleSlope == 1.0
        assert dataset.RescaleType == "HU"
        assert dataset.KVP == 120
        assert dataset.XRayTubeCurrent == 300
        assert dataset.ConvolutionKernel == 'BONE'
        
    def test_pixel_representation_for_ct(self):
        """Test that pixel representation is set correctly for CT (signed)."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        # CT images should use signed representation for Hounsfield Units
        assert dataset.PixelRepresentation == 1
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        
    def test_hounsfield_unit_scaling(self):
        """Test proper Hounsfield Unit scaling parameters."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=5.0,
            image_position=(0.0, 0.0, 0.0),
            rescale_intercept=-1024.0,
            rescale_slope=1.0
        )
        
        assert dataset.RescaleIntercept == -1024.0
        assert dataset.RescaleSlope == 1.0
        assert dataset.RescaleType == "HU"
        
    def test_default_ct_parameters(self):
        """Test that default CT acquisition parameters are reasonable."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        # Check clinical defaults
        assert dataset.KVP == 120  # Typical clinical kVp
        assert dataset.XRayTubeCurrent == 200  # Reasonable mAs
        assert dataset.ExposureTime == 1000  # 1 second
        assert dataset.ConvolutionKernel == 'STANDARD'
        assert dataset.PatientPosition == 'HFS'  # Head First Supine
        
    def test_invalid_pixel_array_dimensions(self):
        """Test error handling for invalid pixel array dimensions."""
        # Test 1D array
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=np.array([1, 2, 3]),
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
                image_position=(0.0, 0.0, 0.0)
            )
        assert "2D" in str(exc_info.value)
        assert "1D" in str(exc_info.value)
        
        # Test 3D array
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=np.random.rand(64, 64, 32),
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
                image_position=(0.0, 0.0, 0.0)
            )
        assert "2D" in str(exc_info.value)
        assert "3D" in str(exc_info.value)
        
    def test_invalid_pixel_spacing(self):
        """Test error handling for invalid pixel spacing."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        # Test single value instead of tuple
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=pixel_array,
                pixel_spacing=(1.0,),  # Should be 2 elements
                slice_thickness=2.0,
                image_position=(0.0, 0.0, 0.0)
            )
        assert "2 elements" in str(exc_info.value)
        
        # Test three values
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=pixel_array,
                pixel_spacing=(1.0, 1.0, 1.0),  # Should be 2 elements
                slice_thickness=2.0,
                image_position=(0.0, 0.0, 0.0)
            )
        assert "2 elements" in str(exc_info.value)
        
    def test_invalid_image_position(self):
        """Test error handling for invalid image position."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        # Test 2D position instead of 3D
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=pixel_array,
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
                image_position=(0.0, 0.0)  # Should be 3 elements
            )
        assert "3 elements" in str(exc_info.value)
        assert "x, y, z" in str(exc_info.value)
        
    def test_invalid_image_orientation(self):
        """Test error handling for invalid image orientation."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        # Test incorrect number of direction cosines
        with pytest.raises(TemplateError) as exc_info:
            CTImageTemplate.create_dataset(
                pixel_array=pixel_array,
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
                image_position=(0.0, 0.0, 0.0),
                image_orientation=(1.0, 0.0, 0.0)  # Should be 6 elements
            )
        assert "6 elements" in str(exc_info.value)
        assert "direction cosines" in str(exc_info.value)
        
    def test_validate_compliance_valid_dataset(self):
        """Test compliance validation for valid CT dataset."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        errors = CTImageTemplate.validate_compliance(dataset)
        assert len(errors) == 0
        
    def test_validate_compliance_missing_elements(self):
        """Test compliance validation with missing required elements."""
        # Create incomplete dataset
        dataset = Dataset()
        dataset.SOPClassUID = CTImageTemplate.SOP_CLASS_UID
        dataset.Modality = "CT"
        # Missing required elements like Rows, Columns, etc.
        
        errors = CTImageTemplate.validate_compliance(dataset)
        assert len(errors) > 0
        
        required_elements = ['Rows', 'Columns', 'PixelData', 'PixelSpacing']
        for element in required_elements:
            assert any(element in error for error in errors)
            
    def test_validate_compliance_wrong_sop_class(self):
        """Test compliance validation with wrong SOP Class UID."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        # Change SOP Class UID to invalid value
        dataset.SOPClassUID = "1.2.3.4.5.6.7.8.9.0"
        
        errors = CTImageTemplate.validate_compliance(dataset)
        assert len(errors) > 0
        assert any("SOPClassUID" in error for error in errors)
        
    def test_validate_compliance_wrong_modality(self):
        """Test compliance validation with wrong modality."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        # Change modality to invalid value for CT template
        dataset.Modality = "MR"
        
        errors = CTImageTemplate.validate_compliance(dataset)
        assert len(errors) > 0
        assert any("modality" in error.lower() for error in errors)
        
    def test_image_type_consistency(self):
        """Test that ImageType is set consistently for CT images."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        assert hasattr(dataset, 'ImageType')
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "AXIAL"]
        
    def test_pixel_data_preservation(self):
        """Test that pixel data is preserved correctly in DICOM format."""
        # Create test data with known values
        pixel_array = np.arange(100, dtype=np.int16).reshape(10, 10)
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0)
        )
        
        # Verify pixel data is preserved
        assert len(dataset.PixelData) == pixel_array.nbytes
        
        # Reconstruct pixel array from DICOM data
        reconstructed = np.frombuffer(dataset.PixelData, dtype=np.int16).reshape(10, 10)
        np.testing.assert_array_equal(pixel_array, reconstructed)
        
    def test_geometric_consistency(self):
        """Test geometric parameter consistency."""
        pixel_array = np.random.randint(-1000, 3000, (256, 512), dtype=np.int16)
        pixel_spacing = (0.8, 1.2)  # Different row/column spacing
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=pixel_spacing,
            slice_thickness=3.0,
            image_position=(-102.4, -256.0, 150.0)
        )
        
        # Verify dimensions match array
        assert dataset.Rows == 256
        assert dataset.Columns == 512
        
        # Verify spacing preservation
        assert dataset.PixelSpacing == [0.8, 1.2]
        
        # Verify reconstruction diameter calculation
        expected_diameter = max(256 * 0.8, 512 * 1.2)  # max(204.8, 614.4) = 614.4
        assert dataset.ReconstructionDiameter == expected_diameter
        
    def test_custom_parameters_integration(self):
        """Test integration of custom CT parameters via kwargs."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)
        
        custom_params = {
            'KVP': 140,
            'XRayTubeCurrent': 400,
            'ExposureTime': 500,
            'ConvolutionKernel': 'LUNG',
            'PatientPosition': 'FFS',
            'InstanceNumber': 42
        }
        
        dataset = CTImageTemplate.create_dataset(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0),
            **custom_params
        )
        
        # Verify all custom parameters were applied
        for param, value in custom_params.items():
            assert hasattr(dataset, param)
            assert getattr(dataset, param) == value