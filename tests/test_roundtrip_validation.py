"""
Round-trip DICOM validation testing for pyrt-dicom.

This module tests the pyrt-dicom library's round-trip validation capabilities:
- <PERSON>reate → read → validate workflows with pydicom
- Multi-vendor DICOM viewer compatibility testing
- UID consistency across complete workflows
- Data integrity preservation through save/load cycles

These tests ensure that DICOM files created by pyrt-dicom can be successfully
read by other DICOM tools and maintain data integrity throughout the workflow.
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import pydicom
from pydicom.dataset import Dataset
from pydicom.data import get_testdata_file

from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.validation.geometric import GeometricValidator
from pyrt_dicom.validation.patient import PatientInfoValidator
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    GeometricParameters,
)
from pyrt_dicom.uid_generation.generators import (
    DefaultUIDGenerator,
    HashBased<PERSON><PERSON><PERSON>enerator,
    RandomUIDGenerator,
)
from pyrt_dicom.utils.exceptions import DicomCreationError, ValidationError


class TestBaseDicomCreator(BaseDicomCreator):
    """Concrete implementation for round-trip testing."""

    def _create_modality_specific_dataset(self):
        """Create minimal test dataset."""
        dataset = self._create_base_dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        dataset.Modality = "CT"
        return dataset

    def _validate_modality_specific(self):
        """Test-specific validation."""
        pass


@pytest.fixture
def temp_test_dir():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_patient_info():
    """Sample patient information for testing."""
    return {
        "PatientID": "ROUNDTRIP_TEST_001",
        "PatientName": "RoundTrip^Test^Patient",
        "PatientBirthDate": "19800101",
        "PatientSex": "M",
        "PatientAge": "044Y",
    }


@pytest.fixture
def reference_ct_dataset():
    """Create a reference CT dataset for testing."""
    try:
        # Try to use real pydicom test data
        ct_path = get_testdata_file("CT_small.dcm", download=False)
        return pydicom.dcmread(ct_path, force=True)
    except Exception:
        # Fallback to creating a minimal CT dataset
        ds = Dataset()
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        ds.Modality = "CT"
        ds.PatientID = "REF_CT_001"
        ds.PatientName = "Reference^CT^Patient"
        ds.StudyInstanceUID = "*******.*******.**********.***********.***********"
        ds.SeriesInstanceUID = "*******.*******.**********.***********.***********"
        ds.SOPInstanceUID = "*******.*******.**********.***********.***********"
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        ds.PixelSpacing = [1.0, 1.0]
        ds.SliceThickness = 2.5
        ds.Rows = 64
        ds.Columns = 64
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 1
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        # Add minimal pixel data
        ds.PixelData = np.zeros((64, 64), dtype=np.int16).tobytes()
        return ds


class TestBasicRoundTripValidation:
    """Test basic round-trip validation workflows."""

    def test_create_save_load_validate(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test basic create → save → load → validate workflow."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Validate before saving
        creator.validate()
        assert creator.is_validated

        # Save to file
        output_path = temp_test_dir / "roundtrip_test.dcm"
        saved_path = creator.save(output_path)

        assert saved_path.exists()
        assert saved_path.stat().st_size > 0

        # Load the saved file
        loaded_dataset = pydicom.dcmread(saved_path)

        # Verify basic attributes are preserved
        assert loaded_dataset.PatientID == sample_patient_info["PatientID"]
        assert loaded_dataset.PatientName == sample_patient_info["PatientName"]
        assert loaded_dataset.Modality == "CT"
        assert loaded_dataset.SOPClassUID == "1.2.840.10008.*******.1.2"

        # Verify UIDs are present and valid
        assert hasattr(loaded_dataset, "StudyInstanceUID")
        assert hasattr(loaded_dataset, "SeriesInstanceUID")
        assert hasattr(loaded_dataset, "SOPInstanceUID")
        assert len(loaded_dataset.StudyInstanceUID) > 0
        assert len(loaded_dataset.SeriesInstanceUID) > 0
        assert len(loaded_dataset.SOPInstanceUID) > 0

    def test_multiple_save_load_cycles(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test multiple save/load cycles preserve data integrity."""
        # Create initial DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load multiple times
        current_path = temp_test_dir / "cycle_0.dcm"
        creator.save(current_path)

        for cycle in range(1, 4):  # Test 3 cycles
            # Load previous file
            loaded_dataset = pydicom.dcmread(current_path)

            # Save to new file
            next_path = temp_test_dir / f"cycle_{cycle}.dcm"
            pydicom.dcmwrite(next_path, loaded_dataset)

            # Verify data integrity
            reloaded_dataset = pydicom.dcmread(next_path)
            assert reloaded_dataset.PatientID == sample_patient_info["PatientID"]
            assert reloaded_dataset.PatientName == sample_patient_info["PatientName"]
            assert reloaded_dataset.Modality == "CT"

            # UIDs should remain the same through cycles
            assert reloaded_dataset.StudyInstanceUID == loaded_dataset.StudyInstanceUID
            assert (
                reloaded_dataset.SeriesInstanceUID == loaded_dataset.SeriesInstanceUID
            )
            assert reloaded_dataset.SOPInstanceUID == loaded_dataset.SOPInstanceUID

            current_path = next_path

    def test_pydicom_validation_compliance(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that created files pass pydicom's internal validation."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save to file
        output_path = temp_test_dir / "validation_test.dcm"
        creator.save(output_path)

        # Load with pydicom and check for validation errors
        try:
            # Load with strict validation
            loaded_dataset = pydicom.dcmread(output_path, force=False)

            # Verify pydicom can access all standard attributes without errors
            _ = loaded_dataset.PatientID
            _ = loaded_dataset.StudyInstanceUID
            _ = loaded_dataset.SeriesInstanceUID
            _ = loaded_dataset.SOPInstanceUID
            _ = loaded_dataset.SOPClassUID
            _ = loaded_dataset.Modality

            # If we get here, pydicom validation passed
            assert True

        except Exception as e:
            pytest.fail(f"pydicom validation failed: {e}")


class TestUIDConsistencyValidation:
    """Test UID consistency across workflows."""

    def test_uid_uniqueness_across_objects(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that different objects get unique UIDs."""
        # Create multiple DICOM objects
        creators = []
        datasets = []

        for i in range(5):
            patient_info = sample_patient_info.copy()
            patient_info["PatientID"] = f"UID_TEST_{i:03d}"

            creator = TestBaseDicomCreator(
                reference_image=reference_ct_dataset, patient_info=patient_info
            )
            creators.append(creator)

            # Save and load to ensure UIDs are finalized
            output_path = temp_test_dir / f"uid_test_{i}.dcm"
            creator.save(output_path)
            dataset = pydicom.dcmread(output_path)
            datasets.append(dataset)

        # Verify all SOPInstanceUIDs are unique
        sop_uids = [ds.SOPInstanceUID for ds in datasets]
        assert len(set(sop_uids)) == len(sop_uids), "SOPInstanceUIDs should be unique"

        # Verify all SeriesInstanceUIDs are unique (since they're different series)
        series_uids = [ds.SeriesInstanceUID for ds in datasets]
        assert len(set(series_uids)) == len(
            series_uids
        ), "SeriesInstanceUIDs should be unique"

    def test_uid_generator_consistency(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test UID generator consistency across different generator types."""
        generators = [
            DefaultUIDGenerator.create_default_generator(),
            HashBasedUIDGenerator(),
            RandomUIDGenerator(),
        ]

        for i, generator in enumerate(generators):
            # Create DICOM object with specific UID generator
            patient_info = sample_patient_info.copy()
            patient_info["PatientID"] = f"GEN_TEST_{i:03d}"

            creator = TestBaseDicomCreator(
                reference_image=reference_ct_dataset,
                patient_info=patient_info,
                uid_generator=generator,
            )

            # Save and load
            output_path = temp_test_dir / f"generator_test_{i}.dcm"
            creator.save(output_path)
            dataset = pydicom.dcmread(output_path)

            # Verify UIDs are valid format
            assert len(dataset.StudyInstanceUID) > 0
            assert len(dataset.SeriesInstanceUID) > 0
            assert len(dataset.SOPInstanceUID) > 0

            # Verify UIDs follow DICOM format (numeric with dots)
            for uid in [
                dataset.StudyInstanceUID,
                dataset.SeriesInstanceUID,
                dataset.SOPInstanceUID,
            ]:
                assert all(
                    c.isdigit() or c == "." for c in uid
                ), f"Invalid UID format: {uid}"

    def test_uid_relationship_preservation(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that UID relationships are preserved through round-trip."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load
        output_path = temp_test_dir / "uid_relationship_test.dcm"
        creator.save(output_path)
        loaded_dataset = pydicom.dcmread(output_path)

        # Save again and verify UIDs remain consistent
        output_path2 = temp_test_dir / "uid_relationship_test2.dcm"
        pydicom.dcmwrite(output_path2, loaded_dataset)
        reloaded_dataset = pydicom.dcmread(output_path2)

        # Verify UID relationships are preserved through multiple save/load cycles
        assert loaded_dataset.StudyInstanceUID == reloaded_dataset.StudyInstanceUID
        assert loaded_dataset.SeriesInstanceUID == reloaded_dataset.SeriesInstanceUID
        assert loaded_dataset.SOPInstanceUID == reloaded_dataset.SOPInstanceUID


class TestDataIntegrityValidation:
    """Test data integrity preservation through round-trip workflows."""

    def test_patient_info_preservation(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that patient information is preserved exactly."""
        # Create DICOM object with comprehensive patient info
        comprehensive_patient_info = {
            "PatientID": "INTEGRITY_TEST_001",
            "PatientName": "Integrity^Test^Patient^Jr^Dr",
            "PatientBirthDate": "19750315",
            "PatientSex": "F",
            "PatientAge": "049Y",
            "PatientWeight": "65.5",
            "PatientSize": "1.65",
        }

        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset,
            patient_info=comprehensive_patient_info,
        )

        # Save and load
        output_path = temp_test_dir / "patient_integrity_test.dcm"
        creator.save(output_path)
        loaded_dataset = pydicom.dcmread(output_path)

        # Verify all patient information is preserved
        for key, expected_value in comprehensive_patient_info.items():
            if hasattr(loaded_dataset, key):
                actual_value = str(getattr(loaded_dataset, key))
                assert (
                    actual_value == expected_value
                ), f"{key}: expected {expected_value}, got {actual_value}"

    def test_geometric_data_preservation(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that geometric data is preserved with high precision."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load
        output_path = temp_test_dir / "geometric_integrity_test.dcm"
        creator.save(output_path)
        loaded_dataset = pydicom.dcmread(output_path)

        # Verify geometric data preservation
        if hasattr(reference_ct_dataset, "ImagePositionPatient") and hasattr(
            loaded_dataset, "ImagePositionPatient"
        ):
            ref_position = reference_ct_dataset.ImagePositionPatient
            loaded_position = loaded_dataset.ImagePositionPatient

            for i in range(3):
                assert (
                    abs(float(ref_position[i]) - float(loaded_position[i])) < 1e-6
                ), f"ImagePositionPatient[{i}] not preserved: {ref_position[i]} vs {loaded_position[i]}"

        if hasattr(reference_ct_dataset, "ImageOrientationPatient") and hasattr(
            loaded_dataset, "ImageOrientationPatient"
        ):
            ref_orientation = reference_ct_dataset.ImageOrientationPatient
            loaded_orientation = loaded_dataset.ImageOrientationPatient

            for i in range(6):
                assert (
                    abs(float(ref_orientation[i]) - float(loaded_orientation[i])) < 1e-6
                ), f"ImageOrientationPatient[{i}] not preserved: {ref_orientation[i]} vs {loaded_orientation[i]}"

    def test_metadata_preservation(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test that metadata is preserved through round-trip."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load
        output_path = temp_test_dir / "metadata_integrity_test.dcm"
        creator.save(output_path)
        loaded_dataset = pydicom.dcmread(output_path)

        # Verify essential metadata is present
        essential_tags = [
            "SOPClassUID",
            "SOPInstanceUID",
            "StudyInstanceUID",
            "SeriesInstanceUID",
            "Modality",
            "PatientID",
        ]

        for tag in essential_tags:
            assert hasattr(loaded_dataset, tag), f"Essential tag {tag} missing"
            assert (
                getattr(loaded_dataset, tag) is not None
            ), f"Essential tag {tag} is None"
            assert (
                len(str(getattr(loaded_dataset, tag))) > 0
            ), f"Essential tag {tag} is empty"


class TestMultiVendorCompatibility:
    """Test compatibility with different DICOM implementations."""

    def test_pydicom_strict_validation(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test strict pydicom validation compliance."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load with strict validation
        output_path = temp_test_dir / "strict_validation_test.dcm"
        creator.save(output_path)

        try:
            # Load with force=False for strict validation
            loaded_dataset = pydicom.dcmread(output_path, force=False)

            # Verify we can access all required attributes
            required_attributes = [
                "PatientID",
                "StudyInstanceUID",
                "SeriesInstanceUID",
                "SOPInstanceUID",
                "SOPClassUID",
                "Modality",
            ]

            for attr in required_attributes:
                value = getattr(loaded_dataset, attr)
                assert value is not None, f"Required attribute {attr} is None"

        except Exception as e:
            pytest.fail(f"Strict pydicom validation failed: {e}")

    def test_file_meta_information_compliance(
        self, sample_patient_info, reference_ct_dataset, temp_test_dir
    ):
        """Test DICOM file meta information compliance."""
        # Create DICOM object
        creator = TestBaseDicomCreator(
            reference_image=reference_ct_dataset, patient_info=sample_patient_info
        )

        # Save and load
        output_path = temp_test_dir / "file_meta_test.dcm"
        creator.save(output_path)
        loaded_dataset = pydicom.dcmread(output_path)

        # Verify file meta information is present and valid
        assert hasattr(loaded_dataset, "file_meta"), "File meta information missing"

        file_meta = loaded_dataset.file_meta
        required_meta_elements = [
            "MediaStorageSOPClassUID",
            "MediaStorageSOPInstanceUID",
            "TransferSyntaxUID",
            "ImplementationClassUID",
        ]

        for element in required_meta_elements:
            assert hasattr(file_meta, element), f"File meta element {element} missing"
            assert (
                getattr(file_meta, element) is not None
            ), f"File meta element {element} is None"

        # Verify consistency between file meta and dataset
        assert file_meta.MediaStorageSOPClassUID == loaded_dataset.SOPClassUID
        assert file_meta.MediaStorageSOPInstanceUID == loaded_dataset.SOPInstanceUID
