"""
Tests for DICOM UID regeneration functionality.

Tests the regeneration of DICOM UIDs while maintaining relationships.
"""

import pytest
from unittest.mock import patch, MagicMock
import pydicom
from pydicom.dataset import Dataset, FileDataset, FileMetaDataset
from pydicom.uid import generate_uid
import os

from pyrt_dicom.uid_generation.regenerator import (
    DIC<PERSON><PERSON>DRegenerator,
    UIDRegenerationConfig,
    UIDCategory,
    regenerate_dicom_files,
)
from pyrt_dicom.uid_generation.generators import RandomUIDGenerator
from pyrt_dicom.uid_generation.registry import UIDRegistry
from pyrt_dicom.utils.exceptions import UIDGenerationError


class TestUIDCategory:
    """Test the UIDCategory Flag enum."""
    
    def test_uid_category_values(self):
        """Test that UID categories have the expected values."""
        assert UIDCategory.STUDY.value == 1
        assert UIDCategory.SERIES.value == 2
        assert UIDCategory.INSTANCE.value == 4
        assert UIDCategory.FRAME_OF_REF.value == 8
        assert UIDCategory.MEDIA_STORAGE.value == 16
        assert UIDCategory.REFERENCED.value == 32
        
    def test_uid_category_combinations(self):
        """Test that UID categories can be combined."""
        combo = UIDCategory.STUDY | UIDCategory.SERIES
        assert combo & UIDCategory.STUDY
        assert combo & UIDCategory.SERIES
        assert not (combo & UIDCategory.INSTANCE)
        
    def test_from_string(self):
        """Test converting string to UIDCategory."""
        assert UIDCategory.from_string("STUDY") == UIDCategory.STUDY
        assert UIDCategory.from_string("series") == UIDCategory.SERIES
        assert UIDCategory.from_string("FrAmE_oF_rEf") == UIDCategory.FRAME_OF_REF
        
        with pytest.raises(ValueError):
            UIDCategory.from_string("INVALID_UID_TYPE")


class TestUIDRegenerationConfig:
    """Test the UID regeneration configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = UIDRegenerationConfig()
        
        # Check that the default value is UIDCategory.ALL
        assert config.regenerate == UIDCategory.ALL
        
        # Verify that all individual flags are set in the ALL value
        assert UIDCategory.STUDY in UIDCategory.ALL
        assert UIDCategory.SERIES in UIDCategory.ALL
        assert UIDCategory.INSTANCE in UIDCategory.ALL
        assert UIDCategory.FRAME_OF_REF in UIDCategory.ALL
        assert UIDCategory.MEDIA_STORAGE in UIDCategory.ALL
        assert UIDCategory.REFERENCED in UIDCategory.ALL
        
        # Check other default values
        assert config.maintain_relationships is True
        assert config.uid_mapping == {}
        # The default generator is actually a RandomUIDGenerator (the default type)
        assert config.generator is not None
        assert hasattr(config.generator, 'generate_study_instance_uid')
        
    def test_custom_generator(self):
        """Test providing a custom UID generator."""
        custom_gen = RandomUIDGenerator()
        config = UIDRegenerationConfig(generator=custom_gen)
        assert config.generator is custom_gen
        
    def test_custom_uid_mapping(self):
        """Test providing custom UID mappings."""
        # Create a sample DICOM dataset
        ds = Dataset()
        ds.StudyInstanceUID = "1.2.3"
        ds.SeriesInstanceUID = "*******"
        ds.SOPInstanceUID = "*******.1"
        ds.file_meta = FileMetaDataset()
        ds.file_meta.MediaStorageSOPInstanceUID = "*******.1"  # Should match SOPInstanceUID
        
        # Create a mapping for the UIDs
        mapping = {
            "1.2.3": "4.5.6",           # Study UID
            "*******": "4.5.6.1",       # Series UID
            "*******.1": "4.5.6.1.1"    # SOP Instance UID
        }
        config = UIDRegenerationConfig(uid_mapping=mapping)
        assert config.uid_mapping == mapping


class TestDICOMUIDRegenerator:
    """Test the DICOM UID regenerator class."""
    
    @pytest.fixture
    def sample_dataset(self):
        """Create a sample DICOM dataset for testing."""
        ds = Dataset()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()
        ds.SOPInstanceUID = generate_uid()
        ds.FrameOfReferenceUID = generate_uid()
        
        # Add file meta information
        ds.file_meta = FileMetaDataset()
        ds.file_meta.MediaStorageSOPInstanceUID = generate_uid()
        
        # Add some referenced sequences
        ref_study = Dataset()
        ref_study.ReferencedSOPInstanceUID = generate_uid()
        ds.ReferencedStudySequence = [ref_study]
        
        return ds
    
    @pytest.fixture
    def sample_datasets(self, sample_dataset):
        """Create multiple related DICOM datasets."""
        # Create a series of related datasets
        datasets = []
        for _ in range(3):
            ds = sample_dataset.copy()
            # Make SOPInstanceUID unique for each dataset
            ds.SOPInstanceUID = generate_uid()
            datasets.append(ds)
            
        return datasets
    
    def test_init_default(self):
        """Test initialization with default config."""
        regenerator = DICOMUIDRegenerator()
        assert isinstance(regenerator.config, UIDRegenerationConfig)
        assert regenerator.config.regenerate == UIDCategory.ALL
        
    def test_init_custom_config(self):
        """Test initialization with custom config."""
        config = UIDRegenerationConfig(regenerate=UIDCategory.STUDY)
        regenerator = DICOMUIDRegenerator(config)
        assert regenerator.config is config
        
    def test_regenerate_single_dataset(self, sample_dataset):
        """Test regenerating UIDs in a single dataset."""
        # Make a deep copy of the original dataset to preserve it
        import copy
        original_ds = copy.deepcopy(sample_dataset)
        
        # Get original UIDs
        original_uids = {
            'StudyInstanceUID': original_ds.StudyInstanceUID,
            'SeriesInstanceUID': original_ds.SeriesInstanceUID,
            'SOPInstanceUID': original_ds.SOPInstanceUID,
            'FrameOfReferenceUID': original_ds.FrameOfReferenceUID,
            'MediaStorageSOPInstanceUID': original_ds.file_meta.MediaStorageSOPInstanceUID,
            'ReferencedSOPInstanceUID': original_ds.ReferencedStudySequence[0].ReferencedSOPInstanceUID
        }
        
        # Create a new regenerator with a fresh UID registry
        regenerator = DICOMUIDRegenerator()
        
        # Create a new dataset to avoid modifying the original
        test_ds = copy.deepcopy(original_ds)
        result = regenerator.regenerate_datasets([test_ds])[0]
        
        # Verify all UIDs were regenerated and are different from originals
        assert result.StudyInstanceUID != original_uids['StudyInstanceUID']
        assert result.SeriesInstanceUID != original_uids['SeriesInstanceUID']
        assert result.SOPInstanceUID != original_uids['SOPInstanceUID']
        assert result.FrameOfReferenceUID != original_uids['FrameOfReferenceUID']
        assert result.file_meta.MediaStorageSOPInstanceUID != original_uids['MediaStorageSOPInstanceUID']
        
        # Referenced UIDs should now be regenerated (this was fixed)
        assert result.ReferencedStudySequence[0].ReferencedSOPInstanceUID != original_uids['ReferencedSOPInstanceUID']
        
        # Verify UIDs follow DICOM format (starts with a number and contains only dots and digits)
        import re
        uid_pattern = re.compile(r'^\d+(\.\d+)*$')
        assert uid_pattern.match(result.StudyInstanceUID)
        assert uid_pattern.match(result.SeriesInstanceUID)
        assert uid_pattern.match(result.SOPInstanceUID)
        assert uid_pattern.match(result.FrameOfReferenceUID)
        assert uid_pattern.match(result.file_meta.MediaStorageSOPInstanceUID)
        assert uid_pattern.match(result.ReferencedStudySequence[0].ReferencedSOPInstanceUID)
        # MediaStorageSOPInstanceUID should match SOPInstanceUID as per implementation
        assert result.file_meta.MediaStorageSOPInstanceUID == result.SOPInstanceUID
        
    def test_regenerate_specific_uids(self, sample_dataset):
        """Test regenerating only specific UID types."""
        original_uids = {
            'StudyInstanceUID': sample_dataset.StudyInstanceUID,
            'SeriesInstanceUID': sample_dataset.SeriesInstanceUID,
            'SOPInstanceUID': sample_dataset.SOPInstanceUID,
        }
        
        # Only regenerate Study and Series UIDs
        config = UIDRegenerationConfig(regenerate=UIDCategory.STUDY | UIDCategory.SERIES)
        regenerator = DICOMUIDRegenerator(config)
        result = regenerator.regenerate_datasets([sample_dataset])[0]
        
        # Verify only specified UIDs were regenerated
        assert result.StudyInstanceUID != original_uids['StudyInstanceUID']
        assert result.SeriesInstanceUID != original_uids['SeriesInstanceUID']
        assert result.SOPInstanceUID == original_uids['SOPInstanceUID']  # Should not change
        
    def test_regenerate_multiple_datasets(self, sample_datasets):
        """Test regenerating UIDs across multiple datasets."""
        # Get original UID relationships
        original_study_uid = sample_datasets[0].StudyInstanceUID
        original_series_uid = sample_datasets[0].SeriesInstanceUID
        
        # All datasets should have the same Study and Series UIDs initially
        for ds in sample_datasets[1:]:
            assert ds.StudyInstanceUID == original_study_uid
            assert ds.SeriesInstanceUID == original_series_uid
        
        # Regenerate UIDs
        regenerator = DICOMUIDRegenerator()
        results = regenerator.regenerate_datasets(sample_datasets)
        
        # Verify relationships are maintained
        new_study_uid = results[0].StudyInstanceUID
        new_series_uid = results[0].SeriesInstanceUID
        
        assert new_study_uid != original_study_uid
        assert new_series_uid != original_series_uid
        
        # All datasets should have the same new Study and Series UIDs
        for result in results[1:]:
            assert result.StudyInstanceUID == new_study_uid
            assert result.SeriesInstanceUID == new_series_uid
            
    def test_custom_uid_mapping(self, sample_dataset):
        """Test providing custom UID mappings."""
        # Make a deep copy of the original dataset
        import copy
        original_ds = copy.deepcopy(sample_dataset)
        
        # Create custom UID mappings for the test
        new_study_uid = "*******.5"
        new_series_uid = "*******.5.6"
        new_instance_uid = "*******.5.6.7"
        
        custom_mapping = {
            original_ds.StudyInstanceUID: new_study_uid,
            original_ds.SeriesInstanceUID: new_series_uid,
            original_ds.SOPInstanceUID: new_instance_uid
        }

        config = UIDRegenerationConfig(uid_mapping=custom_mapping)
        regenerator = DICOMUIDRegenerator(config)
        
        # Create a new dataset to avoid modifying the original
        test_ds = copy.deepcopy(original_ds)
        result = regenerator.regenerate_datasets([test_ds])[0]

        # Verify custom mappings were used
        assert result.StudyInstanceUID == new_study_uid
        assert result.SeriesInstanceUID == new_series_uid
        assert result.SOPInstanceUID == new_instance_uid
        
        # Verify the UIDs were properly set in the dataset
        assert result.StudyInstanceUID == new_study_uid
        assert result.SeriesInstanceUID == new_series_uid
        assert result.SOPInstanceUID == new_instance_uid
        
        # Verify file_meta MediaStorageSOPInstanceUID was updated to match SOPInstanceUID
        assert hasattr(result, 'file_meta')
        assert hasattr(result.file_meta, 'MediaStorageSOPInstanceUID')
        assert result.file_meta.MediaStorageSOPInstanceUID == new_instance_uid
        
        
    def test_maintain_relationships_false(self, sample_datasets):
        """Test with maintain_relationships set to False."""
        # Make deep copies of the datasets to avoid modifying the originals
        import copy
        test_datasets = [copy.deepcopy(ds) for ds in sample_datasets]
        
        # Ensure all datasets have the same study and series UIDs initially
        original_study_uid = test_datasets[0].StudyInstanceUID
        original_series_uid = test_datasets[0].SeriesInstanceUID
        
        for ds in test_datasets[1:]:
            ds.StudyInstanceUID = original_study_uid
            ds.SeriesInstanceUID = original_series_uid
        
        # Configure regenerator to not maintain relationships and only regenerate specific UIDs
        config = UIDRegenerationConfig(
            maintain_relationships=False,
            regenerate=UIDCategory.STUDY | UIDCategory.SERIES | UIDCategory.INSTANCE
        )
        regenerator = DICOMUIDRegenerator(config)

        # Regenerate the datasets
        results = regenerator.regenerate_datasets(test_datasets)

        # With maintain_relationships=False, we expect:
        # 1. All UIDs should be different from originals
        for ds in results:
            assert ds.StudyInstanceUID != original_study_uid
            assert ds.SeriesInstanceUID != original_series_uid
        
        # 2. With the current implementation, datasets with the same original UIDs
        # will still get the same new UIDs (because the mapping is shared)
        # This is actually a design limitation - maintain_relationships=False
        # doesn't generate unique UIDs per dataset when they have identical source UIDs
        study_uids = {ds.StudyInstanceUID for ds in results}
        series_uids = {ds.SeriesInstanceUID for ds in results}
        instance_uids = {ds.SOPInstanceUID for ds in results}
        
        # With the fix, maintain_relationships=False now generates unique UIDs per dataset
        # even when they have identical source UIDs
        assert len(study_uids) == len(test_datasets)  # Each dataset gets unique study UID
        assert len(series_uids) == len(test_datasets)  # Each dataset gets unique series UID
        assert len(instance_uids) == len(test_datasets)  # Each dataset gets unique instance UID


class TestRegenerateDICOMFiles:
    """Test the regenerate_dicom_files convenience function."""
    
    @pytest.fixture
    def sample_dicom_file(self, tmp_path):
        """Create a sample DICOM file for testing."""
        ds = Dataset()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()
        ds.SOPInstanceUID = generate_uid()
        
        # Add required DICOM fields
        ds.PatientName = "Test^Patient"
        ds.PatientID = "12345"
        ds.StudyDate = "20230101"
        ds.StudyTime = "120000"
        ds.Modality = "CT"
        
        # Add file meta information
        ds.file_meta = FileMetaDataset()
        ds.file_meta.MediaStorageSOPClassUID = '1.2.840.10008.5.1.4.1.1.2'  # CT Image Storage
        ds.file_meta.MediaStorageSOPInstanceUID = generate_uid()
        ds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'  # Explicit VR Little Endian
        
        # Save to temporary file
        file_path = tmp_path / "test.dcm"
        ds.save_as(file_path, write_like_original=False)
        return file_path
    
    def test_regenerate_dicom_files(self, sample_dicom_file, tmp_path):
        """Test the regenerate_dicom_files function."""
        output_dir = tmp_path / "output"
        
        # Call the function
        results = regenerate_dicom_files(
            [str(sample_dicom_file)],
            output_dir=str(output_dir)
        )
        
        # Verify results
        assert len(results) == 1
        assert os.path.exists(output_dir)
        
        # Check that output file was created with new UID as filename
        output_files = list(output_dir.glob("*.dcm"))
        assert len(output_files) == 1
        
        # Load the output file and verify UIDs were regenerated
        output_ds = pydicom.dcmread(output_files[0])
        original_ds = pydicom.dcmread(sample_dicom_file)
        
        assert output_ds.SOPInstanceUID != original_ds.SOPInstanceUID
        assert output_ds.StudyInstanceUID != original_ds.StudyInstanceUID
        
    def test_regenerate_dicom_files_no_output_dir(self, sample_dicom_file):
        """Test regenerate_dicom_files without saving to disk."""
        results = regenerate_dicom_files([str(sample_dicom_file)])
        
        # Should return datasets without saving to disk
        assert len(results) == 1
        assert isinstance(results[0], Dataset)
        
    @patch('pydicom.dcmread')
    def test_regenerate_dicom_files_error_handling(self, mock_dcmread):
        """Test error handling in regenerate_dicom_files."""
        # Mock dcmread to raise an exception
        mock_dcmread.side_effect = Exception("DICOM read error")
        
        with pytest.raises(Exception, match="DICOM read error"):
            regenerate_dicom_files(["nonexistent.dcm"])


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_empty_datasets(self):
        """Test with empty datasets list."""
        regenerator = DICOMUIDRegenerator()
        results = regenerator.regenerate_datasets([])
        assert results == []
        
    def test_missing_optional_fields(self):
        """Test with datasets missing optional UID fields."""
        # Create a minimal valid DICOM dataset with only required fields
        ds = Dataset()
        ds.PatientName = "Test^Patient"
        ds.PatientID = "12345"
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()
        ds.SOPInstanceUID = generate_uid()
        
        # Store original UIDs for comparison
        original_uids = {
            'StudyInstanceUID': ds.StudyInstanceUID,
            'SeriesInstanceUID': ds.SeriesInstanceUID,
            'SOPInstanceUID': ds.SOPInstanceUID
        }
        
        # No FrameOfReferenceUID or file_meta
        config = UIDRegenerationConfig(
            regenerate=UIDCategory.STUDY | UIDCategory.SERIES | UIDCategory.INSTANCE
        )
        regenerator = DICOMUIDRegenerator(config)
        
        # Make a copy of the dataset to avoid modifying the original
        import copy
        test_ds = copy.deepcopy(ds)
        
        # This should not raise any exceptions
        results = regenerator.regenerate_datasets([test_ds])
        
        # Should return the same number of datasets
        assert len(results) == 1
        result = results[0]
        
        # Required UIDs should be regenerated
        assert result.StudyInstanceUID != original_uids['StudyInstanceUID']
        assert result.SeriesInstanceUID != original_uids['SeriesInstanceUID']
        assert result.SOPInstanceUID != original_uids['SOPInstanceUID']
        
        # Verify UIDs follow DICOM format
        import re
        uid_pattern = re.compile(r'^\d+(\.\d+)*$')
        assert uid_pattern.match(result.StudyInstanceUID)
        assert uid_pattern.match(result.SeriesInstanceUID)
        assert uid_pattern.match(result.SOPInstanceUID)
        
        # Optional fields should still be missing
        assert not hasattr(result, 'FrameOfReferenceUID')
        assert not hasattr(result, 'file_meta')
        
    def test_invalid_uid_in_mapping(self):
        """Test with invalid UID in the mapping."""
        # Test that invalid UIDs in mapping are now caught by validation
        with pytest.raises(ValueError, match="Invalid source UID format"):
            UIDRegenerationConfig(
                uid_mapping={"invalid.uid": "1.2.3"}
            )
        
        # Test that valid UIDs work fine
        sample_dataset = Dataset()
        sample_dataset.StudyInstanceUID = generate_uid()
        sample_dataset.SeriesInstanceUID = generate_uid()
        sample_dataset.SOPInstanceUID = generate_uid()
        
        # Create a config with valid UID mappings
        valid_study_uid = generate_uid()
        config = UIDRegenerationConfig(
            uid_mapping={sample_dataset.StudyInstanceUID: valid_study_uid}
        )
        
        regenerator = DICOMUIDRegenerator(config)
        results = regenerator.regenerate_datasets([sample_dataset])
        assert len(results) == 1
        assert results[0].StudyInstanceUID == valid_study_uid
