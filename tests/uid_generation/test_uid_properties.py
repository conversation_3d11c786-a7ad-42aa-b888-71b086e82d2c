"""
Property-based testing for UID uniqueness properties and format compliance.

This module uses hypothesis to test UID generation properties across large sample
sizes, format compliance across random inputs, and relationship consistency
properties for DICOM UID generation.
"""

import re
from collections import Counter
from typing import Set, List

import pytest
from hypothesis import given, strategies as st, settings, assume

from pyrt_dicom.uid_generation.generators import (
    HashBasedUIDGenerator,
    RandomUIDGenerator,
    DefaultUIDGenerator,
    UIDGenerator,
)
from pyrt_dicom.utils.exceptions import UIDGenerationError


# DICOM UID format constraints
DICOM_UID_MAX_LENGTH = 64
DICOM_UID_PATTERN = re.compile(r'^[0-9]+(\.[0-9]+)*$')


@st.composite
def valid_seed_data(draw):
    """Generate valid seed data for hash-based UID generation."""
    # Mix of string and bytes data
    if draw(st.booleans()):
        # String data
        return draw(st.text(min_size=1, max_size=1000, alphabet=st.characters(
            whitelist_categories=["Lu", "Ll", "Nd", "Zs", "Po"],
            blacklist_characters=['\x00', '\r', '\n', '\t']
        )))
    else:
        # Bytes data
        return draw(st.binary(min_size=1, max_size=1000))


@st.composite
def valid_root_uid(draw):
    """Generate valid DICOM root UIDs."""
    # Generate 3-8 numeric components
    num_components = draw(st.integers(min_value=3, max_value=8))
    components = []
    for _ in range(num_components):
        # Each component is 1-10 digits, no leading zeros (except single zero)
        is_single_zero = draw(st.integers(min_value=1, max_value=10)) == 1  # 10% chance
        if is_single_zero:
            components.append("0")
        else:
            first_digit = draw(st.integers(min_value=1, max_value=9))
            remaining_digits = draw(st.integers(min_value=0, max_value=9))
            remaining_count = draw(st.integers(min_value=0, max_value=8))
            component = str(first_digit) + str(remaining_digits) * remaining_count
            components.append(component)
    
    root_uid = ".".join(components)
    
    # Ensure it fits within length constraints (leaving room for generated suffix)
    assume(len(root_uid) < 40)
    return root_uid


class TestUIDUniquenessProperties:
    """Property-based tests for UID uniqueness guarantees."""

    @given(
        sample_size=st.integers(min_value=100, max_value=1000),
    )
    @settings(max_examples=10, deadline=10000)  # Limit examples for performance
    def test_random_uid_uniqueness_large_samples(self, sample_size):
        """
        Test UID uniqueness across large sample sizes.
        
        Property: RandomUIDGenerator should produce unique UIDs across
        large sample sizes (100-1000 UIDs) with no collisions.
        """
        generator = RandomUIDGenerator()
        generated_uids = set()
        
        for _ in range(sample_size):
            uid = generator.generate_uid()
            assert uid not in generated_uids, f"Duplicate UID generated: {uid}"
            generated_uids.add(uid)
        
        assert len(generated_uids) == sample_size, "All UIDs should be unique"

    @given(
        sample_size=st.integers(min_value=100, max_value=1000),
    )
    @settings(max_examples=10, deadline=10000)
    def test_random_study_instance_uid_uniqueness(self, sample_size):
        """
        Test Study Instance UID uniqueness across large sample sizes.
        
        Property: Study Instance UIDs should be unique across large sample
        sizes, ensuring no conflicts in clinical workflows.
        """
        generator = RandomUIDGenerator()
        study_uids = set()
        
        for _ in range(sample_size):
            uid = generator.generate_study_instance_uid()
            assert uid not in study_uids, f"Duplicate Study Instance UID: {uid}"
            study_uids.add(uid)
        
        assert len(study_uids) == sample_size, "All Study Instance UIDs should be unique"

    @given(
        sample_size=st.integers(min_value=100, max_value=1000),
    )
    @settings(max_examples=10, deadline=10000)
    def test_random_sop_instance_uid_uniqueness(self, sample_size):
        """
        Test SOP Instance UID uniqueness for large datasets.
        
        Property: SOP Instance UIDs should be unique even when generating
        large numbers (simulating multi-slice CT series or batch processing).
        """
        generator = RandomUIDGenerator()
        sop_uids = set()
        
        for _ in range(sample_size):
            uid = generator.generate_sop_instance_uid()
            assert uid not in sop_uids, f"Duplicate SOP Instance UID: {uid}"
            sop_uids.add(uid)
        
        assert len(sop_uids) == sample_size, "All SOP Instance UIDs should be unique"

    @given(
        seed_data=valid_seed_data(),
    )
    @settings(max_examples=50, deadline=5000)
    def test_hash_uid_determinism(self, seed_data):
        """
        Test hash-based UID determinism property.
        
        Property: Hash-based UID generation should be deterministic - 
        the same seed data should always produce the same UID.
        """
        generator = HashBasedUIDGenerator()
        
        # Generate UID multiple times with same seed
        uid1 = generator.generate_uid(seed_data)
        uid2 = generator.generate_uid(seed_data)
        uid3 = generator.generate_uid(seed_data)
        
        assert uid1 == uid2 == uid3, "Hash-based UIDs should be deterministic"

    @given(
        seeds=st.lists(valid_seed_data(), min_size=10, max_size=100, unique=True),
    )
    @settings(max_examples=20, deadline=10000)
    def test_hash_uid_distribution(self, seeds):
        """
        Test hash-based UID distribution property.
        
        Property: Different seed data should produce different UIDs,
        demonstrating good hash function distribution properties.
        """
        generator = HashBasedUIDGenerator()
        generated_uids = []
        
        for seed in seeds:
            uid = generator.generate_uid(seed)
            generated_uids.append(uid)
        
        # All UIDs should be unique (no hash collisions for different inputs)
        unique_uids = set(generated_uids)
        collision_rate = (len(seeds) - len(unique_uids)) / len(seeds)
        
        # Allow small collision rate for hash functions with truncated output
        # Due to UID length constraints, some collisions are expected but should be rare
        assert collision_rate < 0.15, f"Hash collision rate too high: {collision_rate:.3f}"
        
        # Most UIDs should still be unique
        assert len(unique_uids) >= len(seeds) * 0.85, "Hash distribution should minimize collisions"

    @given(
        root_uid=valid_root_uid(),
        sample_size=st.integers(min_value=50, max_value=200),
    )
    @settings(max_examples=10, deadline=10000)
    def test_custom_root_uid_uniqueness(self, root_uid, sample_size):
        """
        Test UID uniqueness with custom root UIDs.
        
        Property: UID uniqueness should be maintained regardless of
        the root UID used, ensuring no conflicts across organizations.
        """
        generator = RandomUIDGenerator(root_uid=root_uid)
        generated_uids = set()
        
        for _ in range(sample_size):
            uid = generator.generate_uid()
            assert uid.startswith(root_uid), f"UID should start with root: {uid}"
            assert uid not in generated_uids, f"Duplicate UID with custom root: {uid}"
            generated_uids.add(uid)
        
        assert len(generated_uids) == sample_size, "Custom root UIDs should maintain uniqueness"


class TestUIDFormatComplianceProperties:
    """Property-based tests for DICOM UID format compliance."""

    @given(
        seed_data=st.one_of(valid_seed_data(), st.none()),
    )
    @settings(max_examples=100)
    def test_random_uid_format_compliance(self, seed_data):
        """
        Test DICOM format compliance for random UID generation.
        
        Property: All generated UIDs must comply with DICOM format
        requirements regardless of input parameters.
        """
        generator = RandomUIDGenerator()
        uid = generator.generate_uid(seed_data)
        
        # Length constraint
        assert len(uid) <= DICOM_UID_MAX_LENGTH, f"UID exceeds maximum length: {uid} ({len(uid)} chars)"
        
        # Format compliance (only digits and dots)
        assert DICOM_UID_PATTERN.match(uid), f"UID format invalid: {uid}"
        
        # No leading/trailing dots
        assert not uid.startswith('.'), f"UID starts with dot: {uid}"
        assert not uid.endswith('.'), f"UID ends with dot: {uid}"
        
        # No consecutive dots
        assert '..' not in uid, f"UID contains consecutive dots: {uid}"
        
        # Must start with root UID
        assert uid.startswith(generator.root_uid), f"UID doesn't start with root: {uid}"

    @given(
        seed_data=st.one_of(valid_seed_data(), st.none()),
    )
    @settings(max_examples=100)
    def test_hash_uid_format_compliance(self, seed_data):
        """
        Test DICOM format compliance for hash-based UID generation.
        
        Property: Hash-based UIDs must comply with DICOM format
        requirements across all possible seed inputs.
        """
        generator = HashBasedUIDGenerator()
        uid = generator.generate_uid(seed_data)
        
        # Length constraint
        assert len(uid) <= DICOM_UID_MAX_LENGTH, f"UID exceeds maximum length: {uid} ({len(uid)} chars)"
        
        # Format compliance
        assert DICOM_UID_PATTERN.match(uid), f"UID format invalid: {uid}"
        
        # No leading/trailing dots
        assert not uid.startswith('.'), f"UID starts with dot: {uid}"
        assert not uid.endswith('.'), f"UID ends with dot: {uid}"
        
        # No consecutive dots
        assert '..' not in uid, f"UID contains consecutive dots: {uid}"
        
        # Must start with root UID
        assert uid.startswith(generator.root_uid), f"UID doesn't start with root: {uid}"

    @given(
        root_uid=valid_root_uid(),
        seed_data=st.one_of(valid_seed_data(), st.none()),
    )
    @settings(max_examples=50)
    def test_custom_root_format_compliance(self, root_uid, seed_data):
        """
        Test format compliance with custom root UIDs.
        
        Property: Custom root UIDs should produce compliant UIDs
        while maintaining the custom root prefix.
        """
        generator = RandomUIDGenerator(root_uid=root_uid)
        uid = generator.generate_uid(seed_data)
        
        # All standard format requirements
        assert len(uid) <= DICOM_UID_MAX_LENGTH, f"UID exceeds maximum length: {uid}"
        assert DICOM_UID_PATTERN.match(uid), f"UID format invalid: {uid}"
        assert uid.startswith(root_uid), f"UID should start with custom root: {uid}"

    @given(
        uid_type=st.sampled_from(['study', 'series', 'sop', 'frame_ref']),
    )
    @settings(max_examples=100)
    def test_specialized_uid_format_compliance(self, uid_type):
        """
        Test format compliance for specialized UID generation methods.
        
        Property: All specialized UID methods (study, series, SOP, frame reference)
        should produce format-compliant UIDs.
        """
        generator = RandomUIDGenerator()
        
        if uid_type == 'study':
            uid = generator.generate_study_instance_uid()
        elif uid_type == 'series':
            uid = generator.generate_series_instance_uid()
        elif uid_type == 'sop':
            uid = generator.generate_sop_instance_uid()
        elif uid_type == 'frame_ref':
            uid = generator.generate_frame_of_reference_uid()
        else:
            pytest.fail(f"Unknown UID type: {uid_type}")
        
        # Standard format compliance
        assert len(uid) <= DICOM_UID_MAX_LENGTH, f"{uid_type} UID exceeds maximum length"
        assert DICOM_UID_PATTERN.match(uid), f"{uid_type} UID format invalid: {uid}"
        assert uid.startswith(generator.root_uid), f"{uid_type} UID missing root prefix"


class TestUIDRelationshipConsistency:
    """Property-based tests for UID relationship consistency."""

    @given(
        study_seeds=st.lists(valid_seed_data(), min_size=2, max_size=10, unique=True),
    )
    @settings(max_examples=20, deadline=10000)
    def test_study_series_hierarchy_consistency(self, study_seeds):
        """
        Test UID hierarchy consistency in clinical workflows.
        
        Property: Within each study, all series UIDs should be unique,
        but studies should have independent UID spaces.
        """
        hash_gen = HashBasedUIDGenerator()
        
        study_data = {}
        all_series_uids = []
        
        for study_seed in study_seeds:
            # Generate study UID
            study_uid = hash_gen.generate_study_instance_uid(study_seed)
            
            # Generate multiple series UIDs for this study
            series_uids = []
            for modality in ['CT', 'RTSTRUCT', 'RTDOSE', 'RTPLAN']:
                series_seed = f"{study_seed}_{modality}_SERIES"
                series_uid = hash_gen.generate_series_instance_uid(series_seed)
                series_uids.append(series_uid)
                all_series_uids.append(series_uid)
            
            study_data[study_uid] = series_uids
        
        # All study UIDs should be unique
        study_uids = list(study_data.keys())
        assert len(study_uids) == len(set(study_uids)), "Study UIDs should be unique"
        
        # Within each study, series UIDs should be unique
        for study_uid, series_uids in study_data.items():
            assert len(series_uids) == len(set(series_uids)), f"Series UIDs should be unique within study {study_uid}"
        
        # Globally, all series UIDs should be unique
        assert len(all_series_uids) == len(set(all_series_uids)), "All series UIDs should be globally unique"

    @given(
        base_seed=valid_seed_data(),
        instance_count=st.integers(min_value=50, max_value=200),
    )
    @settings(max_examples=10, deadline=10000)
    def test_multi_instance_consistency(self, base_seed, instance_count):
        """
        Test consistency for multi-instance series (like CT slices).
        
        Property: Generating many instance UIDs for a single series should
        produce unique UIDs with consistent prefixes and format.
        """
        hash_gen = HashBasedUIDGenerator()
        
        # Generate series UID
        series_uid = hash_gen.generate_series_instance_uid(base_seed)
        
        # Generate multiple instance UIDs for this series
        instance_uids = []
        for i in range(instance_count):
            instance_seed = f"{base_seed}_INSTANCE_{i:03d}"
            instance_uid = hash_gen.generate_sop_instance_uid(instance_seed)
            instance_uids.append(instance_uid)
        
        # All instance UIDs should be unique
        assert len(instance_uids) == len(set(instance_uids)), "Instance UIDs should be unique"
        
        # All should have the same root
        root_uid = hash_gen.root_uid
        for uid in instance_uids:
            assert uid.startswith(root_uid), f"Instance UID should start with root: {uid}"
        
        # Series UID should also share the root but be different
        assert series_uid.startswith(root_uid), "Series UID should start with root"
        assert series_uid not in instance_uids, "Series UID should not match any instance UID"

    @given(
        patient_seeds=st.lists(valid_seed_data(), min_size=5, max_size=20, unique=True),
    )
    @settings(max_examples=10, deadline=15000)
    def test_multi_patient_isolation(self, patient_seeds):
        """
        Test UID isolation between multiple patients.
        
        Property: UID generation for different patients should produce
        completely isolated UID spaces with no cross-contamination.
        """
        hash_gen = HashBasedUIDGenerator()
        
        patient_uids = {}
        all_uids = []
        
        for patient_seed in patient_seeds:
            # Generate UIDs for this patient
            study_uid = hash_gen.generate_study_instance_uid(f"{patient_seed}_STUDY")
            frame_ref_uid = hash_gen.generate_frame_of_reference_uid(f"{patient_seed}_FRAME_REF")
            ct_series_uid = hash_gen.generate_series_instance_uid(f"{patient_seed}_CT_SERIES")
            struct_series_uid = hash_gen.generate_series_instance_uid(f"{patient_seed}_STRUCT_SERIES")
            
            patient_uid_set = {study_uid, frame_ref_uid, ct_series_uid, struct_series_uid}
            patient_uids[patient_seed] = patient_uid_set
            all_uids.extend(patient_uid_set)
        
        # All UIDs should be globally unique
        assert len(all_uids) == len(set(all_uids)), "Patient UIDs should not cross-contaminate"
        
        # Each patient should have unique UIDs
        for patient_seed, uid_set in patient_uids.items():
            assert len(uid_set) == 4, f"Patient {patient_seed} should have 4 unique UIDs"


class TestFactoryMethodConsistency:
    """Property-based tests for UID generator factory method consistency."""

    @given(
        root_uid=st.one_of(valid_root_uid(), st.none()),
    )
    @settings(max_examples=50)
    def test_factory_method_consistency(self, root_uid):
        """
        Test factory method consistency with direct instantiation.
        
        Property: Factory methods should produce generators that behave
        identically to direct instantiation with the same parameters.
        """
        # Direct instantiation
        if root_uid is None:
            direct_hash = HashBasedUIDGenerator()
            direct_random = RandomUIDGenerator()
        else:
            direct_hash = HashBasedUIDGenerator(root_uid=root_uid)
            direct_random = RandomUIDGenerator(root_uid=root_uid)
        
        # Factory methods
        factory_hash = DefaultUIDGenerator.create_hash_generator(root_uid)
        factory_random = DefaultUIDGenerator.create_random_generator(root_uid)
        factory_default = DefaultUIDGenerator.create_default_generator()
        
        # Test seed data
        test_seed = "test_seed_data"
        
        # Hash generators should produce identical results
        direct_hash_uid = direct_hash.generate_uid(test_seed)
        factory_hash_uid = factory_hash.generate_uid(test_seed)
        assert direct_hash_uid == factory_hash_uid, "Hash generators should be consistent"
        
        # Random generators should have the same format and root
        direct_random_uid = direct_random.generate_uid()
        factory_random_uid = factory_random.generate_uid()
        factory_default_uid = factory_default.generate_uid()
        
        # Check root UID consistency
        expected_root = root_uid if root_uid is not None else direct_random.root_uid
        assert direct_random_uid.startswith(expected_root), "Direct random should use correct root"
        assert factory_random_uid.startswith(expected_root), "Factory random should use correct root"
        assert factory_default_uid.startswith(RandomUIDGenerator().root_uid), "Factory default should use default root"


class TestUIDGeneratorEdgeCases:
    """Property-based tests for UID generator edge cases and error handling."""

    def test_invalid_root_uid_formats(self):
        """
        Test that invalid root UID formats are properly rejected.
        
        Property: UID generators should validate root UID format and
        reject invalid formats with appropriate error messages.
        """
        invalid_roots = [
            "",           # Empty string
            ".",          # Just dot
            ".123",       # Leading dot
            "123.",       # Trailing dot
            "123..456",   # Consecutive dots
            "123.abc.456", # Non-numeric components
            "123.0123.456", # Leading zeros (should be allowed actually)
        ]
        
        for invalid_root in invalid_roots:
            if invalid_root == "123.0123.456":  # This is actually valid
                continue
                
            with pytest.raises(UIDGenerationError):
                RandomUIDGenerator(root_uid=invalid_root)
            
            with pytest.raises(UIDGenerationError):
                HashBasedUIDGenerator(root_uid=invalid_root)

    @given(
        very_long_seed=st.text(min_size=1000, max_size=5000),
    )
    @settings(max_examples=10, deadline=10000)
    def test_large_seed_data_handling(self, very_long_seed):
        """
        Test handling of very large seed data.
        
        Property: UID generators should handle arbitrarily large seed data
        without performance issues or format violations.
        """
        hash_gen = HashBasedUIDGenerator()
        
        # Should handle large seed data gracefully
        uid = hash_gen.generate_uid(very_long_seed)
        
        # Result should still be format compliant
        assert len(uid) <= DICOM_UID_MAX_LENGTH, "Large seed should not produce oversized UID"
        assert DICOM_UID_PATTERN.match(uid), "Large seed should produce valid UID format"

    @given(
        binary_seed=st.binary(min_size=1, max_size=1000),
    )
    @settings(max_examples=50)
    def test_binary_seed_data_handling(self, binary_seed):
        """
        Test handling of binary seed data.
        
        Property: Hash-based generators should handle binary seed data
        correctly, producing valid UIDs regardless of byte content.
        """
        hash_gen = HashBasedUIDGenerator()
        
        # Should handle binary data without errors
        uid = hash_gen.generate_uid(binary_seed)
        
        # Result should be format compliant
        assert len(uid) <= DICOM_UID_MAX_LENGTH, "Binary seed should not produce oversized UID"
        assert DICOM_UID_PATTERN.match(uid), "Binary seed should produce valid UID format"
        
        # Should be reproducible
        uid2 = hash_gen.generate_uid(binary_seed)
        assert uid == uid2, "Binary seed should produce reproducible results"